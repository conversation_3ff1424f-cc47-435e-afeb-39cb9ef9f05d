# Component Error Fixes - Design Document

## Overview

This design addresses the systematic resolution of TypeScript errors across the PDF viewer component library. The approach focuses on fixing type definitions, ensuring interface consistency, and updating test files to match the corrected component interfaces.

## Architecture

### Error Categories and Resolution Strategy

1. **Type Interface Mismatches**: Update interface definitions to include all required properties
2. **Component Props Issues**: Ensure all required props are properly defined and passed
3. **Test Configuration Problems**: Fix test setup and mocking configurations
4. **Import/Export Inconsistencies**: Align exports with actual component implementations

### Component Hierarchy Impact

```
Core Components (High Priority)
├── PDFSimplePage - Fix TextSelection type mismatch
├── PDFViewer - Fix toast import issues
├── PDFUpload - Fix toast import issues
└── Form Components - Fix FormField interface

Navigation Components (Medium Priority)
├── PDFSidebar - Fix missing required props
├── PDFFloatingToolbar - Fix missing annotation props
└── Context Menu - Fix prop consistency

Test Files (High Priority)
├── Component Tests - Fix mock objects and required props
├── Import Tests - Remove references to non-existent exports
└── Setup Files - Fix vitest configuration
```

## Components and Interfaces

### 1. Core Type Definitions

#### FormField Interface Enhancement
```typescript
interface FormField {
  id: string;
  type: string;
  name: string;
  label?: string;
  value: any; // Add missing property
  required: boolean; // Add missing property
  readonly: boolean; // Add missing property
  position: {
    pageNumber: number;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  appearance: any; // Add missing property
  metadata: any; // Add missing property
  properties?: any;
}
```

#### Annotation Interface Consistency
```typescript
interface Annotation {
  id: string;
  type: AnnotationType;
  pageNumber: number; // Ensure consistent naming
  x: number;
  y: number;
  color: string;
  content?: string;
  timestamp: number;
  // Additional properties as needed
}
```

#### ValidationRule Interface
```typescript
interface ValidationRule {
  fieldId: string;
  type: string;
  message: string;
  severity: 'error' | 'warning'; // Add missing property
  pattern?: RegExp;
}
```

### 2. Component Props Standardization

#### PDFFormManager Props
- Make `onFormFieldsChange` and `onFormDataChange` required
- Ensure all test cases provide these props
- Update interface to reflect requirements

#### PDFFloatingToolbar Props
- Add missing annotation-related props as required
- Update all test cases to provide complete prop sets
- Ensure backward compatibility where possible

#### PDFSidebar Props
- Define all required props clearly
- Update test cases to provide complete prop objects
- Fix bookmark interface to include timestamp

### 3. Test File Corrections

#### Vitest Configuration
- Properly import `vi` from 'vitest' in test setup
- Fix global mock configurations
- Ensure test environment is properly configured

#### Mock Object Completeness
- Update all mock objects to include required properties
- Fix FormField mocks to include value, required, readonly, appearance, metadata
- Fix Annotation mocks to include all required properties
- Fix Bookmark mocks to include timestamp

## Data Models

### Updated FormField Model
```typescript
interface FormField {
  id: string;
  type: 'text' | 'email' | 'phone' | 'date' | 'checkbox' | 'radio' | 'select';
  name: string;
  label?: string;
  value: any;
  required: boolean;
  readonly: boolean;
  position: {
    pageNumber: number;
    x: number;
    y: number;
    width: number;
    height: number;
  };
  appearance: {
    fontSize?: number;
    fontFamily?: string;
    color?: string;
    backgroundColor?: string;
    border?: string;
  };
  metadata: {
    createdAt: number;
    updatedAt: number;
    version: string;
  };
  properties?: Record<string, any>;
}
```

### Updated Bookmark Model
```typescript
interface Bookmark {
  id: string;
  page: number;
  title: string;
  timestamp: number; // Ensure this is always included
}
```

## Error Handling

### Type Safety Approach
1. **Strict Type Checking**: Ensure all interfaces are complete and consistent
2. **Default Values**: Provide sensible defaults for optional properties
3. **Validation**: Add runtime validation where necessary
4. **Migration Path**: Provide backward compatibility where possible

### Test Error Resolution
1. **Mock Completeness**: Ensure all mocks match expected interfaces
2. **Required Props**: Provide all required props in test scenarios
3. **Type Assertions**: Use proper TypeScript assertions in tests
4. **Setup Configuration**: Fix global test setup and mocking

## Testing Strategy

### Phase 1: Core Type Fixes
- Fix FormField, Annotation, and ValidationRule interfaces
- Update core components to use corrected types
- Fix immediate compilation errors

### Phase 2: Component Props
- Update component prop interfaces
- Fix all component usage to provide required props
- Ensure backward compatibility

### Phase 3: Test File Updates
- Fix all test files to use correct types
- Update mock objects to match interfaces
- Fix vitest configuration and imports

### Phase 4: Import/Export Cleanup
- Remove references to non-existent exports
- Ensure all imports match actual exports
- Clean up unused or incorrect import statements

## Implementation Priority

1. **High Priority**: Core type definitions and interfaces
2. **High Priority**: Test setup and vitest configuration
3. **Medium Priority**: Component prop fixes and updates
4. **Low Priority**: Import/export cleanup and optimization

This design ensures a systematic approach to fixing all TypeScript errors while maintaining component functionality and backward compatibility where possible.