# Implementation Plan

- [x] 1. Fix core type definitions and interfaces


  - Update FormField interface to include all required properties (value, required, readonly, appearance, metadata)
  - Update Annotation interface to ensure consistent property names and required fields
  - Update ValidationRule interface to include severity property
  - Update Bookmark interface to include timestamp property
  - _Requirements: 1.2, 1.3, 1.4, 5.1, 5.2, 5.3, 5.4_




- [x] 2. Fix test setup and vitest configuration


  - Import vi properly from 'vitest' in test setup files

  - Fix global mock configurations for ResizeObserver and IntersectionObserver
  - Configure proper test environment setup
  - _Requirements: 3.1, 3.4_




- [x] 3. Fix TextSelection type consistency in PDFSimplePage



  - Align TextSelection interface between PDFSimplePage and PDFTextSelection components



  - Ensure consistent property names (startIndex, endIndex) across components
  - Fix type mismatch in onTextSelected callback
  - _Requirements: 1.1, 4.4_






- [ ] 4. Fix toast import patterns across components







  - Update PDFViewer to use consistent toast import (sonner vs useToast)
  - Update PDFUpload to use consistent toast import pattern
  - Remove commented useToast references and use sonner consistently


  - _Requirements: 4.3_

- [ ] 5. Fix PDFFormManager component props and interface


  - Make onFormFieldsChange and onFormDataChange required props
  - Update component interface to reflect required properties
  - Fix prop validation and default handling
  - _Requirements: 2.1_



- [ ] 6. Fix PDFFloatingToolbar component props
  - Add missing required props (selectedTool, onToolSelect, selectedColor, onColorChange)
  - Update component interface to include annotation-related properties
  - Ensure all required props are properly typed
  - _Requirements: 2.2_

- [ ] 7. Fix PDFSidebar component props and interface
  - Add all missing required props (activeTab, onTabChange, numPages, currentPage, etc.)
  - Update component interface to include all necessary properties
  - Fix bookmark interface usage to include timestamp
  - _Requirements: 2.3, 1.4_

- [ ] 8. Update FormField mock objects in tests
  - Add missing properties (value, required, readonly, appearance, metadata) to all FormField mocks
  - Update test cases to provide complete FormField objects
  - Ensure all form-related tests use proper interfaces
  - _Requirements: 3.2, 5.1_

- [ ] 9. Update Annotation mock objects in tests
  - Add missing properties (pageNumber, x, y, color) to all Annotation mocks
  - Update test cases to provide complete Annotation objects
  - Ensure consistent property naming across tests
  - _Requirements: 3.2, 5.2_

- [ ] 10. Update ValidationRule mock objects in tests
  - Add severity property to all ValidationRule mocks
  - Update test cases to provide complete ValidationRule objects
  - Ensure proper validation rule testing
  - _Requirements: 3.2, 5.3_

- [ ] 11. Update Bookmark mock objects in tests
  - Add timestamp property to all Bookmark mocks
  - Update test cases to provide complete Bookmark objects
  - Ensure bookmark functionality tests work properly
  - _Requirements: 3.2, 1.4_

- [ ] 12. Fix PDFFormManager test cases
  - Provide all required props (onFormFieldsChange, onFormDataChange) in test scenarios
  - Update mock objects to match expected interfaces
  - Fix prop validation tests
  - _Requirements: 3.2, 2.1_

- [ ] 13. Fix PDFFloatingToolbar test cases
  - Provide all required props in test scenarios
  - Update mock objects for annotation-related functionality
  - Remove invalid prop references (onSearch, onBookmark, onFullscreen)
  - _Requirements: 3.2, 2.2_

- [ ] 14. Fix PDFSidebar test cases
  - Provide all required props in test scenarios
  - Update mock objects to match expected interfaces
  - Remove invalid prop references (searchResults, onPageChange, width, collapsible, etc.)
  - _Requirements: 3.2, 2.3_

- [ ] 15. Fix PDFSimplePage test cases
  - Update Annotation and FormField mocks to include all required properties
  - Fix type mismatches in test scenarios
  - Ensure proper component testing
  - _Requirements: 3.2, 1.1_

- [ ] 16. Clean up import/export inconsistencies
  - Remove references to non-existent exports (PDFEnhancedPage, EnhancedFormManager, etc.)
  - Update import tests to only reference actual exports
  - Ensure all component exports match actual implementations
  - _Requirements: 4.1, 4.2_

- [ ] 17. Fix component examples and consolidated components
  - Update example components to use correct interfaces
  - Fix type mismatches in example implementations
  - Ensure examples demonstrate proper component usage
  - _Requirements: 1.1, 1.2_

- [ ] 18. Verify and test all fixes
  - Run TypeScript compilation to ensure no errors remain
  - Run test suite to verify all tests pass
  - Verify component functionality is preserved
  - _Requirements: 1.1, 2.1, 2.2, 2.3, 3.1, 3.2, 3.3, 3.4_