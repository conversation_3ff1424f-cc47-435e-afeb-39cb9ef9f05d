# Component Error Fixes - Requirements Document

## Introduction

This specification addresses the systematic fixing of TypeScript errors throughout the PDF viewer component library. The errors primarily stem from type mismatches, missing required properties, and inconsistent interfaces between components and their tests.

## Requirements

### Requirement 1: Type Interface Consistency

**User Story:** As a developer, I want all component interfaces to be consistent and properly typed, so that TypeScript compilation succeeds without errors.

#### Acceptance Criteria

1. WHEN components are imported and used THEN all TypeScript interfaces SHALL match between definitions and usage
2. WHEN form fields are passed to components THEN they SHALL include all required properties (value, required, readonly, appearance, metadata)
3. WHEN annotations are passed to components THEN they SHALL include all required properties (pageNumber, x, y, color, etc.)
4. WHEN bookmark objects are used THEN they SHALL include the timestamp property

### Requirement 2: Component Props Validation

**User Story:** As a developer, I want component props to be properly validated, so that missing required properties are caught at compile time.

#### Acceptance Criteria

1. WHEN PDFFormManager is used THEN onFormFieldsChange and onFormDataChange SHALL be required props
2. WHEN PDFFloatingToolbar is used THEN selectedTool, onToolSelect, selectedColor, onColorChange SHALL be provided
3. WHEN PDFSidebar is used THEN all required props (activeTab, onTabChange, numPages, currentPage, etc.) SHALL be provided
4. WHEN ValidationRule objects are used THEN they SHALL include the severity property

### Requirement 3: Test Setup and Mocking

**User Story:** As a developer, I want test files to properly mock dependencies and use correct types, so that tests can run without TypeScript errors.

#### Acceptance Criteria

1. WHEN test files use vi mocking THEN vi SHALL be properly imported from vitest
2. WHEN components are tested THEN all required props SHALL be provided in test scenarios
3. WHEN mock objects are created THEN they SHALL match the expected interface types
4. WHEN test setup files are used THEN they SHALL properly configure global mocks

### Requirement 4: Import/Export Consistency

**User Story:** As a developer, I want import and export statements to be consistent, so that components can be properly imported without errors.

#### Acceptance Criteria

1. WHEN components are exported from index files THEN they SHALL exist in the actual component files
2. WHEN tests reference component properties THEN those properties SHALL exist in the actual exports
3. WHEN toast notifications are used THEN they SHALL use the correct import pattern (sonner vs useToast)
4. WHEN TextSelection types are used THEN they SHALL be consistent across components

### Requirement 5: Form and Annotation Type Definitions

**User Story:** As a developer, I want form fields and annotations to have complete type definitions, so that all properties are properly typed and validated.

#### Acceptance Criteria

1. WHEN FormField objects are created THEN they SHALL include value, required, readonly, appearance, and metadata properties
2. WHEN Annotation objects are created THEN they SHALL include pageNumber, x, y, color, and other required properties
3. WHEN ValidationRule objects are created THEN they SHALL include the severity property
4. WHEN FormTemplate objects are created THEN they SHALL include description, category, usageCount, isPublic, and other required properties