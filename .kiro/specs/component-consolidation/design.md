# Design Document

## Overview

This design outlines the consolidation of PDF viewer components by integrating enhanced functionality into original components, eliminating redundancy while preserving all features and maintaining backward compatibility. The consolidation will result in a cleaner, more maintainable component architecture with unified interfaces.

## Architecture

### Component Consolidation Strategy

The consolidation follows a **merge-and-enhance** pattern where:

1. **Original components** serve as the base implementation
2. **Enhanced components** provide additional functionality to be integrated
3. **Unified components** act as the comprehensive implementation target
4. **Redundant components** are removed after successful integration

### Component Categories

#### Core Components
- **Target**: `pdf-simple-page.tsx` → Enhanced with all page functionality
- **Sources**: `pdf-enhanced-page*.tsx` files
- **Strategy**: Merge all enhanced page features into a single, configurable page component

#### Search Components  
- **Target**: `pdf-search.tsx` → Unified search implementation
- **Sources**: `pdf-search-enhanced.tsx`, `pdf-search-unified.tsx`, `pdf-search-fixed.tsx`
- **Strategy**: Integrate all search modes as configurable options

#### Form Components
- **Target**: `pdf-form-manager.tsx` → Enhanced form management
- **Sources**: `enhanced-form-manager.tsx`
- **Strategy**: Merge enhanced form field types and validation features

#### Navigation Components
- **Target**: `pdf-floating-toolbar.tsx`, `pdf-sidebar.tsx` → Optimized navigation
- **Sources**: `optimized-toolbar.tsx`, `adaptive-sidebar.tsx`, `optimized-layout.tsx`
- **Strategy**: Integrate performance optimizations and adaptive features

#### Tool Components
- **Target**: Individual tool components → Enhanced capabilities
- **Sources**: `enhanced-tools.tsx`
- **Strategy**: Distribute enhanced tool features to respective components

## Components and Interfaces

### Core Page Component Integration

```typescript
interface ConsolidatedPageProps {
  // Basic props (from pdf-simple-page)
  pageNumber: number
  scale: number
  rotation: number
  className?: string
  
  // Enhanced props (from enhanced variants)
  pdfDocument?: any
  searchText?: string
  searchOptions?: { caseSensitive: boolean; wholeWords: boolean }
  currentSearchPageIndex?: number
  searchResults?: Array<{ pageIndex: number; textItems: any[] }>
  
  // Feature toggles
  enableAnnotations?: boolean
  enableForms?: boolean
  enableTextSelection?: boolean
  enableSearch?: boolean
  enableContextMenu?: boolean
  
  // Event handlers
  onSearch?: (text: string) => void
  onBookmark?: () => void
  onTextSelected?: (selection: TextSelection | null) => void
}
```

### Search Component Integration

```typescript
interface ConsolidatedSearchProps {
  // Core search props
  searchText: string
  onSearchChange: (text: string) => void
  onClose: () => void
  
  // Enhanced search features
  pdfDocument?: any
  numPages?: number
  onPageSelect?: (page: number) => void
  onSearchResults?: (results: SearchResult[]) => void
  onCurrentSearchIndex?: (index: number) => void
  
  // Search modes and options
  variant?: 'simple' | 'enhanced' | 'unified'
  searchOptions?: { caseSensitive: boolean; wholeWords: boolean }
  onSearchOptionsChange?: (options: SearchOptions) => void
  
  // Legacy support
  onSearch?: (searchTerm: string) => void
  onNavigateResults?: (direction: "next" | "prev") => void
  onClearSearch?: () => void
  searchResults?: Array<{ pageIndex: number; textItems: any[] }>
  currentSearchIndex?: number
  isSearching?: boolean
}
```

### Form Manager Integration

```typescript
interface ConsolidatedFormProps {
  // Basic form management
  pdfDocument: any
  pageNumber?: number
  onFormDataChange?: (data: FormData) => void
  
  // Enhanced form features
  formFields?: EnhancedFormField[]
  validationRules?: ValidationRule[]
  formTemplates?: FormTemplate[]
  
  // Form modes
  mode?: 'view' | 'edit' | 'design'
  enableValidation?: boolean
  enableTemplates?: boolean
  
  // Event handlers
  onFieldAdd?: (field: EnhancedFormField) => void
  onFieldUpdate?: (id: string, field: Partial<EnhancedFormField>) => void
  onFieldDelete?: (id: string) => void
  onFormSave?: (formData: FormData) => void
  onFormLoad?: (formData: FormData) => void
}
```

### Navigation Component Integration

```typescript
interface ConsolidatedToolbarProps {
  // Basic navigation
  onPrevPage: () => void
  onNextPage: () => void
  canGoToPrev: boolean
  canGoToNext: boolean
  pageNumber: number
  numPages: number
  onPageChange: (page: number) => void
  
  // Enhanced features
  toolbarGroups?: ToolbarGroup[]
  adaptiveLayout?: boolean
  performanceMode?: boolean
  
  // Zoom and view controls
  scale: number
  onScaleChange: (scale: number) => void
  rotation: number
  onRotationChange: (rotation: number) => void
  
  // Additional actions
  onSearch?: () => void
  onBookmark?: () => void
  onDownload?: () => void
  onFullscreen?: () => void
}
```

## Data Models

### Enhanced Form Field Types

```typescript
type EnhancedFormFieldType = 
  | 'text' | 'multiline' | 'number' | 'email' | 'phone' | 'url' | 'date'
  | 'checkbox' | 'radio' | 'dropdown' | 'listbox' | 'signature'
  | 'button' | 'reset' | 'submit' | 'file' | 'image' | 'barcode'

interface EnhancedFormField {
  id: string
  type: EnhancedFormFieldType
  name: string
  label?: string
  pageNumber: number
  position: { x: number; y: number; width: number; height: number }
  properties: Record<string, any>
  validation?: ValidationRule[]
  required?: boolean
  readonly?: boolean
}
```

### Search Result Models

```typescript
interface SearchResult {
  pageIndex: number
  textItems: Array<{
    str: string
    transform: number[]
    width: number
    height: number
    itemIndex: number
    matches: RegExpMatchArray[]
  }>
}

interface SearchOptions {
  caseSensitive: boolean
  wholeWords: boolean
  regex?: boolean
  fuzzy?: boolean
  proximity?: number
}
```

### Toolbar Configuration Models

```typescript
interface ToolbarGroup {
  id: string
  priority: 'primary' | 'secondary' | 'utility'
  items: ToolbarItem[]
  collapsible?: boolean
}

interface ToolbarItem {
  id: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  action: () => void
  shortcut?: string
  disabled?: boolean
  active?: boolean
  tooltip?: string
}
```

## Error Handling

### Component Integration Errors

1. **Missing Dependencies**: Handle cases where enhanced features require additional dependencies
2. **Interface Conflicts**: Resolve prop conflicts between original and enhanced components
3. **Feature Compatibility**: Ensure enhanced features don't break existing functionality
4. **Performance Impact**: Monitor performance impact of consolidated components

### Runtime Error Handling

```typescript
// Error boundary for consolidated components
interface ComponentErrorBoundary {
  fallbackComponent: React.ComponentType
  onError: (error: Error, errorInfo: ErrorInfo) => void
  resetOnPropsChange?: boolean
}

// Graceful degradation for enhanced features
interface FeatureToggle {
  feature: string
  enabled: boolean
  fallback?: React.ComponentType
  errorHandler?: (error: Error) => void
}
```

## Testing Strategy

### Unit Testing

1. **Component Interface Testing**: Verify all props and callbacks work correctly
2. **Feature Integration Testing**: Test that enhanced features integrate properly
3. **Backward Compatibility Testing**: Ensure existing usage patterns still work
4. **Performance Testing**: Verify consolidated components don't degrade performance

### Integration Testing

1. **Component Interaction Testing**: Test how consolidated components work together
2. **Event Flow Testing**: Verify event handlers and callbacks work across components
3. **State Management Testing**: Test state synchronization between components
4. **Error Handling Testing**: Verify error boundaries and graceful degradation

### Migration Testing

1. **Import Path Testing**: Verify all import paths continue to work
2. **API Compatibility Testing**: Test that existing component APIs are preserved
3. **Feature Parity Testing**: Ensure no functionality is lost during consolidation
4. **Documentation Testing**: Verify examples and documentation remain accurate

## Implementation Phases

### Phase 1: Core Component Consolidation
- Merge enhanced page components into `pdf-simple-page.tsx`
- Update component interfaces and props
- Implement feature toggles for enhanced functionality

### Phase 2: Search Component Unification
- Integrate all search variants into `pdf-search.tsx`
- Implement search mode selection
- Preserve all search capabilities

### Phase 3: Form and Navigation Enhancement
- Merge enhanced form features into `pdf-form-manager.tsx`
- Integrate optimized navigation features
- Update toolbar and sidebar components

### Phase 4: Tool Component Enhancement
- Distribute enhanced tool features to individual components
- Update tool interfaces and capabilities
- Ensure tool integration works properly

### Phase 5: Cleanup and Documentation
- Remove redundant component files
- Update index files and exports
- Update documentation and examples
- Verify all imports work correctly

## Migration Strategy

### Backward Compatibility

1. **Preserve Existing Interfaces**: All current component props and methods remain functional
2. **Gradual Enhancement**: Enhanced features are opt-in through props or configuration
3. **Import Compatibility**: All existing import paths continue to work
4. **Default Behavior**: Components default to their original behavior unless enhanced features are explicitly enabled

### Breaking Changes (Minimal)

1. **Internal Implementation**: Some internal component structure may change
2. **Enhanced Features**: New features may require additional peer dependencies
3. **Performance Characteristics**: Consolidated components may have different performance profiles

### Migration Path

1. **Immediate**: All existing code continues to work without changes
2. **Optional**: Developers can opt into enhanced features by updating props
3. **Future**: Enhanced features become the default in future major versions