# Tech Stack & Build System

## Core Technologies

- **Framework**: Next.js 15.3.4 with App Router
- **Runtime**: React 19.0.0 with React DOM 19.0.0
- **Language**: TypeScript 5 with strict mode enabled
- **Styling**: Tailwind CSS 4 with PostCSS
- **Package Manager**: pnpm (lockfile present)

## UI Framework & Components

- **Component Library**: shadcn/ui with "new-york" style
- **UI Primitives**: Radix UI components (@radix-ui/react-*)
- **Icons**: Lucide React
- **Theming**: next-themes for dark/light mode support
- **Notifications**: Sonner for toast notifications
- **Styling Utilities**: 
  - class-variance-authority for component variants
  - clsx and tailwind-merge for conditional classes

## PDF Functionality

- **PDF Rendering**: react-pdf library for core PDF functionality

## Development Tools

- **Linting**: ESLint with Next.js and TypeScript configurations
- **Build Tool**: Turbopack (via --turbopack flag)
- **CSS Processing**: @tailwindcss/postcss
- **Animations**: tw-animate-css

## Common Commands

```bash
# Development
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint

# Alternative package managers supported
npm run dev       # Also works with npm
yarn dev          # Also works with yarn
bun dev           # Also works with bun
```

## Configuration Notes

- **TypeScript**: Configured with path aliases (@/* maps to ./src/*)
- **ESLint**: Uses flat config with Next.js core web vitals and TypeScript rules
- **Tailwind**: CSS variables enabled, neutral base color, no prefix
- **Next.js**: Minimal configuration, relies on framework defaults