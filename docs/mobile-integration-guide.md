# Mobile Integration Guide

This guide shows how to integrate the mobile optimizations into your PDF viewer application.

## 🚀 Quick Start

### 1. Basic Setup

```tsx
import React from 'react';
import { TouchInterfaceProvider } from '@/components/mobile/touch-interface-manager';
import MobilePDFViewer from '@/components/mobile/mobile-pdf-viewer';
import { LayoutProvider } from '@/components/layout/responsive-layout-manager';
import '@/styles/mobile-optimizations.css';

function App() {
  const [pdfFile, setPdfFile] = useState<File | null>(null);

  return (
    <LayoutProvider>
      <TouchInterfaceProvider>
        <div className="app-container">
          {pdfFile ? (
            <MobilePDFViewer
              file={pdfFile}
              enableGestures={true}
              enableFullscreen={true}
              showToolbar={true}
              showPageIndicator={true}
              onClose={() => setPdfFile(null)}
              onPageChange={(page) => console.log('Page changed:', page)}
              onZoomChange={(zoom) => console.log('Zoom changed:', zoom)}
            />
          ) : (
            <FileUploader onFileSelect={setPdfFile} />
          )}
        </div>
      </TouchInterfaceProvider>
    </LayoutProvider>
  );
}
```

### 2. CSS Integration

Add the mobile optimization styles to your main CSS file:

```css
/* Import mobile optimizations */
@import '@/styles/mobile-optimizations.css';

/* Your existing styles */
.app-container {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}
```

## 🎯 Advanced Configuration

### Custom Gesture Configuration

```tsx
import { GestureEngine } from '@/lib/mobile/gesture-engine';

const customGestureConfig = {
  tapTimeout: 250,
  longPressTimeout: 600,
  swipeMinDistance: 75,
  enableHapticFeedback: true,
  pinchMinScale: 0.5,
  pinchMaxScale: 3,
};

function CustomPDFViewer() {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    const gestureEngine = new GestureEngine(
      containerRef.current,
      {
        onSwipe: (event) => {
          if (event.deltaX > 50) navigateNext();
          if (event.deltaX < -50) navigatePrevious();
        },
        onPinch: (event) => {
          setZoom(currentZoom * event.scale);
        },
        onDoubleTap: () => {
          setZoom(zoom === 1 ? 2 : 1);
        },
      },
      customGestureConfig
    );

    return () => gestureEngine.destroy();
  }, []);

  return <div ref={containerRef}>{/* Your content */}</div>;
}
```

### Responsive Navigation Setup

```tsx
import AdaptiveMobileNavigation from '@/components/mobile/adaptive-mobile-navigation';

const navigationItems = [
  {
    id: 'home',
    label: 'Home',
    icon: Home,
    action: () => navigate('/'),
    category: 'primary' as const,
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    action: () => navigate('/documents'),
    category: 'primary' as const,
    badge: documentCount,
  },
  // ... more items
];

function AppWithNavigation() {
  return (
    <div className="app-layout">
      <main>{/* Your main content */}</main>
      
      <AdaptiveMobileNavigation
        items={navigationItems}
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
        showPageNavigation={true}
        showQuickActions={true}
        position="bottom"
        variant="auto"
      />
    </div>
  );
}
```

## 🛠️ Utility Functions

### Device Detection

```tsx
import { MobileUtils } from '@/lib/mobile/mobile-utils';

function ResponsiveComponent() {
  const [deviceInfo, setDeviceInfo] = useState(MobileUtils.getDeviceInfo());

  useEffect(() => {
    const handleResize = MobileUtils.debounce(() => {
      setDeviceInfo(MobileUtils.getDeviceInfo());
    }, 250);

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (deviceInfo.isMobile) {
    return <MobileLayout />;
  } else if (deviceInfo.isTablet) {
    return <TabletLayout />;
  } else {
    return <DesktopLayout />;
  }
}
```

### Performance Optimization

```tsx
import { MobileUtils } from '@/lib/mobile/mobile-utils';

function PerformanceAwareComponent() {
  const [isLowPower, setIsLowPower] = useState(false);

  useEffect(() => {
    MobileUtils.isLowPowerMode().then(setIsLowPower);
  }, []);

  const handleExpensiveOperation = MobileUtils.throttle(() => {
    if (isLowPower) {
      // Use simplified version
      performSimpleOperation();
    } else {
      // Use full-featured version
      performComplexOperation();
    }
  }, 100);

  return (
    <div>
      <button onClick={handleExpensiveOperation}>
        Process Document
      </button>
    </div>
  );
}
```

## 🎨 Styling Best Practices

### Touch-Friendly Components

```css
/* Ensure all interactive elements meet touch target requirements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Comfortable touch targets for primary actions */
.touch-target-comfortable {
  min-height: 48px;
  min-width: 48px;
}

/* Large touch targets for accessibility */
.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}
```

### Safe Area Support

```css
/* Header with safe area support */
.app-header {
  padding-top: env(safe-area-inset-top);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Footer with safe area support */
.app-footer {
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Full safe area insets */
.safe-area-inset {
  padding: env(safe-area-inset-top) env(safe-area-inset-right) 
           env(safe-area-inset-bottom) env(safe-area-inset-left);
}
```

## 📱 Platform-Specific Optimizations

### iOS Optimizations

```css
/* Prevent zoom on input focus */
@supports (-webkit-touch-callout: none) {
  input, textarea, select {
    font-size: 16px; /* Prevents zoom */
    border-radius: 0; /* Removes default styling */
  }
  
  /* Custom tap highlight */
  .touch-target {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}
```

### Android Optimizations

```css
/* Android-specific optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .android-optimized {
    /* Android-specific styles */
    touch-action: manipulation;
  }
}
```

## 🧪 Testing Integration

### Component Testing

```tsx
import { render, screen } from '@testing-library/react';
import { TouchInterfaceProvider } from '@/components/mobile/touch-interface-manager';
import MobilePDFViewer from '@/components/mobile/mobile-pdf-viewer';

function renderWithProviders(component: React.ReactElement) {
  return render(
    <TouchInterfaceProvider>
      {component}
    </TouchInterfaceProvider>
  );
}

test('mobile PDF viewer renders correctly', () => {
  const mockFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });
  
  renderWithProviders(
    <MobilePDFViewer
      file={mockFile}
      enableGestures={true}
      onClose={() => {}}
    />
  );

  expect(screen.getByTestId('mobile-pdf-viewer')).toBeInTheDocument();
});
```

### Gesture Testing

```tsx
import { fireEvent } from '@testing-library/react';

test('swipe gesture navigation', () => {
  const onPageChange = jest.fn();
  
  renderWithProviders(
    <MobilePDFViewer
      file={mockFile}
      onPageChange={onPageChange}
    />
  );

  const viewer = screen.getByTestId('mobile-pdf-viewer');
  
  // Simulate swipe gesture
  fireEvent.touchStart(viewer, {
    touches: [{ clientX: 100, clientY: 100 }]
  });
  
  fireEvent.touchMove(viewer, {
    touches: [{ clientX: 200, clientY: 100 }]
  });
  
  fireEvent.touchEnd(viewer, { touches: [] });

  expect(onPageChange).toHaveBeenCalled();
});
```

## 🚀 Deployment Considerations

### Build Configuration

```json
{
  "scripts": {
    "build:mobile": "vite build --mode mobile",
    "test:mobile": "vitest run src/test/mobile",
    "lighthouse:mobile": "lighthouse --preset=mobile --output=html"
  }
}
```

### Performance Monitoring

```tsx
import { MobileUtils } from '@/lib/mobile/mobile-utils';

// Monitor performance metrics
const metrics = MobileUtils.getPerformanceMetrics();
console.log('Device memory:', metrics.deviceMemory);
console.log('Connection type:', metrics.connection);
console.log('Hardware concurrency:', metrics.hardwareConcurrency);

// Send to analytics
analytics.track('mobile_performance', {
  deviceMemory: metrics.deviceMemory,
  connection: metrics.connection,
  batteryLevel: metrics.batteryLevel,
});
```

## 📚 Additional Resources

- [Mobile Optimizations Documentation](./mobile-optimizations.md)
- [Touch Interface API Reference](../src/components/mobile/touch-interface-manager.tsx)
- [Gesture Engine API Reference](../src/lib/mobile/gesture-engine.ts)
- [Mobile Testing Guide](../src/test/mobile/README.md)

---

*For more detailed examples and advanced configurations, see the demo components in `src/components/examples/`.*
