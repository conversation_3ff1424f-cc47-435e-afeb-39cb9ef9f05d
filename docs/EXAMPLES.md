# Usage Examples

This document provides practical examples of how to use the Cobalt PDF Viewer and Document Management features.

## Basic PDF Viewing

### Simple PDF Upload and View

```tsx
import { useState } from 'react';
import { MultiDocumentPDFViewer } from '@/components/core';

function SimplePDFViewer() {
  const [showViewer, setShowViewer] = useState(false);

  return (
    <div className="h-screen">
      {showViewer ? (
        <MultiDocumentPDFViewer
          showUploadOnEmpty={true}
          onClose={() => setShowViewer(false)}
        />
      ) : (
        <button onClick={() => setShowViewer(true)}>
          Open PDF Viewer
        </button>
      )}
    </div>
  );
}
```

### PDF Upload with Custom Metadata

```tsx
import { PDFUpload } from '@/components/core';

function CustomPDFUpload() {
  const handleFileSelect = async (file: string | File) => {
    console.log('Selected file:', file);
    // Handle file selection
  };

  const handleDocumentAdded = (documentId: string) => {
    console.log('Document added to library:', documentId);
  };

  return (
    <PDFUpload
      onFileSelect={handleFileSelect}
      addToLibrary={true}
      showMetadataForm={true}
      onDocumentAdded={handleDocumentAdded}
    />
  );
}
```

## Document Library Management

### Basic Document Library

```tsx
import { DocumentLibrary } from '@/components/library';
import { useState } from 'react';

function MyDocumentLibrary() {
  const [selectedDocument, setSelectedDocument] = useState(null);

  const handleDocumentSelect = (document) => {
    setSelectedDocument(document);
    console.log('Selected document:', document.metadata.title);
  };

  const handleDocumentOpen = (document) => {
    console.log('Opening document:', document.metadata.title);
    // Open document in viewer
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">My Document Library</h1>
      <DocumentLibrary
        onDocumentSelect={handleDocumentSelect}
        onDocumentOpen={handleDocumentOpen}
      />
      
      {selectedDocument && (
        <div className="mt-4 p-4 border rounded">
          <h3>Selected: {selectedDocument.metadata.title}</h3>
          <p>Author: {selectedDocument.metadata.author}</p>
          <p>Pages: {selectedDocument.metadata.pageCount}</p>
        </div>
      )}
    </div>
  );
}
```

### Document Library with Sidebar

```tsx
import { DocumentLibrarySidebar } from '@/components/library';
import { PDFViewer } from '@/components/core';
import { useState } from 'react';

function DocumentViewerWithSidebar() {
  const [currentDocument, setCurrentDocument] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  return (
    <div className="flex h-screen">
      {sidebarOpen && (
        <div className="w-80 border-r">
          <DocumentLibrarySidebar
            onDocumentSelect={setCurrentDocument}
            onDocumentOpen={setCurrentDocument}
            onNewDocument={() => console.log('New document requested')}
          />
        </div>
      )}
      
      <div className="flex-1">
        {currentDocument ? (
          <PDFViewer document={currentDocument} />
        ) : (
          <div className="flex items-center justify-center h-full">
            <p>Select a document from the library</p>
          </div>
        )}
      </div>
    </div>
  );
}
```

## Advanced Document Management

### Document Organization with Drag & Drop

```tsx
import { DocumentOrganizer } from '@/components/library';
import { useState, useEffect } from 'react';
import { documentLibrary } from '@/lib/document-library';

function DocumentOrganizationPage() {
  const [documents, setDocuments] = useState([]);
  const [collections, setCollections] = useState([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [docsData, collectionsData] = await Promise.all([
        documentLibrary.getAllDocuments(),
        documentLibrary.getAllCollections()
      ]);
      setDocuments(docsData);
      setCollections(collectionsData);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Organize Documents</h1>
      <DocumentOrganizer
        documents={documents}
        collections={collections}
        onDocumentsUpdate={loadData}
        onCollectionsUpdate={loadData}
      />
    </div>
  );
}
```

### Advanced Search and Filtering

```tsx
import { AdvancedFilters, DocumentLibrary } from '@/components/library';
import { useState, useEffect } from 'react';
import { documentLibrary } from '@/lib/document-library';

function AdvancedSearchPage() {
  const [documents, setDocuments] = useState([]);
  const [filteredDocuments, setFilteredDocuments] = useState([]);

  useEffect(() => {
    loadDocuments();
  }, []);

  const loadDocuments = async () => {
    const docs = await documentLibrary.getAllDocuments();
    setDocuments(docs);
    setFilteredDocuments(docs);
  };

  const handleFilterChange = async (filters) => {
    try {
      const results = await documentLibrary.searchDocuments(filters);
      setFilteredDocuments(results.documents);
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Advanced Document Search</h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <div className="lg:col-span-1">
          <AdvancedFilters
            documents={documents}
            onFilterChange={handleFilterChange}
          />
        </div>
        
        <div className="lg:col-span-3">
          <DocumentLibrary
            documents={filteredDocuments}
            onDocumentSelect={(doc) => console.log('Selected:', doc)}
            onDocumentOpen={(doc) => console.log('Opening:', doc)}
          />
        </div>
      </div>
    </div>
  );
}
```

### Bulk Document Operations

```tsx
import { useState } from 'react';
import { documentLibrary } from '@/lib/document-library';

function BulkOperationsExample() {
  const [selectedDocuments, setSelectedDocuments] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleBulkAddTags = async (tags) => {
    setIsProcessing(true);
    try {
      for (const documentId of selectedDocuments) {
        const document = await documentLibrary.getDocument(documentId);
        if (document) {
          const updatedTags = [...new Set([...document.metadata.tags, ...tags])];
          await documentLibrary.updateDocument(documentId, {
            metadata: {
              ...document.metadata,
              tags: updatedTags
            }
          });
        }
      }
      console.log(`Added tags to ${selectedDocuments.length} documents`);
    } catch (error) {
      console.error('Bulk operation failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleBulkFavorite = async (isFavorite) => {
    setIsProcessing(true);
    try {
      for (const documentId of selectedDocuments) {
        const document = await documentLibrary.getDocument(documentId);
        if (document) {
          await documentLibrary.updateDocument(documentId, {
            metadata: {
              ...document.metadata,
              isFavorite
            }
          });
        }
      }
      console.log(`Updated favorite status for ${selectedDocuments.length} documents`);
    } catch (error) {
      console.error('Bulk operation failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Bulk Operations</h2>
      
      <div className="space-y-2 mb-4">
        <button
          onClick={() => handleBulkAddTags(['important', 'reviewed'])}
          disabled={selectedDocuments.length === 0 || isProcessing}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          Add Tags to Selected ({selectedDocuments.length})
        </button>
        
        <button
          onClick={() => handleBulkFavorite(true)}
          disabled={selectedDocuments.length === 0 || isProcessing}
          className="px-4 py-2 bg-yellow-500 text-white rounded disabled:opacity-50"
        >
          Add to Favorites
        </button>
      </div>
      
      {isProcessing && <p>Processing...</p>}
    </div>
  );
}
```

## Import/Export Operations

### Bulk Document Import

```tsx
import { DocumentImportExport } from '@/components/library';
import { useState } from 'react';

function ImportExportPage() {
  const [documents, setDocuments] = useState([]);
  const [collections, setCollections] = useState([]);

  const refreshData = async () => {
    // Reload documents and collections
    const [docs, cols] = await Promise.all([
      documentLibrary.getAllDocuments(),
      documentLibrary.getAllCollections()
    ]);
    setDocuments(docs);
    setCollections(cols);
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Import & Export</h1>
      <DocumentImportExport
        documents={documents}
        collections={collections}
        onDocumentsUpdate={refreshData}
        onCollectionsUpdate={refreshData}
      />
    </div>
  );
}
```

### Programmatic Import

```tsx
import { documentLibrary } from '@/lib/document-library';

async function importDocumentsFromUrls(urls) {
  const results = [];
  
  for (const url of urls) {
    try {
      // Validate URL
      new URL(url);
      
      // Create metadata
      const metadata = {
        title: url.split('/').pop()?.replace('.pdf', '') || 'Untitled',
        tags: ['imported', 'url'],
        categories: ['external'],
        description: `Imported from ${url}`
      };
      
      // Add to library
      const document = await documentLibrary.addDocument(url, metadata);
      results.push({ success: true, document });
      
    } catch (error) {
      results.push({ success: false, url, error: error.message });
    }
  }
  
  return results;
}

// Usage
const urls = [
  'https://example.com/document1.pdf',
  'https://example.com/document2.pdf'
];

importDocumentsFromUrls(urls).then(results => {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`Imported ${successful.length} documents`);
  if (failed.length > 0) {
    console.log(`Failed to import ${failed.length} documents:`, failed);
  }
});
```

## Settings and Preferences

### Custom Settings Management

```tsx
import { DocumentSettings } from '@/components/library';
import { useState, useEffect } from 'react';
import { documentLibrary } from '@/lib/document-library';

function SettingsPage() {
  const [settings, setSettings] = useState(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    const currentSettings = await documentLibrary.getSettings();
    setSettings(currentSettings);
  };

  const handleSettingsChange = async (newSettings) => {
    setSettings(newSettings);
    // Settings are automatically saved by the component
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Library Settings</h1>
      <DocumentSettings onSettingsChange={handleSettingsChange} />
    </div>
  );
}
```

### Programmatic Settings Update

```tsx
import { documentLibrary } from '@/lib/document-library';

async function updateLibrarySettings() {
  try {
    await documentLibrary.updateSettings({
      defaultView: 'list',
      sortBy: 'name',
      sortOrder: 'asc',
      showThumbnails: true,
      thumbnailSize: 'large',
      maxStorageSize: 1024 * 1024 * 1024, // 1GB
      enableAutoBackup: true,
      backupInterval: 12 // 12 hours
    });
    
    console.log('Settings updated successfully');
  } catch (error) {
    console.error('Failed to update settings:', error);
  }
}
```

## Integration Examples

### Complete Document Management App

```tsx
import { useState, useEffect } from 'react';
import { 
  DocumentLibrary, 
  DocumentLibrarySidebar,
  DocumentSettings 
} from '@/components/library';
import { MultiDocumentPDFViewer } from '@/components/core';

function CompleteDocumentApp() {
  const [currentView, setCurrentView] = useState('library');
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const handleDocumentOpen = (document) => {
    setSelectedDocument(document);
    setCurrentView('viewer');
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'library':
        return (
          <DocumentLibrary
            onDocumentSelect={setSelectedDocument}
            onDocumentOpen={handleDocumentOpen}
          />
        );
      case 'viewer':
        return (
          <MultiDocumentPDFViewer
            initialDocument={selectedDocument}
            onClose={() => setCurrentView('library')}
          />
        );
      case 'settings':
        return <DocumentSettings />;
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen">
      {/* Sidebar */}
      {sidebarOpen && (
        <div className="w-80 border-r flex flex-col">
          {/* Navigation */}
          <div className="p-4 border-b">
            <nav className="space-y-2">
              <button
                onClick={() => setCurrentView('library')}
                className={`w-full text-left px-3 py-2 rounded ${
                  currentView === 'library' ? 'bg-blue-100' : 'hover:bg-gray-100'
                }`}
              >
                Library
              </button>
              <button
                onClick={() => setCurrentView('settings')}
                className={`w-full text-left px-3 py-2 rounded ${
                  currentView === 'settings' ? 'bg-blue-100' : 'hover:bg-gray-100'
                }`}
              >
                Settings
              </button>
            </nav>
          </div>
          
          {/* Sidebar Content */}
          <div className="flex-1">
            {currentView === 'library' && (
              <DocumentLibrarySidebar
                onDocumentSelect={setSelectedDocument}
                onDocumentOpen={handleDocumentOpen}
                onNewDocument={() => console.log('New document')}
              />
            )}
          </div>
        </div>
      )}
      
      {/* Main Content */}
      <div className="flex-1">
        {renderCurrentView()}
      </div>
    </div>
  );
}

export default CompleteDocumentApp;
```

This comprehensive example demonstrates how to build a complete document management application with all the available features integrated together.
