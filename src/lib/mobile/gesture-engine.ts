/**
 * Advanced Mobile Gesture Engine
 * Comprehensive touch gesture recognition and handling system
 */

export interface TouchPoint {
  id: number;
  x: number;
  y: number;
  timestamp: number;
  pressure?: number;
  radiusX?: number;
  radiusY?: number;
  rotationAngle?: number;
}

export interface GestureEvent {
  type: GestureType;
  touches: TouchPoint[];
  deltaX: number;
  deltaY: number;
  scale: number;
  rotation: number;
  velocity: { x: number; y: number };
  center: { x: number; y: number };
  distance: number;
  duration: number;
  preventDefault: () => void;
  stopPropagation: () => void;
}

export type GestureType = 
  | 'tap' 
  | 'double-tap' 
  | 'long-press' 
  | 'swipe' 
  | 'pan' 
  | 'pinch' 
  | 'rotate' 
  | 'two-finger-tap'
  | 'three-finger-tap'
  | 'edge-swipe'
  | 'force-touch';

export interface GestureConfig {
  // Tap gestures
  tapTimeout: number;
  doubleTapTimeout: number;
  longPressTimeout: number;
  tapMaxDistance: number;
  
  // Swipe gestures
  swipeMinDistance: number;
  swipeMaxTime: number;
  swipeMinVelocity: number;
  
  // Pan gestures
  panMinDistance: number;
  panMaxTouches: number;
  
  // Pinch gestures
  pinchMinScale: number;
  pinchMaxScale: number;
  pinchThreshold: number;
  
  // Rotation gestures
  rotationThreshold: number;
  
  // Edge detection
  edgeThreshold: number;
  
  // Force touch (3D Touch)
  forceThreshold: number;
  
  // Performance
  throttleMs: number;
  enableHapticFeedback: boolean;
}

export interface GestureCallbacks {
  onTap?: (event: GestureEvent) => void;
  onDoubleTap?: (event: GestureEvent) => void;
  onLongPress?: (event: GestureEvent) => void;
  onSwipe?: (event: GestureEvent) => void;
  onPan?: (event: GestureEvent) => void;
  onPinch?: (event: GestureEvent) => void;
  onRotate?: (event: GestureEvent) => void;
  onTwoFingerTap?: (event: GestureEvent) => void;
  onThreeFingerTap?: (event: GestureEvent) => void;
  onEdgeSwipe?: (event: GestureEvent) => void;
  onForceTouch?: (event: GestureEvent) => void;
  onGestureStart?: (event: GestureEvent) => void;
  onGestureEnd?: (event: GestureEvent) => void;
}

const DEFAULT_CONFIG: GestureConfig = {
  tapTimeout: 300,
  doubleTapTimeout: 300,
  longPressTimeout: 500,
  tapMaxDistance: 10,
  swipeMinDistance: 50,
  swipeMaxTime: 300,
  swipeMinVelocity: 0.3,
  panMinDistance: 10,
  panMaxTouches: 2,
  pinchMinScale: 0.1,
  pinchMaxScale: 10,
  pinchThreshold: 10,
  rotationThreshold: 5,
  edgeThreshold: 20,
  forceThreshold: 0.5,
  throttleMs: 16,
  enableHapticFeedback: true,
};

export class GestureEngine {
  private element: HTMLElement;
  private config: GestureConfig;
  private callbacks: GestureCallbacks;
  private isActive = false;
  
  // Touch tracking
  private touches: Map<number, TouchPoint> = new Map();
  private gestureStartTime = 0;
  private gestureStartCenter = { x: 0, y: 0 };
  private initialDistance = 0;
  private initialAngle = 0;
  private initialScale = 1;
  
  // Gesture state
  private currentGesture: GestureType | null = null;
  private tapCount = 0;
  private lastTapTime = 0;
  private longPressTimer: number | null = null;
  private doubleTapTimer: number | null = null;
  
  // Performance optimization
  private lastUpdateTime = 0;
  private animationFrame: number | null = null;
  
  constructor(
    element: HTMLElement,
    callbacks: GestureCallbacks = {},
    config: Partial<GestureConfig> = {}
  ) {
    this.element = element;
    this.callbacks = callbacks;
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    this.bindEvents();
  }
  
  private bindEvents(): void {
    this.element.addEventListener('touchstart', this.handleTouchStart, { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd, { passive: false });
    this.element.addEventListener('touchcancel', this.handleTouchCancel, { passive: false });
    
    // Prevent context menu on long press
    this.element.addEventListener('contextmenu', (e) => e.preventDefault());
  }
  
  private handleTouchStart = (e: TouchEvent): void => {
    const now = Date.now();
    this.gestureStartTime = now;
    
    // Update touch tracking
    for (let i = 0; i < e.touches.length; i++) {
      const touch = e.touches[i];
      this.touches.set(touch.identifier, this.createTouchPoint(touch, now));
    }
    
    const touchCount = this.touches.size;
    const center = this.getCenter();
    this.gestureStartCenter = center;
    
    // Initialize gesture-specific state
    if (touchCount === 2) {
      this.initialDistance = this.getDistance();
      this.initialAngle = this.getAngle();
    }
    
    // Handle tap detection
    if (touchCount === 1) {
      this.handleTapStart(center, now);
    }
    
    // Handle multi-finger taps
    if (touchCount === 2) {
      this.handleTwoFingerTap();
    } else if (touchCount === 3) {
      this.handleThreeFingerTap();
    }
    
    // Start long press detection
    if (touchCount === 1) {
      this.startLongPressDetection();
    }
    
    // Check for edge swipe
    if (this.isEdgeTouch(center)) {
      this.currentGesture = 'edge-swipe';
    }
    
    this.triggerCallback('onGestureStart', this.createGestureEvent('tap'));
  };
  
  private handleTouchMove = (e: TouchEvent): void => {
    if (this.touches.size === 0) return;
    
    const now = Date.now();
    
    // Throttle updates for performance
    if (now - this.lastUpdateTime < this.config.throttleMs) {
      return;
    }
    this.lastUpdateTime = now;
    
    // Update touch positions
    for (let i = 0; i < e.touches.length; i++) {
      const touch = e.touches[i];
      if (this.touches.has(touch.identifier)) {
        this.touches.set(touch.identifier, this.createTouchPoint(touch, now));
      }
    }
    
    const touchCount = this.touches.size;
    const center = this.getCenter();
    const deltaX = center.x - this.gestureStartCenter.x;
    const deltaY = center.y - this.gestureStartCenter.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    
    // Cancel tap if moved too far
    if (distance > this.config.tapMaxDistance) {
      this.cancelTap();
      this.cancelLongPress();
    }
    
    // Detect gesture type based on movement
    if (touchCount === 1) {
      if (distance > this.config.panMinDistance) {
        this.currentGesture = 'pan';
        this.triggerCallback('onPan', this.createGestureEvent('pan'));
      }
    } else if (touchCount === 2) {
      const currentDistance = this.getDistance();
      const currentAngle = this.getAngle();
      
      const scaleChange = Math.abs(currentDistance - this.initialDistance);
      const rotationChange = Math.abs(currentAngle - this.initialAngle);
      
      if (scaleChange > this.config.pinchThreshold) {
        this.currentGesture = 'pinch';
        this.triggerCallback('onPinch', this.createGestureEvent('pinch'));
      } else if (rotationChange > this.config.rotationThreshold) {
        this.currentGesture = 'rotate';
        this.triggerCallback('onRotate', this.createGestureEvent('rotate'));
      } else if (distance > this.config.panMinDistance) {
        this.currentGesture = 'pan';
        this.triggerCallback('onPan', this.createGestureEvent('pan'));
      }
    }
    
    // Prevent default to avoid scrolling
    if (this.currentGesture && this.currentGesture !== 'tap') {
      e.preventDefault();
    }
  };
  
  private handleTouchEnd = (e: TouchEvent): void => {
    const now = Date.now();
    const duration = now - this.gestureStartTime;

    // Calculate center and distance before removing touches
    const center = this.getCenter();
    const deltaX = center.x - this.gestureStartCenter.x;
    const deltaY = center.y - this.gestureStartCenter.y;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Remove ended touches
    const endedTouches = Array.from(this.touches.keys()).filter(id =>
      !Array.from(e.touches).some(touch => touch.identifier === id)
    );

    endedTouches.forEach(id => this.touches.delete(id));

    // Handle gesture completion
    if (this.touches.size === 0) {
      this.handleGestureEnd(duration, center, distance);
    }

    this.cancelLongPress();
  };
  
  private handleTouchCancel = (): void => {
    this.touches.clear();
    this.cancelAllGestures();
  };
  
  private handleGestureEnd(duration: number, center: { x: number; y: number }, distance: number): void {
    const deltaX = center.x - this.gestureStartCenter.x;
    const deltaY = center.y - this.gestureStartCenter.y;
    const velocity = this.calculateVelocity(deltaX, deltaY, duration);

    // Detect swipe
    if (
      distance > this.config.swipeMinDistance &&
      duration < this.config.swipeMaxTime &&
      velocity.magnitude > this.config.swipeMinVelocity
    ) {
      this.currentGesture = 'swipe';
      this.triggerCallback('onSwipe', this.createGestureEvent('swipe'));
    }

    // Handle tap completion
    if (this.currentGesture === null && distance <= this.config.tapMaxDistance) {
      this.handleTapEnd();
    }

    this.triggerCallback('onGestureEnd', this.createGestureEvent(this.currentGesture || 'tap'));
    this.resetGestureState();
  }
  
  private handleTapStart(center: { x: number; y: number }, timestamp: number): void {
    const timeSinceLastTap = timestamp - this.lastTapTime;
    
    if (timeSinceLastTap < this.config.doubleTapTimeout) {
      this.tapCount++;
    } else {
      this.tapCount = 1;
    }
    
    this.lastTapTime = timestamp;
  }
  
  private handleTapEnd(): void {
    if (this.tapCount === 1) {
      // Single tap - wait for potential double tap
      this.doubleTapTimer = window.setTimeout(() => {
        this.triggerCallback('onTap', this.createGestureEvent('tap'));
        this.triggerHapticFeedback('light');
        this.tapCount = 0;
      }, this.config.doubleTapTimeout);
    } else if (this.tapCount === 2) {
      // Double tap
      if (this.doubleTapTimer) {
        clearTimeout(this.doubleTapTimer);
        this.doubleTapTimer = null;
      }
      this.triggerCallback('onDoubleTap', this.createGestureEvent('double-tap'));
      this.triggerHapticFeedback('medium');
      this.tapCount = 0;
    }
  }
  
  private handleTwoFingerTap(): void {
    this.triggerCallback('onTwoFingerTap', this.createGestureEvent('two-finger-tap'));
    this.triggerHapticFeedback('light');
  }
  
  private handleThreeFingerTap(): void {
    this.triggerCallback('onThreeFingerTap', this.createGestureEvent('three-finger-tap'));
    this.triggerHapticFeedback('heavy');
  }
  
  private startLongPressDetection(): void {
    this.longPressTimer = window.setTimeout(() => {
      if (this.touches.size === 1) {
        this.currentGesture = 'long-press';
        this.triggerCallback('onLongPress', this.createGestureEvent('long-press'));
        this.triggerHapticFeedback('heavy');
      }
    }, this.config.longPressTimeout);
  }
  
  private cancelTap(): void {
    if (this.doubleTapTimer) {
      clearTimeout(this.doubleTapTimer);
      this.doubleTapTimer = null;
    }
    this.tapCount = 0;
  }
  
  private cancelLongPress(): void {
    if (this.longPressTimer) {
      clearTimeout(this.longPressTimer);
      this.longPressTimer = null;
    }
  }
  
  private cancelAllGestures(): void {
    this.cancelTap();
    this.cancelLongPress();
    this.resetGestureState();
  }
  
  private resetGestureState(): void {
    this.currentGesture = null;
    this.initialDistance = 0;
    this.initialAngle = 0;
    this.gestureStartTime = 0;
  }
  
  private createTouchPoint(touch: Touch, timestamp: number): TouchPoint {
    return {
      id: touch.identifier,
      x: touch.clientX,
      y: touch.clientY,
      timestamp,
      pressure: (touch as Touch & { force?: number }).force || 1,
      radiusX: touch.radiusX || 1,
      radiusY: touch.radiusY || 1,
      rotationAngle: touch.rotationAngle || 0,
    };
  }
  
  private getCenter(): { x: number; y: number } {
    const touches = Array.from(this.touches.values());
    if (touches.length === 0) return { x: 0, y: 0 };
    
    const sum = touches.reduce(
      (acc, touch) => ({ x: acc.x + touch.x, y: acc.y + touch.y }),
      { x: 0, y: 0 }
    );
    
    return {
      x: sum.x / touches.length,
      y: sum.y / touches.length,
    };
  }
  
  private getDistance(): number {
    const touches = Array.from(this.touches.values());
    if (touches.length < 2) return 0;
    
    const dx = touches[1].x - touches[0].x;
    const dy = touches[1].y - touches[0].y;
    return Math.sqrt(dx * dx + dy * dy);
  }
  
  private getAngle(): number {
    const touches = Array.from(this.touches.values());
    if (touches.length < 2) return 0;
    
    const dx = touches[1].x - touches[0].x;
    const dy = touches[1].y - touches[0].y;
    return Math.atan2(dy, dx) * (180 / Math.PI);
  }
  
  private calculateVelocity(deltaX: number, deltaY: number, duration: number): { x: number; y: number; magnitude: number } {
    const time = Math.max(duration, 1);
    const vx = deltaX / time;
    const vy = deltaY / time;
    const magnitude = Math.sqrt(vx * vx + vy * vy);
    
    return { x: vx, y: vy, magnitude };
  }
  
  private isEdgeTouch(point: { x: number; y: number }): boolean {
    const rect = this.element.getBoundingClientRect();
    return (
      point.x < this.config.edgeThreshold ||
      point.x > rect.width - this.config.edgeThreshold ||
      point.y < this.config.edgeThreshold ||
      point.y > rect.height - this.config.edgeThreshold
    );
  }
  
  private createGestureEvent(type: GestureType): GestureEvent {
    const touches = Array.from(this.touches.values());
    const center = this.getCenter();
    const deltaX = center.x - this.gestureStartCenter.x;
    const deltaY = center.y - this.gestureStartCenter.y;
    const duration = Date.now() - this.gestureStartTime;
    const velocity = this.calculateVelocity(deltaX, deltaY, duration);
    
    return {
      type,
      touches,
      deltaX,
      deltaY,
      scale: this.getDistance() / (this.initialDistance || 1),
      rotation: this.getAngle() - this.initialAngle,
      velocity,
      center,
      distance: this.getDistance(),
      duration,
      preventDefault: () => {},
      stopPropagation: () => {},
    };
  }
  
  private triggerCallback(callbackName: keyof GestureCallbacks, event: GestureEvent): void {
    const callback = this.callbacks[callbackName];
    if (callback) {
      callback(event);
    }
  }
  
  private triggerHapticFeedback(type: 'light' | 'medium' | 'heavy'): void {
    if (!this.config.enableHapticFeedback) return;
    
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30],
      };
      navigator.vibrate(patterns[type]);
    }
  }
  
  public destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart);
    this.element.removeEventListener('touchmove', this.handleTouchMove);
    this.element.removeEventListener('touchend', this.handleTouchEnd);
    this.element.removeEventListener('touchcancel', this.handleTouchCancel);
    
    this.cancelAllGestures();
    this.touches.clear();
    
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
    }
  }
  
  public updateConfig(config: Partial<GestureConfig>): void {
    this.config = { ...this.config, ...config };
  }
  
  public updateCallbacks(callbacks: Partial<GestureCallbacks>): void {
    this.callbacks = { ...this.callbacks, ...callbacks };
  }
}
