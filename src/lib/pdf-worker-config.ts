import { pdfjs } from 'react-pdf';

/**
 * Configure PDF.js worker with proper fallback handling
 * This ensures reliable PDF loading across different environments
 */
export function configurePDFWorker() {
  // Try to use local worker first, fallback to CDN if needed
  const workerSrc = process.env.NODE_ENV === 'production' 
    ? `/pdf.worker.min.js`
    : `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

  pdfjs.GlobalWorkerOptions.workerSrc = workerSrc;

  // Additional PDF.js configuration for better reliability
  if (typeof window !== 'undefined') {
    // Disable automatic font loading to prevent CORS issues
    pdfjs.GlobalWorkerOptions.disableFontFace = false;
    
    // Enable verbose logging in development
    if (process.env.NODE_ENV === 'development') {
      pdfjs.GlobalWorkerOptions.verbosity = pdfjs.VerbosityLevel.INFOS;
    }

    // Configure memory management
    pdfjs.GlobalWorkerOptions.maxImageSize = 1024 * 1024 * 50; // 50MB
    pdfjs.GlobalWorkerOptions.disableAutoFetch = false;
    pdfjs.GlobalWorkerOptions.disableStream = false;
  }
}

/**
 * Test if PDF.js worker is properly configured and accessible
 */
export async function testPDFWorker(): Promise<boolean> {
  try {
    // Create a minimal PDF document to test worker functionality
    const testPDF = new Uint8Array([
      0x25, 0x50, 0x44, 0x46, 0x2d, 0x31, 0x2e, 0x34, // %PDF-1.4
      0x0a, 0x25, 0xe2, 0xe3, 0xcf, 0xd3, 0x0a, 0x0a, // Binary comment
      0x31, 0x20, 0x30, 0x20, 0x6f, 0x62, 0x6a, 0x0a, // 1 0 obj
      0x3c, 0x3c, 0x0a, 0x2f, 0x54, 0x79, 0x70, 0x65, // <<
      0x20, 0x2f, 0x43, 0x61, 0x74, 0x61, 0x6c, 0x6f, //  /Catalog
      0x67, 0x0a, 0x2f, 0x50, 0x61, 0x67, 0x65, 0x73, // g /Pages
      0x20, 0x32, 0x20, 0x30, 0x20, 0x52, 0x0a, 0x3e, //  2 0 R >
      0x3e, 0x0a, 0x65, 0x6e, 0x64, 0x6f, 0x62, 0x6a, // > endobj
      0x0a, 0x0a, 0x32, 0x20, 0x30, 0x20, 0x6f, 0x62, // 2 0 obj
      0x6a, 0x0a, 0x3c, 0x3c, 0x0a, 0x2f, 0x54, 0x79, // j << /Type
      0x70, 0x65, 0x20, 0x2f, 0x50, 0x61, 0x67, 0x65, // pe /Page
      0x73, 0x0a, 0x2f, 0x4b, 0x69, 0x64, 0x73, 0x20, // s /Kids 
      0x5b, 0x33, 0x20, 0x30, 0x20, 0x52, 0x5d, 0x0a, // [3 0 R]
      0x2f, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x20, 0x31, // /Count 1
      0x0a, 0x3e, 0x3e, 0x0a, 0x65, 0x6e, 0x64, 0x6f, // >> endobj
      0x62, 0x6a, 0x0a, 0x0a, 0x33, 0x20, 0x30, 0x20, // bj 3 0 
      0x6f, 0x62, 0x6a, 0x0a, 0x3c, 0x3c, 0x0a, 0x2f, // obj << /
      0x54, 0x79, 0x70, 0x65, 0x20, 0x2f, 0x50, 0x61, // Type /Pa
      0x67, 0x65, 0x0a, 0x2f, 0x50, 0x61, 0x72, 0x65, // ge /Pare
      0x6e, 0x74, 0x20, 0x32, 0x20, 0x30, 0x20, 0x52, // nt 2 0 R
      0x0a, 0x2f, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x42, // /MediaB
      0x6f, 0x78, 0x20, 0x5b, 0x30, 0x20, 0x30, 0x20, // ox [0 0 
      0x36, 0x31, 0x32, 0x20, 0x37, 0x39, 0x32, 0x5d, // 612 792]
      0x0a, 0x3e, 0x3e, 0x0a, 0x65, 0x6e, 0x64, 0x6f, // >> endobj
      0x62, 0x6a, 0x0a, 0x0a, 0x78, 0x72, 0x65, 0x66, // bj xref
      0x0a, 0x30, 0x20, 0x34, 0x0a, 0x30, 0x30, 0x30, // 0 4 000
      0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x20, // 0000000 
      0x36, 0x35, 0x35, 0x33, 0x35, 0x20, 0x66, 0x20, // 65535 f 
      0x0a, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // 0000000
      0x30, 0x30, 0x39, 0x20, 0x30, 0x30, 0x30, 0x30, // 009 0000
      0x30, 0x20, 0x6e, 0x20, 0x0a, 0x30, 0x30, 0x30, //  n 000
      0x30, 0x30, 0x30, 0x30, 0x30, 0x37, 0x34, 0x20, // 0000074 
      0x30, 0x30, 0x30, 0x30, 0x30, 0x20, 0x6e, 0x20, // 00000 n 
      0x0a, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, 0x30, // 0000000
      0x31, 0x34, 0x39, 0x20, 0x30, 0x30, 0x30, 0x30, // 149 0000
      0x30, 0x20, 0x6e, 0x20, 0x0a, 0x74, 0x72, 0x61, //  n trailer
      0x69, 0x6c, 0x65, 0x72, 0x0a, 0x3c, 0x3c, 0x0a, // iler <<
      0x2f, 0x53, 0x69, 0x7a, 0x65, 0x20, 0x34, 0x0a, // /Size 4
      0x2f, 0x52, 0x6f, 0x6f, 0x74, 0x20, 0x31, 0x20, // /Root 1 
      0x30, 0x20, 0x52, 0x0a, 0x3e, 0x3e, 0x0a, 0x73, // 0 R >> s
      0x74, 0x61, 0x72, 0x74, 0x78, 0x72, 0x65, 0x66, // tartxref
      0x0a, 0x32, 0x32, 0x39, 0x0a, 0x25, 0x25, 0x45, // 229 %%E
      0x4f, 0x46, 0x0a // OF
    ]);

    const loadingTask = pdfjs.getDocument({ data: testPDF });
    const pdf = await loadingTask.promise;
    
    // If we can load a simple PDF, the worker is working
    return pdf.numPages === 1;
  } catch (error) {
    console.error('PDF.js worker test failed:', error);
    return false;
  }
}

/**
 * Log PDF loading attempt with detailed information
 */
export function logPDFLoadAttempt(file: string | File, context: string = 'unknown') {
  const timestamp = new Date().toISOString();
  const fileInfo = typeof file === 'string'
    ? { type: 'url', source: file, size: 'unknown' }
    : { type: 'file', name: file.name, size: file.size, lastModified: file.lastModified };

  console.log(`[PDF Load Attempt] ${timestamp}`, {
    context,
    file: fileInfo,
    userAgent: navigator.userAgent,
    viewport: { width: window.innerWidth, height: window.innerHeight },
    memory: (performance as Performance & { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory ? {
      used: Math.round((performance as Performance & { memory: { usedJSHeapSize: number } }).memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round((performance as Performance & { memory: { totalJSHeapSize: number } }).memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round((performance as Performance & { memory: { jsHeapSizeLimit: number } }).memory.jsHeapSizeLimit / 1024 / 1024)
    } : 'unavailable'
  });
}

/**
 * Log PDF loading success with performance metrics
 */
export function logPDFLoadSuccess(numPages: number, loadTime: number, context: string = 'unknown') {
  const timestamp = new Date().toISOString();

  console.log(`[PDF Load Success] ${timestamp}`, {
    context,
    numPages,
    loadTime: `${loadTime}ms`,
    performance: {
      timing: performance.timing ? {
        domContentLoaded: performance.timing.domContentLoadedEventEnd - performance.timing.navigationStart,
        loadComplete: performance.timing.loadEventEnd - performance.timing.navigationStart
      } : 'unavailable'
    }
  });
}

/**
 * Enhanced error handler for PDF loading issues
 */
export function handlePDFError(error: Error, context: string = 'unknown'): {
  type: string;
  message: string;
  canRetry: boolean;
  suggestedAction: string;
} {
  const timestamp = new Date().toISOString();

  // Log detailed error information
  console.error(`[PDF Load Error] ${timestamp}`, {
    context,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    },
    environment: {
      userAgent: navigator.userAgent,
      online: navigator.onLine,
      cookieEnabled: navigator.cookieEnabled,
      language: navigator.language,
      platform: navigator.platform
    },
    pdfjs: {
      version: typeof pdfjs !== 'undefined' ? pdfjs.version : 'unknown',
      workerSrc: typeof pdfjs !== 'undefined' ? pdfjs.GlobalWorkerOptions.workerSrc : 'unknown'
    }
  });
  const message = error.message.toLowerCase();
  
  // Worker-related errors
  if (message.includes('worker') || message.includes('script error')) {
    return {
      type: 'WORKER_ERROR',
      message: 'PDF.js worker failed to load or execute',
      canRetry: true,
      suggestedAction: 'Check your internet connection and try reloading the page'
    };
  }
  
  // CORS-related errors
  if (message.includes('cors') || message.includes('cross-origin')) {
    return {
      type: 'CORS_ERROR',
      message: 'Cross-origin request blocked',
      canRetry: true,
      suggestedAction: 'Try uploading the PDF file directly instead of using a URL'
    };
  }
  
  // Network errors
  if (message.includes('network') || message.includes('fetch') || message.includes('load')) {
    return {
      type: 'NETWORK_ERROR',
      message: 'Network error occurred while loading the PDF',
      canRetry: true,
      suggestedAction: 'Check your internet connection and try again'
    };
  }
  
  // File format errors
  if (message.includes('invalid') || message.includes('corrupted') || message.includes('format')) {
    return {
      type: 'FORMAT_ERROR',
      message: 'The PDF file appears to be invalid or corrupted',
      canRetry: false,
      suggestedAction: 'Try opening a different PDF file'
    };
  }
  
  // Memory errors
  if (message.includes('memory') || message.includes('out of memory')) {
    return {
      type: 'MEMORY_ERROR',
      message: 'Insufficient memory to load the PDF',
      canRetry: true,
      suggestedAction: 'Close other browser tabs and try again'
    };
  }
  
  // Default error
  return {
    type: 'UNKNOWN_ERROR',
    message: error.message || 'An unknown error occurred while loading the PDF',
    canRetry: true,
    suggestedAction: 'Try reloading the page or using a different PDF file'
  };
}
