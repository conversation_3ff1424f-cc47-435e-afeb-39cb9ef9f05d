import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Re-export the proper PDF types from pdfjs-dist
export type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Type for the document callback from react-pdf
export type DocumentCallback = PDFDocumentProxy;

// Type for page callback from react-pdf
export type PageCallback = PDFPageProxy & {
  width: number;
  height: number;
  originalWidth: number;
  originalHeight: number;
};

// Union type for PDF document that can be either the direct proxy or wrapped
export type PDFDocument = PDFDocumentProxy | {
  _pdfInfo?: {
    pdfDocument: PDFDocumentProxy;
  };
} | unknown;

// Type guard to check if an object is a PDFDocumentProxy
export function isPDFDocumentProxy(obj: unknown): obj is PDFDocumentProxy {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'numPages' in obj &&
    'getPage' in obj &&
    typeof (obj as Record<string, unknown>).getPage === 'function'
  );
}

// Helper function to extract the actual PDF document from various formats
export function extractPDFDocument(pdfDocument: PDFDocument): PDFDocumentProxy | null {
  if (!pdfDocument) return null;
  
  // Check if it's already a PDFDocumentProxy
  if (isPDFDocumentProxy(pdfDocument)) {
    return pdfDocument;
  }
  
  // Check if it has the _pdfInfo wrapper (legacy format)
  if (
    typeof pdfDocument === 'object' &&
    pdfDocument !== null &&
    '_pdfInfo' in pdfDocument &&
    pdfDocument._pdfInfo &&
    typeof pdfDocument._pdfInfo === 'object' &&
    'pdfDocument' in pdfDocument._pdfInfo
  ) {
    const wrapped = pdfDocument._pdfInfo.pdfDocument;
    if (isPDFDocumentProxy(wrapped)) {
      return wrapped;
    }
  }
  
  return null;
}

// Type for outline items
export interface OutlineItem {
  title: string;
  bold?: boolean;
  italic?: boolean;
  color?: [number, number, number] | null;
  dest?: unknown;
  url?: string;
  items?: OutlineItem[];
}

// Type for text content items
export interface TextContentItem {
  str: string;
  dir: string;
  width: number;
  height: number;
  transform: number[];
  fontName: string;
  hasEOL: boolean;
}

// Type for text content
export interface TextContent {
  items: TextContentItem[];
  styles: Record<string, unknown>;
}

// Document metadata interface for enhanced document management
export interface DocumentMetadata {
  // Basic metadata
  title: string;
  author?: string;
  subject?: string;
  keywords?: string[];
  creator?: string;
  producer?: string;

  // File information
  fileName: string;
  fileSize: number;
  filePath?: string;
  mimeType: string;

  // Dates
  creationDate?: Date;
  modificationDate?: Date;
  addedDate: Date;
  lastAccessedDate: Date;

  // Document properties
  pageCount: number;
  version?: string;
  isEncrypted: boolean;
  hasFormFields: boolean;
  hasAnnotations: boolean;
  hasBookmarks: boolean;

  // Organization
  tags: string[];
  categories: string[];
  collections: string[];
  isFavorite: boolean;
  isPinned: boolean;

  // Thumbnail and preview
  thumbnailUrl?: string;
  previewPages?: string[]; // URLs to preview images

  // Usage statistics
  openCount: number;
  totalTimeSpent: number; // in milliseconds
  lastPageViewed: number;

  // Custom metadata
  description?: string;
  notes?: string;
  rating?: number; // 1-5 stars
  customFields: Record<string, unknown>;
}

// Document instance interface for multi-document support
export interface DocumentInstance {
  id: string;
  file: string | File;
  title: string;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
  numPages: number;
  pdfDocument?: PDFDocumentProxy;
  outline?: OutlineItem[];
  lastAccessed: number;

  // Enhanced metadata
  metadata: DocumentMetadata;

  // Document-specific state
  pageNumber: number;
  scale: number;
  rotation: number;
  searchText: string;
  bookmarks: Array<{ id: string; page: number; title: string; timestamp: number }>;
  annotations: unknown[];
  formData: Record<string, unknown>;
}

// Document manager state
export interface DocumentManagerState {
  documents: Map<string, DocumentInstance>;
  activeDocumentId: string | null;
  maxDocuments: number;
  memoryThreshold: number;
}

// Error types for better error handling
export enum DocumentErrorType {
  LOAD_FAILED = 'LOAD_FAILED',
  CORRUPTED_FILE = 'CORRUPTED_FILE',
  UNSUPPORTED_FORMAT = 'UNSUPPORTED_FORMAT',
  FILE_NOT_FOUND = 'FILE_NOT_FOUND',
  NETWORK_ERROR = 'NETWORK_ERROR',
  MEMORY_ERROR = 'MEMORY_ERROR',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  WORKER_ERROR = 'WORKER_ERROR',
  CORS_ERROR = 'CORS_ERROR',
  FORMAT_ERROR = 'FORMAT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface DocumentError {
  type: DocumentErrorType;
  message: string;
  originalError?: Error;
  canRetry: boolean;
  suggestedAction?: string;
}

// Document library interfaces
export interface DocumentLibrary {
  id: string;
  name: string;
  description?: string;
  documents: Map<string, DocumentInstance>;
  collections: DocumentCollection[];
  settings: DocumentLibrarySettings;
  createdDate: Date;
  modifiedDate: Date;
}

export interface DocumentCollection {
  id: string;
  name: string;
  description?: string;
  documentIds: string[];
  color?: string;
  icon?: string;
  isSystem: boolean; // For built-in collections like "Recent", "Favorites"
  createdDate: Date;
  modifiedDate: Date;
}

export interface DocumentLibrarySettings {
  defaultView: 'grid' | 'list' | 'compact';
  sortBy: 'name' | 'dateAdded' | 'dateModified' | 'size' | 'lastAccessed';
  sortOrder: 'asc' | 'desc';
  showThumbnails: boolean;
  thumbnailSize: 'small' | 'medium' | 'large';
  autoGenerateThumbnails: boolean;
  maxStorageSize: number; // in bytes
  enableAutoBackup: boolean;
  backupInterval: number; // in hours
}

// Search and filter interfaces
export interface DocumentSearchQuery {
  text?: string;
  tags?: string[];
  categories?: string[];
  collections?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  sizeRange?: {
    min: number;
    max: number;
  };
  pageCountRange?: {
    min: number;
    max: number;
  };
  hasAnnotations?: boolean;
  hasBookmarks?: boolean;
  hasFormFields?: boolean;
  isFavorite?: boolean;
  rating?: number;
}

export interface DocumentSearchResult {
  documents: DocumentInstance[];
  totalCount: number;
  facets: {
    tags: Array<{ name: string; count: number }>;
    categories: Array<{ name: string; count: number }>;
    collections: Array<{ name: string; count: number }>;
    authors: Array<{ name: string; count: number }>;
  };
}

// Import/Export interfaces
export interface DocumentImportOptions {
  source: 'file' | 'url' | 'folder' | 'cloud';
  extractMetadata: boolean;
  generateThumbnails: boolean;
  autoTagging: boolean;
  duplicateHandling: 'skip' | 'replace' | 'rename';
  defaultCollection?: string;
  defaultTags?: string[];
}

export interface DocumentExportOptions {
  format: 'json' | 'csv' | 'xml';
  includeFiles: boolean;
  includeMetadata: boolean;
  includeThumbnails: boolean;
  compression: boolean;
  selectedDocuments?: string[];
  selectedCollections?: string[];
}

// Helper function to generate document title from file
export function generateDocumentTitle(file: string | File): string {
  if (typeof file === 'string') {
    // Extract filename from URL
    try {
      const url = new URL(file);
      const pathname = url.pathname;
      const filename = pathname.split('/').pop() || 'Document';
      return filename.replace(/\.[^/.]+$/, ''); // Remove extension
    } catch {
      return 'Remote Document';
    }
  } else {
    // Extract filename from File object
    return file.name.replace(/\.[^/.]+$/, ''); // Remove extension
  }
}

// Utility function to create default document metadata
export function createDefaultDocumentMetadata(file: string | File): DocumentMetadata {
  const now = new Date();
  const fileName = typeof file === 'string'
    ? file.split('/').pop() || 'unknown.pdf'
    : file.name;

  return {
    title: generateDocumentTitle(file),
    fileName,
    fileSize: typeof file === 'string' ? 0 : file.size,
    mimeType: 'application/pdf',
    addedDate: now,
    lastAccessedDate: now,
    pageCount: 0,
    isEncrypted: false,
    hasFormFields: false,
    hasAnnotations: false,
    hasBookmarks: false,
    tags: [],
    categories: [],
    collections: [],
    isFavorite: false,
    isPinned: false,
    openCount: 0,
    totalTimeSpent: 0,
    lastPageViewed: 1,
    customFields: {}
  };
}

// Utility function to extract metadata from PDF document
export async function extractPDFMetadata(
  pdfDocument: PDFDocumentProxy,
  file: string | File
): Promise<Partial<DocumentMetadata>> {
  try {
    const metadata = await pdfDocument.getMetadata();
    const info = metadata.info;

    return {
      title: info.Title || generateDocumentTitle(file),
      author: info.Author || undefined,
      subject: info.Subject || undefined,
      keywords: info.Keywords ? info.Keywords.split(',').map(k => k.trim()) : [],
      creator: info.Creator || undefined,
      producer: info.Producer || undefined,
      creationDate: info.CreationDate ? new Date(info.CreationDate) : undefined,
      modificationDate: info.ModDate ? new Date(info.ModDate) : undefined,
      pageCount: pdfDocument.numPages,
      version: info.PDFFormatVersion || undefined,
      isEncrypted: pdfDocument.isEncrypted || false
    };
  } catch (error) {
    console.warn('Failed to extract PDF metadata:', error);
    return {
      pageCount: pdfDocument.numPages,
      isEncrypted: pdfDocument.isEncrypted || false
    };
  }
}

// Utility function to format file size
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Utility function to format duration
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);

  if (hours > 0) {
    return `${hours}h ${minutes % 60}m`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

// Helper function to categorize errors
export function categorizeError(error: Error): DocumentError {
  const message = error.message.toLowerCase();

  if (message.includes('network') || message.includes('fetch')) {
    return {
      type: DocumentErrorType.NETWORK_ERROR,
      message: 'Network error occurred while loading the document',
      originalError: error,
      canRetry: true,
      suggestedAction: 'Check your internet connection and try again'
    };
  }

  if (message.includes('corrupted') || message.includes('invalid pdf')) {
    return {
      type: DocumentErrorType.CORRUPTED_FILE,
      message: 'The PDF file appears to be corrupted or invalid',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Try opening a different PDF file'
    };
  }

  if (message.includes('not found') || message.includes('404')) {
    return {
      type: DocumentErrorType.FILE_NOT_FOUND,
      message: 'The PDF file could not be found',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Verify the file path or URL is correct'
    };
  }

  if (message.includes('permission') || message.includes('unauthorized')) {
    return {
      type: DocumentErrorType.PERMISSION_DENIED,
      message: 'Permission denied accessing the PDF file',
      originalError: error,
      canRetry: false,
      suggestedAction: 'Check file permissions or authentication'
    };
  }

  if (message.includes('memory') || message.includes('out of memory')) {
    return {
      type: DocumentErrorType.MEMORY_ERROR,
      message: 'Insufficient memory to load the document',
      originalError: error,
      canRetry: true,
      suggestedAction: 'Close other documents and try again'
    };
  }

  // Default case
  return {
    type: DocumentErrorType.LOAD_FAILED,
    message: error.message || 'Failed to load PDF document',
    originalError: error,
    canRetry: true,
    suggestedAction: 'Try reloading the document'
  };
}
