/**
 * OCR Manager
 * Manages OCR operations for PDF documents with caching, background processing, and integration
 */

import { Tesseract<PERSON>ngine, type OCRResult, type OCRProgress, type OCRConfig, type ImagePreprocessingOptions } from './tesseract-engine';
import { getCacheManager } from '@/lib/cache/cache-manager';

export interface OCRDocumentResult {
  documentId: string;
  totalPages: number;
  processedPages: number;
  results: Map<number, OCRResult>;
  metadata: {
    startTime: Date;
    endTime?: Date;
    totalProcessingTime?: number;
    averageConfidence: number;
    detectedLanguages: string[];
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  };
}

export interface OCRJobConfig {
  documentId: string;
  pages: number[];
  language?: string | string[];
  preprocessingOptions?: ImagePreprocessingOptions;
  priority?: 'low' | 'normal' | 'high';
  enableCaching?: boolean;
  backgroundProcessing?: boolean;
}

export interface OCRManagerConfig {
  maxWorkers: number;
  defaultLanguage: string;
  enableCaching: boolean;
  cacheTimeout: number; // milliseconds
  maxConcurrentJobs: number;
  enableBackgroundProcessing: boolean;
  ocrConfig: Partial<OCRConfig>;
}

const DEFAULT_CONFIG: OCRManagerConfig = {
  maxWorkers: 2,
  defaultLanguage: 'eng',
  enableCaching: true,
  cacheTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxConcurrentJobs: 3,
  enableBackgroundProcessing: true,
  ocrConfig: {},
};

export class OCRManager {
  private static instance: OCRManager | null = null;
  private engine: TesseractEngine | null = null;
  private config: OCRManagerConfig;
  private activeJobs = new Map<string, OCRDocumentResult>();
  private jobQueue: OCRJobConfig[] = [];
  private isProcessingQueue = false;
  private cacheManager = getCacheManager();

  private constructor(config: Partial<OCRManagerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  static getInstance(config?: Partial<OCRManagerConfig>): OCRManager {
    if (!OCRManager.instance) {
      OCRManager.instance = new OCRManager(config);
    }
    return OCRManager.instance;
  }

  /**
   * Initialize the OCR manager
   */
  async initialize(onProgress?: (progress: OCRProgress) => void): Promise<void> {
    if (this.engine) return;

    this.engine = new TesseractEngine(this.config.maxWorkers, this.config.ocrConfig);
    await this.engine.initialize(onProgress);
  }

  /**
   * Process OCR for a PDF document
   */
  async processDocument(
    pdfDocument: unknown,
    config: OCRJobConfig,
    onProgress?: (progress: OCRProgress) => void,
    onPageComplete?: (result: OCRResult) => void
  ): Promise<OCRDocumentResult> {
    if (!this.engine) {
      throw new Error('OCR manager not initialized. Call initialize() first.');
    }

    const { documentId, pages, language, preprocessingOptions, enableCaching = this.config.enableCaching } = config;

    // Check if job already exists
    if (this.activeJobs.has(documentId)) {
      return this.activeJobs.get(documentId)!;
    }

    // Create document result
    const documentResult: OCRDocumentResult = {
      documentId,
      totalPages: pages.length,
      processedPages: 0,
      results: new Map(),
      metadata: {
        startTime: new Date(),
        averageConfidence: 0,
        detectedLanguages: [],
        status: 'processing',
      },
    };

    this.activeJobs.set(documentId, documentResult);

    try {
      onProgress?.({
        status: 'processing',
        progress: 0,
        message: `Starting OCR for document ${documentId}...`,
        totalPages: pages.length,
      });

      const imagesToProcess: Array<{
        data: HTMLCanvasElement;
        pageNumber: number;
        preprocessingOptions?: ImagePreprocessingOptions;
      }> = [];

      // Extract images from PDF pages
      for (let i = 0; i < pages.length; i++) {
        const pageNumber = pages[i];

        onProgress?.({
          status: 'processing',
          progress: (i / pages.length) * 20, // 20% for image extraction
          message: `Extracting image from page ${pageNumber}...`,
          pageNumber,
          totalPages: pages.length,
        });

        // Check cache first
        if (enableCaching) {
          const cachedResult = await this.getCachedOCRResult(documentId, pageNumber);
          if (cachedResult) {
            documentResult.results.set(pageNumber, cachedResult);
            documentResult.processedPages++;
            onPageComplete?.(cachedResult);
            continue;
          }
        }

        try {
          const canvas = await this.extractPageAsCanvas(pdfDocument, pageNumber);
          imagesToProcess.push({
            data: canvas,
            pageNumber,
            preprocessingOptions,
          });
        } catch (error) {
          console.error(`Failed to extract page ${pageNumber}:`, error);
        }
      }

      // Process images with OCR
      if (imagesToProcess.length > 0) {
        const ocrResults = await this.engine.processBatch(imagesToProcess, {
          config: { language: language || this.config.defaultLanguage },
          onProgress: (batchProgress) => {
            const overallProgress = 20 + (batchProgress.progress * 0.8); // 80% for OCR processing
            onProgress?.({
              ...batchProgress,
              progress: overallProgress,
              totalPages: pages.length,
            });
          },
          onPageComplete: async (result) => {
            documentResult.results.set(result.pageNumber, result);
            documentResult.processedPages++;

            // Cache result
            if (enableCaching) {
              await this.cacheOCRResult(documentId, result);
            }

            onPageComplete?.(result);
          },
        });

        // Update document result
        for (const result of ocrResults) {
          documentResult.results.set(result.pageNumber, result);
        }
      }

      // Calculate metadata
      const allResults = Array.from(documentResult.results.values());
      documentResult.metadata.endTime = new Date();
      documentResult.metadata.totalProcessingTime = 
        documentResult.metadata.endTime.getTime() - documentResult.metadata.startTime.getTime();
      documentResult.metadata.averageConfidence = 
        allResults.reduce((sum, r) => sum + r.confidence, 0) / allResults.length;
      documentResult.metadata.detectedLanguages = 
        [...new Set(allResults.map(r => r.language))];
      documentResult.metadata.status = 'completed';

      onProgress?.({
        status: 'complete',
        progress: 100,
        message: `OCR completed for document ${documentId}`,
        totalPages: pages.length,
      });

      return documentResult;
    } catch (error) {
      documentResult.metadata.status = 'failed';
      onProgress?.({
        status: 'error',
        progress: 0,
        message: `OCR failed for document ${documentId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
      throw error;
    } finally {
      // Keep result for a while, then remove
      setTimeout(() => {
        this.activeJobs.delete(documentId);
      }, 5 * 60 * 1000); // 5 minutes
    }
  }

  /**
   * Process OCR for a single page
   */
  async processPage(
    pdfDocument: unknown,
    pageNumber: number,
    options: {
      documentId?: string;
      language?: string;
      preprocessingOptions?: ImagePreprocessingOptions;
      enableCaching?: boolean;
      onProgress?: (progress: OCRProgress) => void;
    } = {}
  ): Promise<OCRResult> {
    if (!this.engine) {
      throw new Error('OCR manager not initialized');
    }

    const {
      documentId = 'unknown',
      language = this.config.defaultLanguage,
      preprocessingOptions,
      enableCaching = this.config.enableCaching,
      onProgress,
    } = options;

    // Check cache first
    if (enableCaching) {
      const cachedResult = await this.getCachedOCRResult(documentId, pageNumber);
      if (cachedResult) {
        onProgress?.({
          status: 'complete',
          progress: 100,
          message: 'Retrieved from cache',
          pageNumber,
        });
        return cachedResult;
      }
    }

    try {
      onProgress?.({
        status: 'processing',
        progress: 0,
        message: `Extracting page ${pageNumber}...`,
        pageNumber,
      });

      const canvas = await this.extractPageAsCanvas(pdfDocument, pageNumber);

      const result = await this.engine.processImage(canvas, {
        pageNumber,
        preprocessingOptions,
        config: { language },
        onProgress,
      });

      // Cache result
      if (enableCaching) {
        await this.cacheOCRResult(documentId, result);
      }

      return result;
    } catch (error) {
      onProgress?.({
        status: 'error',
        progress: 0,
        message: `Failed to process page ${pageNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`,
        pageNumber,
      });
      throw error;
    }
  }

  /**
   * Add OCR job to queue for background processing
   */
  async queueOCRJob(config: OCRJobConfig): Promise<string> {
    const jobId = `${config.documentId}-${Date.now()}`;
    this.jobQueue.push({ ...config, priority: config.priority || 'normal' });
    
    // Sort queue by priority
    this.jobQueue.sort((a, b) => {
      const priorityOrder = { high: 3, normal: 2, low: 1 };
      return priorityOrder[b.priority || 'normal'] - priorityOrder[a.priority || 'normal'];
    });

    if (this.config.enableBackgroundProcessing && !this.isProcessingQueue) {
      this.processQueue();
    }

    return jobId;
  }

  /**
   * Process queued OCR jobs
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessingQueue || this.jobQueue.length === 0) return;

    this.isProcessingQueue = true;

    while (this.jobQueue.length > 0 && this.activeJobs.size < this.config.maxConcurrentJobs) {
      const job = this.jobQueue.shift();
      if (!job) break;

      try {
        // Process job in background
        this.processDocument(
          null, // Would need to pass actual PDF document
          job,
          undefined, // No progress callback for background jobs
          undefined  // No page complete callback for background jobs
        ).catch(error => {
          console.error(`Background OCR job failed for ${job.documentId}:`, error);
        });
      } catch (error) {
        console.error(`Failed to start background OCR job:`, error);
      }
    }

    this.isProcessingQueue = false;
  }

  /**
   * Extract PDF page as canvas for OCR processing
   */
  private async extractPageAsCanvas(pdfDocument: unknown, pageNumber: number): Promise<HTMLCanvasElement> {
    const page = await pdfDocument.getPage(pageNumber);
    const viewport = page.getViewport({ scale: 2.0 }); // Higher scale for better OCR

    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) {
      throw new Error('Failed to get canvas context');
    }

    canvas.width = viewport.width;
    canvas.height = viewport.height;

    await page.render({
      canvasContext: context,
      viewport: viewport,
    }).promise;

    return canvas;
  }

  /**
   * Cache OCR result
   */
  private async cacheOCRResult(documentId: string, result: OCRResult): Promise<void> {
    try {
      const cacheKey = `ocr-${documentId}-${result.pageNumber}`;
      const cacheData = {
        ...result,
        cachedAt: new Date(),
        expiresAt: new Date(Date.now() + this.config.cacheTimeout),
      };

      // Store in cache manager if available
      if (this.cacheManager) {
        // Use the cache manager's storage
        await this.cacheManager.setPage({
          documentId: `ocr-${documentId}`,
          pageNumber: result.pageNumber,
          scale: 1.0,
          rotation: 0,
          imageData: JSON.stringify(cacheData),
          textContent: result.text,
        });
      } else {
        // Fallback to localStorage
        localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      }
    } catch (error) {
      console.error('Failed to cache OCR result:', error);
    }
  }

  /**
   * Get cached OCR result
   */
  private async getCachedOCRResult(documentId: string, pageNumber: number): Promise<OCRResult | null> {
    try {
      const cacheKey = `ocr-${documentId}-${pageNumber}`;

      // Try cache manager first
      if (this.cacheManager) {
        const cachedPage = await this.cacheManager.getPage(`ocr-${documentId}`, pageNumber);
        if (cachedPage && cachedPage.imageData) {
          const cacheData = JSON.parse(cachedPage.imageData);
          const expiresAt = new Date(cacheData.expiresAt);
          
          if (expiresAt > new Date()) {
            return cacheData as OCRResult;
          }
        }
      } else {
        // Fallback to localStorage
        const cached = localStorage.getItem(cacheKey);
        if (cached) {
          const cacheData = JSON.parse(cached);
          const expiresAt = new Date(cacheData.expiresAt);
          
          if (expiresAt > new Date()) {
            return cacheData as OCRResult;
          } else {
            localStorage.removeItem(cacheKey);
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Failed to get cached OCR result:', error);
      return null;
    }
  }

  /**
   * Clear OCR cache
   */
  async clearCache(documentId?: string): Promise<void> {
    try {
      if (documentId) {
        // Clear cache for specific document
        if (this.cacheManager) {
          await this.cacheManager.clearCache('pages');
        } else {
          // Clear from localStorage
          const keys = Object.keys(localStorage);
          keys.forEach(key => {
            if (key.startsWith(`ocr-${documentId}-`)) {
              localStorage.removeItem(key);
            }
          });
        }
      } else {
        // Clear all OCR cache
        if (this.cacheManager) {
          await this.cacheManager.clearCache('all');
        } else {
          const keys = Object.keys(localStorage);
          keys.forEach(key => {
            if (key.startsWith('ocr-')) {
              localStorage.removeItem(key);
            }
          });
        }
      }
    } catch (error) {
      console.error('Failed to clear OCR cache:', error);
    }
  }

  /**
   * Get OCR statistics
   */
  getStats(): {
    activeJobs: number;
    queuedJobs: number;
    isInitialized: boolean;
    engineStatus: unknown;
  } {
    return {
      activeJobs: this.activeJobs.size,
      queuedJobs: this.jobQueue.length,
      isInitialized: !!this.engine,
      engineStatus: this.engine?.getStatus() || null,
    };
  }

  /**
   * Cancel OCR job
   */
  cancelJob(documentId: string): boolean {
    const jobIndex = this.jobQueue.findIndex(job => job.documentId === documentId);
    if (jobIndex !== -1) {
      this.jobQueue.splice(jobIndex, 1);
      return true;
    }

    const activeJob = this.activeJobs.get(documentId);
    if (activeJob) {
      activeJob.metadata.status = 'cancelled';
      this.activeJobs.delete(documentId);
      return true;
    }

    return false;
  }

  /**
   * Get job status
   */
  getJobStatus(documentId: string): OCRDocumentResult | null {
    return this.activeJobs.get(documentId) || null;
  }

  /**
   * Terminate OCR manager
   */
  async terminate(): Promise<void> {
    if (this.engine) {
      await this.engine.terminate();
      this.engine = null;
    }

    this.activeJobs.clear();
    this.jobQueue = [];
    this.isProcessingQueue = false;
  }
}
