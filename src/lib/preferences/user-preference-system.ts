/**
 * User Preference System
 * Comprehensive user settings with accessibility profiles and preferences management
 */

export interface UserPreferences {
  // General preferences
  general: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    autoSave: boolean;
    autoSaveInterval: number; // minutes
    confirmBeforeExit: boolean;
    showWelcomeScreen: boolean;
    enableAnalytics: boolean;
    enableCrashReporting: boolean;
  };

  // PDF viewer preferences
  viewer: {
    defaultZoom: number;
    defaultViewMode: 'single' | 'continuous' | 'facing';
    enableThumbnails: boolean;
    thumbnailSize: 'small' | 'medium' | 'large';
    enableOutline: boolean;
    enableAnnotations: boolean;
    defaultAnnotationTool: string;
    enableSearch: boolean;
    searchHighlightColor: string;
    pageTransition: 'none' | 'fade' | 'slide';
    enablePageNumbers: boolean;
    enableProgressBar: boolean;
  };

  // Accessibility preferences
  accessibility: {
    enableScreenReader: boolean;
    enableKeyboardNavigation: boolean;
    enableHighContrast: boolean;
    highContrastMode: 'light' | 'dark' | 'custom';
    fontSize: number;
    fontFamily: string;
    lineHeight: number;
    letterSpacing: number;
    enableFocusIndicators: boolean;
    enableReadingGuides: boolean;
    enableTextToSpeech: boolean;
    speechRate: number;
    speechVoice: string;
    enableReducedMotion: boolean;
    colorBlindnessSupport: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  };

  // Performance preferences
  performance: {
    enableVirtualization: boolean;
    cacheSize: number; // MB
    preloadPages: number;
    enableWebWorkers: boolean;
    maxConcurrentWorkers: number;
    enableGPUAcceleration: boolean;
    qualityMode: 'low' | 'medium' | 'high' | 'auto';
    enableProgressiveLoading: boolean;
    compressionLevel: number;
  };

  // Privacy preferences
  privacy: {
    enableLocalStorage: boolean;
    enableCookies: boolean;
    enableSessionStorage: boolean;
    clearDataOnExit: boolean;
    enableEncryption: boolean;
    shareUsageData: boolean;
    enableRemoteSync: boolean;
    dataRetentionDays: number;
  };

  // Collaboration preferences
  collaboration: {
    enableRealTimeSync: boolean;
    enableComments: boolean;
    enableSharing: boolean;
    defaultSharePermissions: 'view' | 'comment' | 'edit';
    enableNotifications: boolean;
    notificationTypes: string[];
    enablePresenceIndicators: boolean;
    autoSaveCollaborativeChanges: boolean;
  };

  // Advanced preferences
  advanced: {
    enableDeveloperMode: boolean;
    enableDebugLogging: boolean;
    logLevel: 'error' | 'warn' | 'info' | 'debug';
    enableExperimentalFeatures: boolean;
    customCSS: string;
    customJavaScript: string;
    enablePlugins: boolean;
    enableCustomShortcuts: boolean;
    backupFrequency: 'never' | 'daily' | 'weekly' | 'monthly';
  };
}

export interface PreferenceProfile {
  id: string;
  name: string;
  description: string;
  category: 'accessibility' | 'performance' | 'general' | 'custom';
  preferences: Partial<UserPreferences>;
  isDefault: boolean;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastUsed: Date;
  usageCount: number;
  tags: string[];
}

export interface PreferenceValidation {
  isValid: boolean;
  errors: Array<{
    path: string;
    message: string;
    severity: 'error' | 'warning';
  }>;
  warnings: Array<{
    path: string;
    message: string;
  }>;
}

export interface PreferenceExport {
  version: string;
  exportDate: Date;
  preferences: UserPreferences;
  profiles: PreferenceProfile[];
  metadata: {
    userAgent: string;
    platform: string;
    version: string;
  };
}

const DEFAULT_PREFERENCES: UserPreferences = {
  general: {
    theme: 'auto',
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    autoSave: true,
    autoSaveInterval: 5,
    confirmBeforeExit: true,
    showWelcomeScreen: true,
    enableAnalytics: false,
    enableCrashReporting: true,
  },
  viewer: {
    defaultZoom: 1.0,
    defaultViewMode: 'continuous',
    enableThumbnails: true,
    thumbnailSize: 'medium',
    enableOutline: true,
    enableAnnotations: true,
    defaultAnnotationTool: 'highlight',
    enableSearch: true,
    searchHighlightColor: '#ffff00',
    pageTransition: 'none',
    enablePageNumbers: true,
    enableProgressBar: true,
  },
  accessibility: {
    enableScreenReader: false,
    enableKeyboardNavigation: true,
    enableHighContrast: false,
    highContrastMode: 'light',
    fontSize: 16,
    fontFamily: 'system-ui',
    lineHeight: 1.4,
    letterSpacing: 0,
    enableFocusIndicators: true,
    enableReadingGuides: false,
    enableTextToSpeech: false,
    speechRate: 1.0,
    speechVoice: '',
    enableReducedMotion: false,
    colorBlindnessSupport: 'none',
  },
  performance: {
    enableVirtualization: true,
    cacheSize: 100,
    preloadPages: 3,
    enableWebWorkers: true,
    maxConcurrentWorkers: 4,
    enableGPUAcceleration: true,
    qualityMode: 'auto',
    enableProgressiveLoading: true,
    compressionLevel: 5,
  },
  privacy: {
    enableLocalStorage: true,
    enableCookies: true,
    enableSessionStorage: true,
    clearDataOnExit: false,
    enableEncryption: false,
    shareUsageData: false,
    enableRemoteSync: false,
    dataRetentionDays: 30,
  },
  collaboration: {
    enableRealTimeSync: false,
    enableComments: true,
    enableSharing: true,
    defaultSharePermissions: 'view',
    enableNotifications: true,
    notificationTypes: ['comments', 'mentions', 'shares'],
    enablePresenceIndicators: true,
    autoSaveCollaborativeChanges: true,
  },
  advanced: {
    enableDeveloperMode: false,
    enableDebugLogging: false,
    logLevel: 'warn',
    enableExperimentalFeatures: false,
    customCSS: '',
    customJavaScript: '',
    enablePlugins: false,
    enableCustomShortcuts: true,
    backupFrequency: 'weekly',
  },
};

const SYSTEM_PROFILES: Omit<PreferenceProfile, 'id' | 'createdAt' | 'updatedAt' | 'lastUsed' | 'usageCount'>[] = [
  {
    name: 'High Accessibility',
    description: 'Optimized for users with accessibility needs',
    category: 'accessibility',
    isDefault: false,
    isSystem: true,
    tags: ['accessibility', 'screen-reader', 'high-contrast'],
    preferences: {
      accessibility: {
        enableScreenReader: true,
        enableKeyboardNavigation: true,
        enableHighContrast: true,
        fontSize: 18,
        lineHeight: 1.6,
        enableFocusIndicators: true,
        enableTextToSpeech: true,
        enableReducedMotion: true,
      },
      viewer: {
        defaultZoom: 1.2,
        pageTransition: 'none',
        searchHighlightColor: '#000000',
      },
    },
  },
  {
    name: 'High Performance',
    description: 'Optimized for speed and responsiveness',
    category: 'performance',
    isDefault: false,
    isSystem: true,
    tags: ['performance', 'speed', 'optimization'],
    preferences: {
      performance: {
        enableVirtualization: true,
        cacheSize: 200,
        preloadPages: 5,
        enableWebWorkers: true,
        maxConcurrentWorkers: 8,
        enableGPUAcceleration: true,
        qualityMode: 'high',
        enableProgressiveLoading: true,
        compressionLevel: 3,
      },
      viewer: {
        enableThumbnails: false,
        pageTransition: 'none',
      },
    },
  },
  {
    name: 'Privacy Focused',
    description: 'Maximum privacy and data protection',
    category: 'general',
    isDefault: false,
    isSystem: true,
    tags: ['privacy', 'security', 'data-protection'],
    preferences: {
      privacy: {
        enableLocalStorage: false,
        enableCookies: false,
        clearDataOnExit: true,
        enableEncryption: true,
        shareUsageData: false,
        enableRemoteSync: false,
        dataRetentionDays: 1,
      },
      general: {
        enableAnalytics: false,
        enableCrashReporting: false,
      },
    },
  },
  {
    name: 'Collaboration Ready',
    description: 'Optimized for team collaboration',
    category: 'general',
    isDefault: false,
    isSystem: true,
    tags: ['collaboration', 'sharing', 'teamwork'],
    preferences: {
      collaboration: {
        enableRealTimeSync: true,
        enableComments: true,
        enableSharing: true,
        defaultSharePermissions: 'comment',
        enableNotifications: true,
        enablePresenceIndicators: true,
        autoSaveCollaborativeChanges: true,
      },
      general: {
        autoSave: true,
        autoSaveInterval: 2,
      },
    },
  },
];

export class UserPreferenceSystem {
  private preferences: UserPreferences;
  private profiles: Map<string, PreferenceProfile> = new Map();
  private currentProfile: PreferenceProfile | null = null;
  private eventListeners: Map<string, Set<(data: any) => void>> = new Map();
  private validationRules: Map<string, (value: any) => PreferenceValidation> = new Map();

  constructor(initialPreferences?: Partial<UserPreferences>) {
    this.preferences = this.mergePreferences(DEFAULT_PREFERENCES, initialPreferences || {});
    
    this.initializeSystemProfiles();
    this.loadUserData();
    this.setupValidationRules();
    this.applyPreferences();
  }

  private mergePreferences(base: UserPreferences, override: Partial<UserPreferences>): UserPreferences {
    const merged = JSON.parse(JSON.stringify(base));
    
    Object.keys(override).forEach(category => {
      if (merged[category as keyof UserPreferences] && override[category as keyof UserPreferences]) {
        Object.assign(merged[category as keyof UserPreferences], override[category as keyof UserPreferences]);
      }
    });
    
    return merged;
  }

  private initializeSystemProfiles(): void {
    SYSTEM_PROFILES.forEach(profile => {
      const fullProfile: PreferenceProfile = {
        ...profile,
        id: `system-${profile.name.toLowerCase().replace(/\s+/g, '-')}`,
        createdAt: new Date(),
        updatedAt: new Date(),
        lastUsed: new Date(),
        usageCount: 0,
      };
      this.profiles.set(fullProfile.id, fullProfile);
    });
  }

  private loadUserData(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      // Load user preferences
      const savedPreferences = localStorage.getItem('user-preferences');
      if (savedPreferences) {
        const parsed = JSON.parse(savedPreferences);
        if (parsed && typeof parsed === 'object') {
          this.preferences = this.mergePreferences(this.preferences, parsed);
        }
      }

      // Load user profiles
      const savedProfiles = localStorage.getItem('user-preference-profiles');
      if (savedProfiles) {
        const profiles = JSON.parse(savedProfiles);
        if (Array.isArray(profiles)) {
          profiles.forEach((profile: any) => {
            if (profile && typeof profile === 'object') {
              this.profiles.set(profile.id, {
                ...profile,
                createdAt: new Date(profile.createdAt || Date.now()),
                updatedAt: new Date(profile.updatedAt || Date.now()),
                lastUsed: new Date(profile.lastUsed || Date.now()),
              });
            }
          });
        }
      }

      // Load current profile
      const currentProfileId = localStorage.getItem('current-preference-profile');
      if (currentProfileId && this.profiles.has(currentProfileId)) {
        this.currentProfile = this.profiles.get(currentProfileId)!;
      }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  private saveUserData(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      localStorage.setItem('user-preferences', JSON.stringify(this.preferences));

      const userProfiles = Array.from(this.profiles.values()).filter(p => !p.isSystem);
      localStorage.setItem('user-preference-profiles', JSON.stringify(userProfiles));

      if (this.currentProfile) {
        localStorage.setItem('current-preference-profile', this.currentProfile.id);
      }
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  private setupValidationRules(): void {
    // Add validation rules for different preference types
    this.validationRules.set('general.autoSaveInterval', (value: number) => ({
      isValid: value >= 1 && value <= 60,
      errors: value < 1 || value > 60 ? [{ 
        path: 'general.autoSaveInterval', 
        message: 'Auto-save interval must be between 1 and 60 minutes',
        severity: 'error' as const
      }] : [],
      warnings: [],
    }));

    this.validationRules.set('performance.cacheSize', (value: number) => ({
      isValid: value >= 10 && value <= 1000,
      errors: value < 10 || value > 1000 ? [{ 
        path: 'performance.cacheSize', 
        message: 'Cache size must be between 10 and 1000 MB',
        severity: 'error' as const
      }] : [],
      warnings: value > 500 ? [{ 
        path: 'performance.cacheSize', 
        message: 'Large cache sizes may impact system performance'
      }] : [],
    }));

    this.validationRules.set('accessibility.fontSize', (value: number) => ({
      isValid: value >= 8 && value <= 72,
      errors: value < 8 || value > 72 ? [{ 
        path: 'accessibility.fontSize', 
        message: 'Font size must be between 8 and 72 pixels',
        severity: 'error' as const
      }] : [],
      warnings: [],
    }));
  }

  private applyPreferences(): void {
    if (typeof document === 'undefined' || typeof window === 'undefined') return;

    // Apply theme
    if (this.preferences.general.theme !== 'auto') {
      document.documentElement.setAttribute('data-theme', this.preferences.general.theme);
    } else {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      document.documentElement.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    }

    // Apply accessibility preferences
    if (this.preferences.accessibility.enableReducedMotion) {
      document.documentElement.setAttribute('data-reduced-motion', 'true');
    }

    if (this.preferences.accessibility.enableHighContrast) {
      document.documentElement.setAttribute('data-high-contrast', this.preferences.accessibility.highContrastMode);
    }

    // Apply custom CSS
    if (this.preferences.advanced.customCSS) {
      this.applyCustomCSS(this.preferences.advanced.customCSS);
    }

    this.emit('preferences-applied', { preferences: this.preferences });
  }

  private applyCustomCSS(css: string): void {
    if (typeof document === 'undefined') return;

    let styleElement = document.getElementById('user-custom-css') as HTMLStyleElement;
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = 'user-custom-css';
      document.head.appendChild(styleElement);
    }
    styleElement.textContent = css;
  }

  public updatePreferences(updates: Partial<UserPreferences>): PreferenceValidation {
    const validation = this.validatePreferences(updates);
    
    if (validation.isValid) {
      this.preferences = this.mergePreferences(this.preferences, updates);
      this.applyPreferences();
      this.saveUserData();
      this.emit('preferences-updated', { preferences: this.preferences, updates });
    }
    
    return validation;
  }

  public validatePreferences(preferences: Partial<UserPreferences>): PreferenceValidation {
    const validation: PreferenceValidation = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    const flattenObject = (obj: any, prefix = ''): Array<{ path: string; value: any }> => {
      const result: Array<{ path: string; value: any }> = [];
      
      Object.keys(obj).forEach(key => {
        const path = prefix ? `${prefix}.${key}` : key;
        const value = obj[key];
        
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          result.push(...flattenObject(value, path));
        } else {
          result.push({ path, value });
        }
      });
      
      return result;
    };

    const flatPreferences = flattenObject(preferences);
    
    flatPreferences.forEach(({ path, value }) => {
      const rule = this.validationRules.get(path);
      if (rule) {
        const result = rule(value);
        validation.errors.push(...result.errors);
        validation.warnings.push(...result.warnings);
        
        if (!result.isValid) {
          validation.isValid = false;
        }
      }
    });

    return validation;
  }

  public createProfile(name: string, description: string, category: PreferenceProfile['category'] = 'custom'): string {
    const profileId = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const profile: PreferenceProfile = {
      id: profileId,
      name,
      description,
      category,
      preferences: JSON.parse(JSON.stringify(this.preferences)),
      isDefault: false,
      isSystem: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      lastUsed: new Date(),
      usageCount: 0,
      tags: [],
    };

    this.profiles.set(profileId, profile);
    this.saveUserData();
    this.emit('profile-created', { profile });
    
    return profileId;
  }

  public loadProfile(profileId: string): boolean {
    const profile = this.profiles.get(profileId);
    if (!profile) return false;

    this.preferences = this.mergePreferences(DEFAULT_PREFERENCES, profile.preferences);
    this.currentProfile = profile;
    
    profile.lastUsed = new Date();
    profile.usageCount++;
    
    this.applyPreferences();
    this.saveUserData();
    this.emit('profile-loaded', { profile });
    
    return true;
  }

  public deleteProfile(profileId: string): boolean {
    const profile = this.profiles.get(profileId);
    if (!profile || profile.isSystem) return false;
    
    const deleted = this.profiles.delete(profileId);
    if (deleted) {
      if (this.currentProfile?.id === profileId) {
        this.currentProfile = null;
      }
      this.saveUserData();
      this.emit('profile-deleted', { profileId });
    }
    
    return deleted;
  }

  public exportPreferences(): PreferenceExport {
    return {
      version: '1.0',
      exportDate: new Date(),
      preferences: this.preferences,
      profiles: Array.from(this.profiles.values()).filter(p => !p.isSystem),
      metadata: {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        version: '1.0.0',
      },
    };
  }

  public importPreferences(exportData: PreferenceExport): boolean {
    try {
      const validation = this.validatePreferences(exportData.preferences);
      if (!validation.isValid) {
        this.emit('import-error', { errors: validation.errors });
        return false;
      }

      this.preferences = exportData.preferences;
      
      exportData.profiles.forEach(profile => {
        const newId = `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        this.profiles.set(newId, {
          ...profile,
          id: newId,
          isSystem: false,
          createdAt: new Date(),
          updatedAt: new Date(),
          lastUsed: new Date(),
          usageCount: 0,
        });
      });

      this.applyPreferences();
      this.saveUserData();
      this.emit('preferences-imported', { exportData });
      
      return true;
    } catch (error) {
      this.emit('import-error', { error });
      return false;
    }
  }

  public resetToDefaults(): void {
    this.preferences = JSON.parse(JSON.stringify(DEFAULT_PREFERENCES));
    this.currentProfile = null;
    this.applyPreferences();
    this.saveUserData();
    this.emit('preferences-reset', {});
  }

  public getPreferences(): UserPreferences {
    return JSON.parse(JSON.stringify(this.preferences));
  }

  public getProfiles(): PreferenceProfile[] {
    return Array.from(this.profiles.values()).sort((a, b) => 
      b.lastUsed.getTime() - a.lastUsed.getTime()
    );
  }

  public getCurrentProfile(): PreferenceProfile | null {
    return this.currentProfile;
  }

  public addEventListener(type: string, listener: (data: any) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: any) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: any): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Preference system event listener error:', error);
      }
    });
  }

  public destroy(): void {
    const customCSS = document.getElementById('user-custom-css');
    if (customCSS) {
      customCSS.remove();
    }
    
    this.eventListeners.clear();
  }
}
