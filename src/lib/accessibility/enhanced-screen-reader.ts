/**
 * Enhanced Screen Reader Support
 * Advanced ARIA implementation and screen reader optimization for PDF content
 */

export interface ScreenReaderConfig {
  enableLiveRegions: boolean;
  enableLandmarks: boolean;
  enableStructuralNavigation: boolean;
  enableContentDescription: boolean;
  enableProgressAnnouncements: boolean;
  verbosityLevel: 'minimal' | 'standard' | 'verbose';
  announcementDelay: number;
  enableSpatialNavigation: boolean;
  enableTableNavigation: boolean;
  enableFormNavigation: boolean;
}

export interface ContentStructure {
  type: 'heading' | 'paragraph' | 'list' | 'table' | 'image' | 'link' | 'form' | 'landmark';
  level?: number;
  text: string;
  position: { x: number; y: number; width: number; height: number };
  pageNumber: number;
  attributes: Record<string, string>;
  children?: ContentStructure[];
  parent?: ContentStructure;
}

export interface NavigationContext {
  currentElement: ContentStructure | null;
  previousElement: ContentStructure | null;
  nextElement: ContentStructure | null;
  parentElement: ContentStructure | null;
  childElements: ContentStructure[];
  siblingElements: ContentStructure[];
  breadcrumb: ContentStructure[];
}

export interface ScreenReaderAnnouncement {
  id: string;
  type: 'navigation' | 'content' | 'status' | 'error' | 'progress';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  message: string;
  timestamp: Date;
  context?: unknown;
}

const DEFAULT_CONFIG: ScreenReaderConfig = {
  enableLiveRegions: true,
  enableLandmarks: true,
  enableStructuralNavigation: true,
  enableContentDescription: true,
  enableProgressAnnouncements: true,
  verbosityLevel: 'standard',
  announcementDelay: 100,
  enableSpatialNavigation: true,
  enableTableNavigation: true,
  enableFormNavigation: true,
};

export class EnhancedScreenReaderSupport {
  private config: ScreenReaderConfig;
  private contentStructure: Map<number, ContentStructure[]> = new Map(); // pageNumber -> structures
  private navigationContext: NavigationContext;
  private liveRegions: Map<string, HTMLElement> = new Map();
  private announcementQueue: ScreenReaderAnnouncement[] = [];
  private isProcessingAnnouncements = false;
  private currentFocus: ContentStructure | null = null;
  private eventListeners: Map<string, Set<(data: unknown) => void>> = new Map();

  constructor(config: Partial<ScreenReaderConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.navigationContext = {
      currentElement: null,
      previousElement: null,
      nextElement: null,
      parentElement: null,
      childElements: [],
      siblingElements: [],
      breadcrumb: [],
    };

    this.setupLiveRegions();
    this.setupKeyboardHandlers();
  }

  private setupLiveRegions(): void {
    if (!this.config.enableLiveRegions || typeof document === 'undefined') return;

    // Create ARIA live regions for different types of announcements
    const liveRegionTypes = [
      { id: 'sr-status', role: 'status', priority: 'polite' },
      { id: 'sr-alert', role: 'alert', priority: 'assertive' },
      { id: 'sr-log', role: 'log', priority: 'polite' },
      { id: 'sr-progress', role: 'progressbar', priority: 'polite' },
    ];

    liveRegionTypes.forEach(({ id, role, priority }) => {
      let region = document.getElementById(id);
      if (!region) {
        region = document.createElement('div');
        region.id = id;
        region.setAttribute('role', role);
        region.setAttribute('aria-live', priority);
        region.setAttribute('aria-atomic', 'true');
        region.className = 'sr-only';
        region.style.cssText = `
          position: absolute !important;
          width: 1px !important;
          height: 1px !important;
          padding: 0 !important;
          margin: -1px !important;
          overflow: hidden !important;
          clip: rect(0, 0, 0, 0) !important;
          white-space: nowrap !important;
          border: 0 !important;
        `;
        document.body.appendChild(region);
      }
      this.liveRegions.set(id, region);
    });
  }

  private setupKeyboardHandlers(): void {
    if (!this.config.enableStructuralNavigation || typeof document === 'undefined') return;

    document.addEventListener('keydown', (event) => {
      // Only handle if screen reader navigation is active
      if (!this.isScreenReaderActive()) return;

      const { key, ctrlKey, shiftKey, altKey } = event;

      // Structural navigation shortcuts
      if (altKey) {
        switch (key.toLowerCase()) {
          case 'h':
            event.preventDefault();
            this.navigateToNextHeading(shiftKey);
            break;
          case 'p':
            event.preventDefault();
            this.navigateToNextParagraph(shiftKey);
            break;
          case 'l':
            event.preventDefault();
            this.navigateToNextList(shiftKey);
            break;
          case 't':
            event.preventDefault();
            this.navigateToNextTable(shiftKey);
            break;
          case 'g':
            event.preventDefault();
            this.navigateToNextGraphic(shiftKey);
            break;
          case 'f':
            event.preventDefault();
            this.navigateToNextForm(shiftKey);
            break;
          case 'k':
            event.preventDefault();
            this.navigateToNextLink(shiftKey);
            break;
          case 'm':
            event.preventDefault();
            this.navigateToNextLandmark(shiftKey);
            break;
        }
      }

      // Table navigation
      if (this.currentFocus?.type === 'table' && ctrlKey && altKey) {
        switch (key) {
          case 'ArrowUp':
            event.preventDefault();
            this.navigateTableCell('up');
            break;
          case 'ArrowDown':
            event.preventDefault();
            this.navigateTableCell('down');
            break;
          case 'ArrowLeft':
            event.preventDefault();
            this.navigateTableCell('left');
            break;
          case 'ArrowRight':
            event.preventDefault();
            this.navigateTableCell('right');
            break;
          case 'Home':
            event.preventDefault();
            this.navigateTableCell('first-cell');
            break;
          case 'End':
            event.preventDefault();
            this.navigateTableCell('last-cell');
            break;
        }
      }
    });
  }

  public extractContentStructure(pageNumber: number, textContent: unknown, viewport: unknown): void {
    const structures: ContentStructure[] = [];
    
    if (!textContent || !textContent.items) return;

    // Group text items into logical structures
    const textItems = textContent.items;
    let currentStructure: ContentStructure | null = null;

    for (let i = 0; i < textItems.length; i++) {
      const item = textItems[i];
      const text = item.str?.trim();
      
      if (!text) continue;

      const position = {
        x: item.transform[4],
        y: item.transform[5],
        width: item.width || 0,
        height: item.height || 0,
      };

      // Detect structure type based on text properties
      const structureType = this.detectStructureType(item, text, textItems, i);
      
      if (structureType) {
        // Create new structure
        const structure: ContentStructure = {
          type: structureType.type,
          level: structureType.level,
          text,
          position,
          pageNumber,
          attributes: this.generateAriaAttributes(structureType, text, position),
          children: [],
        };

        structures.push(structure);
        currentStructure = structure;
      } else if (currentStructure && currentStructure.type === 'paragraph') {
        // Append to current paragraph
        currentStructure.text += ' ' + text;
      } else {
        // Create new paragraph
        currentStructure = {
          type: 'paragraph',
          text,
          position,
          pageNumber,
          attributes: this.generateAriaAttributes({ type: 'paragraph' }, text, position),
          children: [],
        };
        structures.push(currentStructure);
      }
    }

    // Build hierarchical structure
    this.buildHierarchy(structures);
    
    // Store structures for this page
    this.contentStructure.set(pageNumber, structures);
    
    this.emit('structure-extracted', { pageNumber, structures });
  }

  private detectStructureType(item: unknown, text: string, allItems: unknown[], index: number):
    { type: ContentStructure['type']; level?: number } | null {
    
    // Heading detection (based on font size, weight, position)
    const fontSize = item.height || 12;
    const isLargeFont = fontSize > 16;
    const isBold = item.fontName?.includes('Bold') || false;
    const isAtLineStart = index === 0 || allItems[index - 1].transform[5] !== item.transform[5];
    
    if (isLargeFont && isBold && isAtLineStart) {
      const level = Math.min(6, Math.max(1, Math.floor((24 - fontSize) / 2) + 1));
      return { type: 'heading', level };
    }

    // List detection
    if (text.match(/^[\u2022\u2023\u25E6\u2043\u2219\-\*]\s/) || text.match(/^\d+\.\s/)) {
      return { type: 'list' };
    }

    // Link detection
    if (item.fontName?.includes('Underline') || text.match(/https?:\/\//)) {
      return { type: 'link' };
    }

    // Table detection (simplified)
    if (text.includes('\t') || (index > 0 && Math.abs(item.transform[4] - allItems[index - 1].transform[4]) > 100)) {
      return { type: 'table' };
    }

    return null;
  }

  private generateAriaAttributes(
    structureType: { type: ContentStructure['type']; level?: number },
    text: string,
    position: unknown
  ): Record<string, string> {
    const attributes: Record<string, string> = {};

    switch (structureType.type) {
      case 'heading':
        attributes['role'] = 'heading';
        attributes['aria-level'] = String(structureType.level || 1);
        attributes['aria-label'] = `Heading level ${structureType.level}: ${text}`;
        break;
        
      case 'paragraph':
        attributes['role'] = 'paragraph';
        attributes['aria-label'] = `Paragraph: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`;
        break;
        
      case 'list':
        attributes['role'] = 'list';
        attributes['aria-label'] = `List item: ${text}`;
        break;
        
      case 'table':
        attributes['role'] = 'table';
        attributes['aria-label'] = `Table content: ${text}`;
        break;
        
      case 'image':
        attributes['role'] = 'img';
        attributes['aria-label'] = text || 'Image';
        break;
        
      case 'link':
        attributes['role'] = 'link';
        attributes['aria-label'] = `Link: ${text}`;
        break;
        
      case 'form':
        attributes['role'] = 'form';
        attributes['aria-label'] = `Form element: ${text}`;
        break;
        
      case 'landmark':
        attributes['role'] = 'region';
        attributes['aria-label'] = text;
        break;
    }

    // Add position information for spatial navigation
    if (this.config.enableSpatialNavigation) {
      attributes['data-x'] = String(position.x);
      attributes['data-y'] = String(position.y);
      attributes['data-width'] = String(position.width);
      attributes['data-height'] = String(position.height);
    }

    return attributes;
  }

  private buildHierarchy(structures: ContentStructure[]): void {
    const stack: ContentStructure[] = [];

    for (const structure of structures) {
      if (structure.type === 'heading') {
        // Pop elements from stack until we find a parent heading
        while (stack.length > 0) {
          const top = stack[stack.length - 1];
          if (top.type === 'heading' && (top.level || 0) < (structure.level || 0)) {
            break;
          }
          stack.pop();
        }
        
        // Set parent relationship
        if (stack.length > 0) {
          structure.parent = stack[stack.length - 1];
          stack[stack.length - 1].children!.push(structure);
        }
        
        stack.push(structure);
      } else {
        // Add to current heading's children
        if (stack.length > 0) {
          structure.parent = stack[stack.length - 1];
          stack[stack.length - 1].children!.push(structure);
        }
      }
    }
  }

  public navigateToNextHeading(reverse = false): void {
    this.navigateToNextStructure('heading', reverse);
  }

  public navigateToNextParagraph(reverse = false): void {
    this.navigateToNextStructure('paragraph', reverse);
  }

  public navigateToNextList(reverse = false): void {
    this.navigateToNextStructure('list', reverse);
  }

  public navigateToNextTable(reverse = false): void {
    this.navigateToNextStructure('table', reverse);
  }

  public navigateToNextGraphic(reverse = false): void {
    this.navigateToNextStructure('image', reverse);
  }

  public navigateToNextForm(reverse = false): void {
    this.navigateToNextStructure('form', reverse);
  }

  public navigateToNextLink(reverse = false): void {
    this.navigateToNextStructure('link', reverse);
  }

  public navigateToNextLandmark(reverse = false): void {
    this.navigateToNextStructure('landmark', reverse);
  }

  private navigateToNextStructure(type: ContentStructure['type'], reverse = false): void {
    const allStructures = this.getAllStructures();
    const filtered = allStructures.filter(s => s.type === type);
    
    if (filtered.length === 0) {
      this.announce(`No ${type}s found in document`, 'status');
      return;
    }

    let currentIndex = -1;
    if (this.currentFocus) {
      currentIndex = filtered.findIndex(s => s === this.currentFocus);
    }

    const nextIndex = reverse 
      ? (currentIndex <= 0 ? filtered.length - 1 : currentIndex - 1)
      : (currentIndex >= filtered.length - 1 ? 0 : currentIndex + 1);

    const targetStructure = filtered[nextIndex];
    this.focusStructure(targetStructure);
  }

  private navigateTableCell(direction: 'up' | 'down' | 'left' | 'right' | 'first-cell' | 'last-cell'): void {
    // Simplified table navigation - would need more sophisticated implementation
    this.announce(`Table navigation: ${direction}`, 'status');
  }

  private focusStructure(structure: ContentStructure): void {
    this.currentFocus = structure;
    this.updateNavigationContext(structure);
    
    // Announce the structure
    const announcement = this.generateStructureAnnouncement(structure);
    this.announce(announcement, 'navigation');
    
    // Emit navigation event
    this.emit('structure-focused', { structure, context: this.navigationContext });
  }

  private updateNavigationContext(structure: ContentStructure): void {
    this.navigationContext.previousElement = this.navigationContext.currentElement;
    this.navigationContext.currentElement = structure;
    this.navigationContext.parentElement = structure.parent || null;
    this.navigationContext.childElements = structure.children || [];
    
    // Find siblings
    if (structure.parent) {
      this.navigationContext.siblingElements = structure.parent.children || [];
    } else {
      const pageStructures = this.contentStructure.get(structure.pageNumber) || [];
      this.navigationContext.siblingElements = pageStructures.filter(s => !s.parent);
    }

    // Build breadcrumb
    this.navigationContext.breadcrumb = [];
    let current = structure.parent;
    while (current) {
      this.navigationContext.breadcrumb.unshift(current);
      current = current.parent;
    }
  }

  private generateStructureAnnouncement(structure: ContentStructure): string {
    let announcement = '';

    switch (this.config.verbosityLevel) {
      case 'minimal':
        announcement = structure.text;
        break;
        
      case 'standard':
        announcement = `${structure.type}${structure.level ? ` level ${structure.level}` : ''}: ${structure.text}`;
        break;
        
      case 'verbose':
        const position = `page ${structure.pageNumber}`;
        const hierarchy = this.navigationContext.breadcrumb.length > 0 
          ? `, in ${this.navigationContext.breadcrumb.map(b => b.text).join(', ')}`
          : '';
        announcement = `${structure.type}${structure.level ? ` level ${structure.level}` : ''} on ${position}${hierarchy}: ${structure.text}`;
        break;
    }

    return announcement;
  }

  private getAllStructures(): ContentStructure[] {
    const allStructures: ContentStructure[] = [];
    for (const structures of this.contentStructure.values()) {
      allStructures.push(...structures);
    }
    return allStructures.sort((a, b) => {
      if (a.pageNumber !== b.pageNumber) {
        return a.pageNumber - b.pageNumber;
      }
      return a.position.y - b.position.y;
    });
  }

  public announce(message: string, type: ScreenReaderAnnouncement['type'], priority: ScreenReaderAnnouncement['priority'] = 'medium'): void {
    const announcement: ScreenReaderAnnouncement = {
      id: `announcement-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      priority,
      message,
      timestamp: new Date(),
    };

    this.announcementQueue.push(announcement);
    this.processAnnouncementQueue();
  }

  private async processAnnouncementQueue(): Promise<void> {
    if (this.isProcessingAnnouncements || this.announcementQueue.length === 0) return;

    this.isProcessingAnnouncements = true;

    while (this.announcementQueue.length > 0) {
      // Sort by priority
      this.announcementQueue.sort((a, b) => {
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      });

      const announcement = this.announcementQueue.shift()!;
      await this.deliverAnnouncement(announcement);
    }

    this.isProcessingAnnouncements = false;
  }

  private async deliverAnnouncement(announcement: ScreenReaderAnnouncement): Promise<void> {
    const regionId = announcement.priority === 'urgent' || announcement.type === 'error' 
      ? 'sr-alert' 
      : 'sr-status';
    
    const region = this.liveRegions.get(regionId);
    if (region) {
      region.textContent = announcement.message;
      
      // Clear after announcement
      setTimeout(() => {
        region.textContent = '';
      }, 1000);
    }

    // Add delay between announcements
    if (this.config.announcementDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, this.config.announcementDelay));
    }
  }

  private isScreenReaderActive(): boolean {
    // Detect if screen reader is active
    if (typeof window === 'undefined' || typeof document === 'undefined') return false;

    return window.navigator.userAgent.includes('NVDA') ||
           window.navigator.userAgent.includes('JAWS') ||
           window.speechSynthesis !== undefined ||
           document.querySelector('[aria-live]') !== null;
  }

  public getNavigationContext(): NavigationContext {
    return { ...this.navigationContext };
  }

  public getContentStructure(pageNumber?: number): ContentStructure[] {
    if (pageNumber !== undefined) {
      return this.contentStructure.get(pageNumber) || [];
    }
    return this.getAllStructures();
  }

  public addEventListener(type: string, listener: (data: unknown) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: unknown) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: unknown): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Screen reader event listener error:', error);
      }
    });
  }

  public destroy(): void {
    this.contentStructure.clear();
    this.announcementQueue = [];
    this.eventListeners.clear();
    
    // Remove live regions
    this.liveRegions.forEach(region => {
      region.remove();
    });
    this.liveRegions.clear();
  }
}
