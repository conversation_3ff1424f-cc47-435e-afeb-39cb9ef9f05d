/**
 * Semantic Search Engine
 * Advanced semantic search using embeddings for content-based document discovery
 */

export interface SemanticSearchConfig {
  enableEmbeddings: boolean;
  embeddingModel: 'sentence-transformers' | 'openai' | 'local';
  apiKey?: string;
  maxEmbeddingLength: number;
  similarityThreshold: number;
  maxResults: number;
  enableCaching: boolean;
  chunkSize: number;
  overlapSize: number;
}

export interface DocumentChunk {
  id: string;
  documentId: string;
  pageNumber: number;
  text: string;
  embedding?: number[];
  metadata: {
    startIndex: number;
    endIndex: number;
    type: 'paragraph' | 'heading' | 'list' | 'table' | 'caption';
    level?: number;
  };
}

export interface SemanticSearchResult {
  chunk: DocumentChunk;
  similarity: number;
  relevanceScore: number;
  context: string;
  highlights: string[];
}

export interface ConceptualQuery {
  query: string;
  concepts: string[];
  intent: 'search' | 'summarize' | 'compare' | 'analyze';
  filters?: {
    documentTypes?: string[];
    dateRange?: { start: Date; end: Date };
    authors?: string[];
    topics?: string[];
  };
}

const DEFAULT_CONFIG: SemanticSearchConfig = {
  enableEmbeddings: true,
  embeddingModel: 'sentence-transformers',
  maxEmbeddingLength: 512,
  similarityThreshold: 0.7,
  maxResults: 50,
  enableCaching: true,
  chunkSize: 500,
  overlapSize: 50,
};

export class SemanticSearchEngine {
  private config: SemanticSearchConfig;
  private chunks: Map<string, DocumentChunk> = new Map();
  private embeddings: Map<string, number[]> = new Map();
  private documentIndex: Map<string, string[]> = new Map(); // documentId -> chunkIds
  private conceptIndex: Map<string, Set<string>> = new Map(); // concept -> chunkIds
  private embeddingWorker: Worker | null = null;

  constructor(config: Partial<SemanticSearchConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeEmbeddingWorker();
  }

  private initializeEmbeddingWorker(): void {
    if (!this.config.enableEmbeddings) return;

    try {
      this.embeddingWorker = new Worker('/embedding-worker.js');
      this.embeddingWorker.onmessage = this.handleWorkerMessage.bind(this);
      this.embeddingWorker.onerror = (error) => {
        console.error('Embedding worker error:', error);
        this.embeddingWorker = null;
      };
    } catch (error) {
      console.warn('Failed to initialize embedding worker:', error);
    }
  }

  private handleWorkerMessage(event: MessageEvent): void {
    const { type, chunkId, embedding, error } = event.data;
    
    if (type === 'embedding-complete') {
      if (embedding && chunkId) {
        this.embeddings.set(chunkId, embedding);
        this.updateConceptIndex(chunkId);
      }
    } else if (type === 'embedding-error') {
      console.error('Embedding generation error:', error);
    }
  }

  public async indexDocument(documentId: string, text: string, metadata: any = {}): Promise<void> {
    const chunks = this.createTextChunks(documentId, text, metadata);
    const chunkIds: string[] = [];

    for (const chunk of chunks) {
      this.chunks.set(chunk.id, chunk);
      chunkIds.push(chunk.id);
      
      // Generate embeddings
      if (this.config.enableEmbeddings) {
        await this.generateEmbedding(chunk);
      }
    }

    this.documentIndex.set(documentId, chunkIds);
  }

  private createTextChunks(documentId: string, text: string, metadata: any): DocumentChunk[] {
    const chunks: DocumentChunk[] = [];
    const sentences = this.splitIntoSentences(text);
    let currentChunk = '';
    let chunkIndex = 0;
    let startIndex = 0;

    for (let i = 0; i < sentences.length; i++) {
      const sentence = sentences[i];
      const potentialChunk = currentChunk + (currentChunk ? ' ' : '') + sentence;

      if (potentialChunk.length > this.config.chunkSize && currentChunk) {
        // Create chunk
        const chunk: DocumentChunk = {
          id: `${documentId}-chunk-${chunkIndex}`,
          documentId,
          pageNumber: metadata.pageNumber || 1,
          text: currentChunk,
          metadata: {
            startIndex,
            endIndex: startIndex + currentChunk.length,
            type: this.detectTextType(currentChunk),
          },
        };
        chunks.push(chunk);

        // Start new chunk with overlap
        const overlapSentences = sentences.slice(Math.max(0, i - 2), i);
        currentChunk = overlapSentences.join(' ') + ' ' + sentence;
        startIndex += currentChunk.length - this.config.overlapSize;
        chunkIndex++;
      } else {
        currentChunk = potentialChunk;
      }
    }

    // Add final chunk
    if (currentChunk) {
      const chunk: DocumentChunk = {
        id: `${documentId}-chunk-${chunkIndex}`,
        documentId,
        pageNumber: metadata.pageNumber || 1,
        text: currentChunk,
        metadata: {
          startIndex,
          endIndex: startIndex + currentChunk.length,
          type: this.detectTextType(currentChunk),
        },
      };
      chunks.push(chunk);
    }

    return chunks;
  }

  private splitIntoSentences(text: string): string[] {
    // Simple sentence splitting - could be enhanced with NLP libraries
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0);
  }

  private detectTextType(text: string): DocumentChunk['metadata']['type'] {
    // Simple heuristics for text type detection
    if (text.match(/^#{1,6}\s/)) return 'heading';
    if (text.match(/^\s*[-*+]\s/)) return 'list';
    if (text.includes('\t') || text.match(/\|\s*\w+\s*\|/)) return 'table';
    if (text.length < 100 && text.match(/^(Figure|Table|Chart)\s+\d+/i)) return 'caption';
    return 'paragraph';
  }

  private async generateEmbedding(chunk: DocumentChunk): Promise<void> {
    if (!this.embeddingWorker) {
      // Fallback to simple word-based embeddings
      this.embeddings.set(chunk.id, this.generateSimpleEmbedding(chunk.text));
      return;
    }

    this.embeddingWorker.postMessage({
      type: 'generate-embedding',
      chunkId: chunk.id,
      text: chunk.text,
      model: this.config.embeddingModel,
      apiKey: this.config.apiKey,
    });
  }

  private generateSimpleEmbedding(text: string): number[] {
    // Simple TF-IDF-like embedding for fallback
    const words = text.toLowerCase().split(/\W+/).filter(w => w.length > 2);
    const wordCounts = new Map<string, number>();
    
    words.forEach(word => {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    });

    // Create a simple 100-dimensional embedding
    const embedding = new Array(100).fill(0);
    for (const [word, count] of wordCounts) {
      const hash = this.simpleHash(word) % 100;
      embedding[hash] += count / words.length;
    }

    return embedding;
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private updateConceptIndex(chunkId: string): void {
    const chunk = this.chunks.get(chunkId);
    if (!chunk) return;

    const concepts = this.extractConcepts(chunk.text);
    concepts.forEach(concept => {
      if (!this.conceptIndex.has(concept)) {
        this.conceptIndex.set(concept, new Set());
      }
      this.conceptIndex.get(concept)!.add(chunkId);
    });
  }

  private extractConcepts(text: string): string[] {
    // Simple concept extraction - could be enhanced with NER
    const words = text.toLowerCase().split(/\W+/);
    const concepts: string[] = [];
    
    // Extract potential concepts (capitalized words, technical terms, etc.)
    const originalWords = text.split(/\W+/);
    originalWords.forEach(word => {
      if (word.length > 3 && /^[A-Z]/.test(word)) {
        concepts.push(word.toLowerCase());
      }
    });

    // Extract noun phrases (simple heuristic)
    for (let i = 0; i < words.length - 1; i++) {
      if (words[i].length > 3 && words[i + 1].length > 3) {
        concepts.push(`${words[i]} ${words[i + 1]}`);
      }
    }

    return [...new Set(concepts)];
  }

  public async semanticSearch(query: ConceptualQuery): Promise<SemanticSearchResult[]> {
    const queryEmbedding = await this.getQueryEmbedding(query.query);
    const results: SemanticSearchResult[] = [];

    // Find similar chunks using embeddings
    for (const [chunkId, chunk] of this.chunks) {
      const chunkEmbedding = this.embeddings.get(chunkId);
      if (!chunkEmbedding) continue;

      const similarity = this.cosineSimilarity(queryEmbedding, chunkEmbedding);
      
      if (similarity >= this.config.similarityThreshold) {
        const relevanceScore = this.calculateRelevanceScore(chunk, query, similarity);
        
        results.push({
          chunk,
          similarity,
          relevanceScore,
          context: this.generateContext(chunk),
          highlights: this.generateHighlights(chunk.text, query.concepts),
        });
      }
    }

    // Sort by relevance score
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    return results.slice(0, this.config.maxResults);
  }

  private async getQueryEmbedding(query: string): Promise<number[]> {
    if (this.embeddingWorker) {
      return new Promise((resolve) => {
        const tempId = `query-${Date.now()}`;
        
        const handler = (event: MessageEvent) => {
          if (event.data.chunkId === tempId) {
            this.embeddingWorker!.removeEventListener('message', handler);
            resolve(event.data.embedding || this.generateSimpleEmbedding(query));
          }
        };
        
        this.embeddingWorker.addEventListener('message', handler);
        this.embeddingWorker.postMessage({
          type: 'generate-embedding',
          chunkId: tempId,
          text: query,
          model: this.config.embeddingModel,
          apiKey: this.config.apiKey,
        });
      });
    }
    
    return this.generateSimpleEmbedding(query);
  }

  private cosineSimilarity(a: number[], b: number[]): number {
    if (a.length !== b.length) return 0;
    
    let dotProduct = 0;
    let normA = 0;
    let normB = 0;
    
    for (let i = 0; i < a.length; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  private calculateRelevanceScore(
    chunk: DocumentChunk,
    query: ConceptualQuery,
    similarity: number
  ): number {
    let score = similarity * 0.6; // Base similarity weight
    
    // Boost for concept matches
    const conceptMatches = query.concepts.filter(concept =>
      chunk.text.toLowerCase().includes(concept.toLowerCase())
    ).length;
    score += (conceptMatches / query.concepts.length) * 0.3;
    
    // Boost for text type relevance
    if (query.intent === 'summarize' && chunk.metadata.type === 'heading') {
      score += 0.1;
    }
    
    // Boost for document metadata matches
    if (query.filters) {
      // Add metadata-based scoring here
    }
    
    return Math.min(1, score);
  }

  private generateContext(chunk: DocumentChunk): string {
    const chunkIds = this.documentIndex.get(chunk.documentId) || [];
    const chunkIndex = chunkIds.indexOf(chunk.id);
    
    let context = chunk.text;
    
    // Add previous chunk for context
    if (chunkIndex > 0) {
      const prevChunk = this.chunks.get(chunkIds[chunkIndex - 1]);
      if (prevChunk) {
        context = prevChunk.text.slice(-100) + '... ' + context;
      }
    }
    
    // Add next chunk for context
    if (chunkIndex < chunkIds.length - 1) {
      const nextChunk = this.chunks.get(chunkIds[chunkIndex + 1]);
      if (nextChunk) {
        context = context + ' ...' + nextChunk.text.slice(0, 100);
      }
    }
    
    return context;
  }

  private generateHighlights(text: string, concepts: string[]): string[] {
    const highlights: string[] = [];
    
    concepts.forEach(concept => {
      const regex = new RegExp(`\\b${concept}\\b`, 'gi');
      const matches = text.match(regex);
      if (matches) {
        highlights.push(...matches);
      }
    });
    
    return [...new Set(highlights)];
  }

  public async findSimilarDocuments(documentId: string, limit = 10): Promise<SemanticSearchResult[]> {
    const chunkIds = this.documentIndex.get(documentId) || [];
    if (chunkIds.length === 0) return [];
    
    // Use the first chunk as representative
    const representativeChunk = this.chunks.get(chunkIds[0]);
    if (!representativeChunk) return [];
    
    const query: ConceptualQuery = {
      query: representativeChunk.text,
      concepts: this.extractConcepts(representativeChunk.text),
      intent: 'search',
    };
    
    const results = await this.semanticSearch(query);
    
    // Filter out chunks from the same document
    return results
      .filter(result => result.chunk.documentId !== documentId)
      .slice(0, limit);
  }

  public getDocumentSummary(documentId: string): string {
    const chunkIds = this.documentIndex.get(documentId) || [];
    const headingChunks = chunkIds
      .map(id => this.chunks.get(id))
      .filter(chunk => chunk && chunk.metadata.type === 'heading')
      .slice(0, 5);
    
    return headingChunks
      .map(chunk => chunk!.text)
      .join(' • ');
  }

  public getTopConcepts(limit = 20): Array<{ concept: string; frequency: number }> {
    const conceptFrequency = new Map<string, number>();
    
    for (const [concept, chunkIds] of this.conceptIndex) {
      conceptFrequency.set(concept, chunkIds.size);
    }
    
    return Array.from(conceptFrequency.entries())
      .map(([concept, frequency]) => ({ concept, frequency }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit);
  }

  public removeDocument(documentId: string): void {
    const chunkIds = this.documentIndex.get(documentId) || [];
    
    chunkIds.forEach(chunkId => {
      this.chunks.delete(chunkId);
      this.embeddings.delete(chunkId);
      
      // Remove from concept index
      for (const [concept, chunkSet] of this.conceptIndex) {
        chunkSet.delete(chunkId);
        if (chunkSet.size === 0) {
          this.conceptIndex.delete(concept);
        }
      }
    });
    
    this.documentIndex.delete(documentId);
  }

  public destroy(): void {
    if (this.embeddingWorker) {
      this.embeddingWorker.terminate();
    }
    
    this.chunks.clear();
    this.embeddings.clear();
    this.documentIndex.clear();
    this.conceptIndex.clear();
  }
}
