"use client";

import { PageCache, type CachedPageData, type CacheConfig } from './page-cache';
import { ThumbnailCache, type ThumbnailData, type ThumbnailCacheConfig } from './thumbnail-cache';
import { getServiceWorkerManager, type CacheStats as ServiceWorkerStats } from '../service-worker';

/**
 * Unified Cache Manager
 * Coordinates all caching systems: memory, IndexedDB, service worker, and thumbnails
 */

export interface CacheManagerConfig {
  pageCache: Partial<CacheConfig>;
  thumbnailCache: Partial<ThumbnailCacheConfig>;
  enableServiceWorker: boolean;
  enablePreloading: boolean;
  enableBackgroundSync: boolean;
  globalMemoryLimit: number; // Total memory limit across all caches
  syncInterval: number; // Background sync interval
}

export const DEFAULT_CACHE_MANAGER_CONFIG: CacheManagerConfig = {
  pageCache: {},
  thumbnailCache: {},
  enableServiceWorker: true,
  enablePreloading: true,
  enableBackgroundSync: true,
  globalMemoryLimit: 200 * 1024 * 1024, // 200MB
  syncInterval: 300000, // 5 minutes
};

export interface CacheManagerStats {
  pages: {
    memoryPages: number;
    memorySizeMB: number;
    memoryUtilization: number;
  };
  thumbnails: {
    memoryThumbnails: number;
    memorySizeMB: number;
    memoryUtilization: number;
    activeGenerations: number;
    queuedGenerations: number;
  };
  serviceWorker: ServiceWorkerStats;
  global: {
    totalMemoryMB: number;
    totalMemoryUtilization: number;
    isOnline: boolean;
    serviceWorkerActive: boolean;
  };
}

export interface CacheEntry {
  type: 'page' | 'thumbnail';
  documentId: string;
  pageNumber: number;
  data: CachedPageData | ThumbnailData;
}

export class CacheManager {
  private static instance: CacheManager;
  private config: CacheManagerConfig;
  private pageCache: PageCache;
  private thumbnailCache: ThumbnailCache;
  private serviceWorkerManager = getServiceWorkerManager();
  private syncInterval: NodeJS.Timeout | null = null;
  private preloadQueue = new Set<string>();
  private isInitialized = false;

  private constructor(config: Partial<CacheManagerConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_MANAGER_CONFIG, ...config };
    this.pageCache = new PageCache(this.config.pageCache);
    this.thumbnailCache = new ThumbnailCache(this.config.thumbnailCache);
    this.initialize();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<CacheManagerConfig>): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager(config);
    }
    return CacheManager.instance;
  }

  /**
   * Initialize the cache manager
   */
  private async initialize(): Promise<void> {
    try {
      // Initialize service worker if enabled
      if (this.config.enableServiceWorker) {
        await this.serviceWorkerManager.register();
      }

      // Start background sync if enabled
      if (this.config.enableBackgroundSync) {
        this.startBackgroundSync();
      }

      this.isInitialized = true;
      console.log('[CacheManager] Initialized successfully');
    } catch (error) {
      console.error('[CacheManager] Initialization failed:', error);
    }
  }

  /**
   * Get page from any cache level
   */
  async getPage(
    documentId: string,
    pageNumber: number,
    scale: number = 1.0,
    rotation: number = 0
  ): Promise<CachedPageData | null> {
    try {
      // Try page cache first
      const pageData = await this.pageCache.getPage(documentId, pageNumber, scale, rotation);

      if (pageData) {
        // Trigger preloading if enabled
        if (this.config.enablePreloading) {
          this.schedulePreload(documentId, pageNumber, scale, rotation);
        }

        return pageData;
      }

      return null;
    } catch (error) {
      console.error('[CacheManager] Failed to get page:', error);
      return null;
    }
  }

  /**
   * Store page in cache
   */
  async setPage(pageData: Omit<CachedPageData, 'timestamp' | 'accessCount' | 'lastAccessed' | 'size'>): Promise<void> {
    // Store in page cache
    await this.pageCache.setPage(pageData);

    // Store in service worker cache if enabled
    if (this.config.enableServiceWorker && pageData.imageData) {
      try {
        await this.serviceWorkerManager.cachePDF(
          pageData.imageData, 
          `${pageData.documentId}_${pageData.pageNumber}`
        );
      } catch (error) {
        console.warn('[CacheManager] Service worker caching failed:', error);
      }
    }
  }

  /**
   * Get thumbnail from cache
   */
  async getThumbnail(
    documentId: string,
    pageNumber: number,
    scale: number = 1.0
  ): Promise<ThumbnailData | null> {
    try {
      return await this.thumbnailCache.getThumbnail(documentId, pageNumber, scale);
    } catch (error) {
      console.error('[CacheManager] Failed to get thumbnail:', error);
      return null;
    }
  }

  /**
   * Store thumbnail in cache
   */
  async setThumbnail(thumbnailData: Omit<ThumbnailData, 'timestamp' | 'lastAccessed' | 'accessCount' | 'size'>): Promise<void> {
    await this.thumbnailCache.setThumbnail(thumbnailData);

    // Store in service worker cache if enabled
    if (this.config.enableServiceWorker) {
      try {
        await this.serviceWorkerManager.cacheThumbnail(
          thumbnailData.imageData,
          thumbnailData.documentId,
          thumbnailData.pageNumber
        );
      } catch (error) {
        console.warn('[CacheManager] Service worker thumbnail caching failed:', error);
      }
    }
  }

  /**
   * Generate and cache thumbnail
   */
  async generateThumbnail(
    pdfPage: unknown,
    documentId: string,
    pageNumber: number,
    options?: {
      width?: number;
      height?: number;
      quality?: number;
      scale?: number;
    }
  ): Promise<ThumbnailData | null> {
    return await this.thumbnailCache.generateThumbnail(pdfPage, documentId, pageNumber, options);
  }

  /**
   * Preload pages around a specific page
   */
  async preloadPages(
    documentId: string,
    centerPage: number,
    range: number = 2,
    scale: number = 1.0,
    rotation: number = 0
  ): Promise<void> {
    if (!this.config.enablePreloading) return;

    await this.pageCache.preloadAdjacentPages(documentId, centerPage, range, scale, rotation);
  }

  /**
   * Schedule preloading for adjacent pages
   */
  private schedulePreload(
    documentId: string,
    pageNumber: number,
    scale: number,
    rotation: number
  ): void {
    const preloadKey = `${documentId}_${pageNumber}_${scale}_${rotation}`;
    
    if (this.preloadQueue.has(preloadKey)) return;
    
    this.preloadQueue.add(preloadKey);
    
    // Schedule preload with delay
    setTimeout(async () => {
      try {
        await this.preloadPages(documentId, pageNumber, 2, scale, rotation);
      } catch (error) {
        console.warn('[CacheManager] Preload failed:', error);
      } finally {
        this.preloadQueue.delete(preloadKey);
      }
    }, 500);
  }

  /**
   * Clear specific cache type
   */
  async clearCache(type: 'all' | 'pages' | 'thumbnails' | 'serviceWorker' = 'all'): Promise<void> {
    switch (type) {
      case 'all':
        await Promise.all([
          this.pageCache.clearAll(),
          this.thumbnailCache.clearAll(),
          this.config.enableServiceWorker ? this.serviceWorkerManager.clearCache('all') : Promise.resolve(),
        ]);
        break;
      case 'pages':
        await this.pageCache.clearAll();
        break;
      case 'thumbnails':
        await this.thumbnailCache.clearAll();
        break;
      case 'serviceWorker':
        if (this.config.enableServiceWorker) {
          await this.serviceWorkerManager.clearCache('all');
        }
        break;
    }
  }

  /**
   * Get comprehensive cache statistics
   */
  async getStats(): Promise<CacheManagerStats> {
    const pageStats = this.pageCache.getStats();
    const thumbnailStats = this.thumbnailCache.getStats();

    let serviceWorkerStats = { pdfEntries: 0, thumbnailEntries: 0, totalEntries: 0 };

    if (this.config.enableServiceWorker) {
      try {
        serviceWorkerStats = await this.serviceWorkerManager.getCacheStats();
      } catch (error) {
        console.error('[CacheManager] Failed to get service worker stats:', error);
      }
    }

    const totalMemoryMB = pageStats.memorySizeMB + thumbnailStats.memorySizeMB;
    const globalMemoryLimitMB = this.config.globalMemoryLimit / (1024 * 1024);

    return {
      pages: pageStats,
      thumbnails: thumbnailStats,
      serviceWorker: serviceWorkerStats,
      global: {
        totalMemoryMB,
        totalMemoryUtilization: totalMemoryMB / globalMemoryLimitMB,
        isOnline: !this.serviceWorkerManager.isOffline(),
        serviceWorkerActive: this.serviceWorkerManager.getStatus() === 'registered',
      },
    };
  }

  /**
   * Optimize cache usage
   */
  async optimizeCache(): Promise<void> {
    const stats = await this.getStats();
    
    // If global memory usage is too high, clear some caches
    if (stats.global.totalMemoryUtilization > 0.9) {
      console.log('[CacheManager] Memory usage high, optimizing...');
      
      // Clear thumbnails first (they can be regenerated)
      if (stats.thumbnails.memoryUtilization > 0.8) {
        await this.thumbnailCache.clearAll();
      }
      
      // If still high, clear some pages
      if (stats.global.totalMemoryUtilization > 0.8) {
        // This would trigger LRU eviction in page cache
        console.log('[CacheManager] Triggering page cache optimization');
      }
    }
  }

  /**
   * Start background synchronization
   */
  private startBackgroundSync(): void {
    this.syncInterval = setInterval(async () => {
      try {
        await this.performBackgroundSync();
      } catch (error) {
        console.error('[CacheManager] Background sync failed:', error);
      }
    }, this.config.syncInterval);
  }

  /**
   * Perform background synchronization tasks
   */
  private async performBackgroundSync(): Promise<void> {
    // Optimize cache usage
    await this.optimizeCache();
    
    // Sync with service worker if online
    if (!this.serviceWorkerManager.isOffline()) {
      // Perform any necessary sync operations
      console.log('[CacheManager] Background sync completed');
    }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<CacheManagerConfig>): void {
    this.config = { ...this.config, ...updates };
    
    // Restart background sync if interval changed
    if (updates.syncInterval && this.syncInterval) {
      clearInterval(this.syncInterval);
      this.startBackgroundSync();
    }
  }

  /**
   * Check if cache manager is ready
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Destroy cache manager and cleanup resources
   */
  destroy(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }
    
    this.pageCache.destroy();
    this.thumbnailCache.destroy();
    this.preloadQueue.clear();
    
    CacheManager.instance = null as unknown as CacheManager;
  }
}

// Export convenience functions
export const getCacheManager = (config?: Partial<CacheManagerConfig>): CacheManager => {
  return CacheManager.getInstance(config);
};

export const initializeCacheManager = async (config?: Partial<CacheManagerConfig>): Promise<CacheManager> => {
  const manager = getCacheManager(config);
  // Wait for initialization
  while (!manager.isReady()) {
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  return manager;
};
