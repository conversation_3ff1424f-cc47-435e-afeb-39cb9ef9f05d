"use client";

/**
 * Thumbnail Cache System
 * Specialized caching for PDF page thumbnails with size optimization
 */

export interface ThumbnailData {
  documentId: string;
  pageNumber: number;
  scale: number;
  imageData: string; // Base64 encoded thumbnail
  width: number;
  height: number;
  quality: number; // Compression quality used
  timestamp: number;
  lastAccessed: number;
  accessCount: number;
  size: number; // Size in bytes
}

export interface ThumbnailCacheConfig {
  // Memory settings
  maxMemoryThumbnails: number;
  maxMemorySize: number; // bytes
  
  // IndexedDB settings
  maxIndexedDBThumbnails: number;
  maxIndexedDBSize: number; // bytes
  
  // Thumbnail generation settings
  defaultWidth: number;
  defaultHeight: number;
  defaultQuality: number;
  adaptiveQuality: boolean;
  
  // Cache policies
  evictionPolicy: 'lru' | 'lfu' | 'size-aware';
  preloadAdjacent: boolean; // Preload adjacent page thumbnails
  backgroundGeneration: boolean;
  
  // Performance settings
  maxConcurrentGeneration: number;
  generationDelay: number; // ms
  cleanupInterval: number; // ms
}

export const DEFAULT_THUMBNAIL_CONFIG: ThumbnailCacheConfig = {
  maxMemoryThumbnails: 50,
  maxMemorySize: 20 * 1024 * 1024, // 20MB
  maxIndexedDBThumbnails: 200,
  maxIndexedDBSize: 100 * 1024 * 1024, // 100MB
  defaultWidth: 150,
  defaultHeight: 200,
  defaultQuality: 0.7,
  adaptiveQuality: true,
  evictionPolicy: 'size-aware',
  preloadAdjacent: true,
  backgroundGeneration: true,
  maxConcurrentGeneration: 3,
  generationDelay: 100,
  cleanupInterval: 60000, // 1 minute
};

export class ThumbnailCache {
  private config: ThumbnailCacheConfig;
  private memoryCache = new Map<string, ThumbnailData>();
  private memoryCacheSize = 0;
  private db: IDBDatabase | null = null;
  private isInitialized = false;
  private generationQueue = new Set<string>();
  private activeGenerations = new Set<string>();
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<ThumbnailCacheConfig> = {}) {
    this.config = { ...DEFAULT_THUMBNAIL_CONFIG, ...config };
    this.initialize();
  }

  /**
   * Initialize the thumbnail cache
   */
  private async initialize(): Promise<void> {
    try {
      await this.initializeIndexedDB();
      this.startBackgroundCleanup();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize thumbnail cache:', error);
    }
  }

  /**
   * Initialize IndexedDB for thumbnail storage
   */
  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PDFThumbnailCache', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains('thumbnails')) {
          const store = db.createObjectStore('thumbnails', { keyPath: 'id' });
          store.createIndex('documentId', 'documentId', { unique: false });
          store.createIndex('pageNumber', 'pageNumber', { unique: false });
          store.createIndex('lastAccessed', 'lastAccessed', { unique: false });
          store.createIndex('size', 'size', { unique: false });
        }
      };
    });
  }

  /**
   * Generate cache key for thumbnail
   */
  private getCacheKey(documentId: string, pageNumber: number, scale: number): string {
    return `${documentId}_${pageNumber}_${scale}`;
  }

  /**
   * Get thumbnail from cache
   */
  async getThumbnail(
    documentId: string,
    pageNumber: number,
    scale: number = 1.0
  ): Promise<ThumbnailData | null> {
    const key = this.getCacheKey(documentId, pageNumber, scale);

    // Check memory cache first
    const memoryResult = this.memoryCache.get(key);
    if (memoryResult) {
      memoryResult.lastAccessed = Date.now();
      memoryResult.accessCount++;
      return memoryResult;
    }

    // Check IndexedDB cache
    if (!this.db) return null;

    try {
      const transaction = this.db.transaction(['thumbnails'], 'readonly');
      const store = transaction.objectStore('thumbnails');
      const request = store.get(key);

      return new Promise((resolve) => {
        request.onsuccess = () => {
          const result = request.result;
          if (result) {
            // Move to memory cache
            this.addToMemoryCache(key, result);
            result.lastAccessed = Date.now();
            result.accessCount++;
            
            // Update access stats
            this.updateThumbnailStats(key, result.lastAccessed, result.accessCount);
          }
          resolve(result || null);
        };
        request.onerror = () => resolve(null);
      });
    } catch (error) {
      console.error('Error reading thumbnail from IndexedDB:', error);
      return null;
    }
  }

  /**
   * Store thumbnail in cache
   */
  async setThumbnail(thumbnailData: Omit<ThumbnailData, 'timestamp' | 'lastAccessed' | 'accessCount' | 'size'>): Promise<void> {
    const key = this.getCacheKey(thumbnailData.documentId, thumbnailData.pageNumber, thumbnailData.scale);
    
    // Calculate size
    const size = this.calculateThumbnailSize(thumbnailData.imageData);
    
    const fullThumbnailData: ThumbnailData = {
      ...thumbnailData,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
      accessCount: 1,
      size,
    };

    // Add to memory cache
    this.addToMemoryCache(key, fullThumbnailData);

    // Add to IndexedDB cache
    await this.addToIndexedDBCache(key, fullThumbnailData);

    // Preload adjacent thumbnails if enabled
    if (this.config.preloadAdjacent) {
      this.scheduleAdjacentPreload(thumbnailData.documentId, thumbnailData.pageNumber);
    }
  }

  /**
   * Generate thumbnail from PDF page
   */
  async generateThumbnail(
    pdfPage: object, // PDF.js page object
    documentId: string,
    pageNumber: number,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      scale?: number;
    } = {}
  ): Promise<ThumbnailData | null> {
    const {
      width = this.config.defaultWidth,
      height = this.config.defaultHeight,
      quality = this.config.defaultQuality,
      scale = 1.0,
    } = options;

    const key = this.getCacheKey(documentId, pageNumber, scale);

    // Check if already generating
    if (this.activeGenerations.has(key)) {
      return null;
    }

    // Check if too many concurrent generations
    if (this.activeGenerations.size >= this.config.maxConcurrentGeneration) {
      this.generationQueue.add(key);
      return null;
    }

    this.activeGenerations.add(key);

    try {
      // Calculate optimal quality based on device capabilities
      const finalQuality = this.config.adaptiveQuality 
        ? this.calculateAdaptiveQuality(quality)
        : quality;

      // Create canvas for thumbnail generation
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Failed to get canvas context');
      }

      // Get page viewport
      const viewport = pdfPage.getViewport({ scale: scale * (width / pdfPage.getViewport({ scale: 1.0 }).width) });
      
      canvas.width = width;
      canvas.height = height;

      // Render page to canvas
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await pdfPage.render(renderContext).promise;

      // Convert to base64 with compression
      const imageData = canvas.toDataURL('image/jpeg', finalQuality);

      // Create thumbnail data
      const thumbnailData: ThumbnailData = {
        documentId,
        pageNumber,
        scale,
        imageData,
        width,
        height,
        quality: finalQuality,
        timestamp: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        size: this.calculateThumbnailSize(imageData),
      };

      // Store in cache
      await this.setThumbnail(thumbnailData);

      return thumbnailData;
    } catch (error) {
      console.error('Failed to generate thumbnail:', error);
      return null;
    } finally {
      this.activeGenerations.delete(key);
      
      // Process next item in queue
      if (this.generationQueue.size > 0) {
        const nextKey = this.generationQueue.values().next().value;
        this.generationQueue.delete(nextKey);
        
        // Schedule with delay
        setTimeout(() => {
          // Re-trigger generation for queued item
          // This would need to be implemented based on your specific needs
        }, this.config.generationDelay);
      }
    }
  }

  /**
   * Add thumbnail to memory cache with eviction
   */
  private addToMemoryCache(key: string, thumbnailData: ThumbnailData): void {
    // Check if we need to evict thumbnails
    while (
      (this.memoryCache.size >= this.config.maxMemoryThumbnails ||
       this.memoryCacheSize + thumbnailData.size > this.config.maxMemorySize) &&
      this.memoryCache.size > 0
    ) {
      this.evictFromMemoryCache();
    }

    this.memoryCache.set(key, thumbnailData);
    this.memoryCacheSize += thumbnailData.size;
  }

  /**
   * Evict thumbnail from memory cache
   */
  private evictFromMemoryCache(): void {
    if (this.memoryCache.size === 0) return;

    let keyToEvict: string;

    switch (this.config.evictionPolicy) {
      case 'lru':
        keyToEvict = this.findLRUKey();
        break;
      case 'lfu':
        keyToEvict = this.findLFUKey();
        break;
      case 'size-aware':
      default:
        keyToEvict = this.findSizeAwareEvictionKey();
        break;
    }

    const thumbnailData = this.memoryCache.get(keyToEvict);
    if (thumbnailData) {
      this.memoryCache.delete(keyToEvict);
      this.memoryCacheSize -= thumbnailData.size;
    }
  }

  /**
   * Find least recently used key
   */
  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, data] of this.memoryCache) {
      if (data.lastAccessed < oldestTime) {
        oldestTime = data.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Find least frequently used key
   */
  private findLFUKey(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;

    for (const [key, data] of this.memoryCache) {
      if (data.accessCount < leastCount) {
        leastCount = data.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  /**
   * Find key to evict using size-aware strategy
   */
  private findSizeAwareEvictionKey(): string {
    let bestKey = '';
    let bestScore = 0;
    const now = Date.now();

    for (const [key, data] of this.memoryCache) {
      // Score based on size, age, and access frequency
      const timeSinceAccess = now - data.lastAccessed;
      const sizeWeight = data.size / (1024 * 1024); // MB
      const score = (timeSinceAccess / 1000) * sizeWeight / (data.accessCount + 1);
      
      if (score > bestScore) {
        bestScore = score;
        bestKey = key;
      }
    }

    return bestKey;
  }

  /**
   * Add thumbnail to IndexedDB cache
   */
  private async addToIndexedDBCache(key: string, thumbnailData: ThumbnailData): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['thumbnails'], 'readwrite');
      const store = transaction.objectStore('thumbnails');
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put({ ...thumbnailData, id: key });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Check if we need cleanup
      await this.cleanupIndexedDBIfNeeded();
    } catch (error) {
      console.error('Error writing thumbnail to IndexedDB:', error);
    }
  }

  /**
   * Update thumbnail access statistics
   */
  private async updateThumbnailStats(key: string, lastAccessed: number, accessCount: number): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['thumbnails'], 'readwrite');
      const store = transaction.objectStore('thumbnails');
      
      const getRequest = store.get(key);
      getRequest.onsuccess = () => {
        const data = getRequest.result;
        if (data) {
          data.lastAccessed = lastAccessed;
          data.accessCount = accessCount;
          store.put(data);
        }
      };
    } catch (error) {
      console.error('Error updating thumbnail stats:', error);
    }
  }

  /**
   * Calculate adaptive quality based on device capabilities
   */
  private calculateAdaptiveQuality(baseQuality: number): number {
    // Simple adaptive quality based on device memory
    if (typeof navigator !== 'undefined' && 'deviceMemory' in navigator) {
      const deviceMemory = (navigator as Navigator & { deviceMemory?: number }).deviceMemory || 4;
      
      if (deviceMemory <= 2) {
        return Math.max(0.4, baseQuality * 0.7); // Lower quality for low-memory devices
      } else if (deviceMemory >= 8) {
        return Math.min(0.9, baseQuality * 1.2); // Higher quality for high-memory devices
      }
    }
    
    return baseQuality;
  }

  /**
   * Calculate thumbnail size in bytes
   */
  private calculateThumbnailSize(imageData: string): number {
    return Math.round(imageData.length * 0.75); // Base64 overhead
  }

  /**
   * Schedule preloading of adjacent thumbnails
   */
  private scheduleAdjacentPreload(documentId: string, pageNumber: number): void {
    if (!this.config.backgroundGeneration) return;

    // Preload previous and next page thumbnails
    const adjacentPages = [pageNumber - 1, pageNumber + 1].filter(p => p > 0);
    
    adjacentPages.forEach(adjPageNumber => {
      const key = this.getCacheKey(documentId, adjPageNumber, 1.0);
      
      if (!this.memoryCache.has(key) && !this.generationQueue.has(key)) {
        // Add to generation queue with lower priority
        setTimeout(() => {
          this.generationQueue.add(key);
        }, this.config.generationDelay * 2);
      }
    });
  }

  /**
   * Cleanup IndexedDB if needed
   */
  private async cleanupIndexedDBIfNeeded(): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['thumbnails'], 'readonly');
      const store = transaction.objectStore('thumbnails');
      const countRequest = store.count();

      countRequest.onsuccess = async () => {
        const count = countRequest.result;
        
        if (count > this.config.maxIndexedDBThumbnails) {
          await this.cleanupIndexedDB(count - this.config.maxIndexedDBThumbnails);
        }
      };
    } catch (error) {
      console.error('Error during IndexedDB cleanup check:', error);
    }
  }

  /**
   * Clean up old entries from IndexedDB
   */
  private async cleanupIndexedDB(entriesToRemove: number): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['thumbnails'], 'readwrite');
      const store = transaction.objectStore('thumbnails');
      const index = store.index('lastAccessed');
      
      const request = index.openCursor();
      let removed = 0;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor && removed < entriesToRemove) {
          cursor.delete();
          removed++;
          cursor.continue();
        }
      };
    } catch (error) {
      console.error('Error cleaning up IndexedDB thumbnails:', error);
    }
  }

  /**
   * Start background cleanup
   */
  private startBackgroundCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performBackgroundCleanup();
    }, this.config.cleanupInterval);
  }

  /**
   * Perform background cleanup
   */
  private performBackgroundCleanup(): void {
    // Clean up memory cache if it's getting too large
    if (this.memoryCacheSize > this.config.maxMemorySize * 0.9) {
      const targetSize = this.config.maxMemorySize * 0.7;
      while (this.memoryCacheSize > targetSize && this.memoryCache.size > 0) {
        this.evictFromMemoryCache();
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    memoryThumbnails: number;
    memorySizeMB: number;
    memoryUtilization: number;
    activeGenerations: number;
    queuedGenerations: number;
  } {
    return {
      memoryThumbnails: this.memoryCache.size,
      memorySizeMB: Math.round(this.memoryCacheSize / (1024 * 1024) * 100) / 100,
      memoryUtilization: this.memoryCacheSize / this.config.maxMemorySize,
      activeGenerations: this.activeGenerations.size,
      queuedGenerations: this.generationQueue.size,
    };
  }

  /**
   * Clear all caches
   */
  async clearAll(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear();
    this.memoryCacheSize = 0;

    // Clear IndexedDB cache
    if (this.db) {
      try {
        const transaction = this.db.transaction(['thumbnails'], 'readwrite');
        const store = transaction.objectStore('thumbnails');
        await new Promise<void>((resolve, reject) => {
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      } catch (error) {
        console.error('Error clearing thumbnail cache:', error);
      }
    }
  }

  /**
   * Destroy cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    
    this.memoryCache.clear();
    this.memoryCacheSize = 0;
    this.generationQueue.clear();
    this.activeGenerations.clear();
    
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
