"use client";

/**
 * Multi-level Page Cache System
 * Implements memory, IndexedDB, and service worker caching for PDF pages
 */

export interface CachedPageData {
  pageNumber: number;
  documentId: string;
  scale: number;
  rotation: number;
  imageData: string; // Base64 encoded image
  textContent?: string;
  annotations?: object[];
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number; // Size in bytes
}

export interface CacheConfig {
  // Memory cache settings
  maxMemorySize: number; // Max memory cache size in bytes
  maxMemoryPages: number; // Max pages in memory
  
  // IndexedDB cache settings
  maxIndexedDBSize: number; // Max IndexedDB cache size in bytes
  maxIndexedDBPages: number; // Max pages in IndexedDB
  
  // Cache policies
  evictionPolicy: 'lru' | 'lfu' | 'hybrid'; // Eviction strategy
  compressionEnabled: boolean; // Enable image compression
  compressionQuality: number; // JPEG quality (0.1-1.0)
  
  // Performance settings
  preloadDistance: number; // Pages to preload ahead/behind
  backgroundCleanupInterval: number; // Cleanup interval in ms
}

export const DEFAULT_CACHE_CONFIG: CacheConfig = {
  maxMemorySize: 100 * 1024 * 1024, // 100MB
  maxMemoryPages: 20,
  maxIndexedDBSize: 500 * 1024 * 1024, // 500MB
  maxIndexedDBPages: 100,
  evictionPolicy: 'hybrid',
  compressionEnabled: true,
  compressionQuality: 0.8,
  preloadDistance: 2,
  backgroundCleanupInterval: 30000, // 30 seconds
};

export class PageCache {
  private config: CacheConfig;
  private memoryCache = new Map<string, CachedPageData>();
  private memoryCacheSize = 0;
  private db: IDBDatabase | null = null;
  private isInitialized = false;
  private cleanupInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_CONFIG, ...config };
    this.initialize();
  }

  /**
   * Initialize the cache system
   */
  private async initialize(): Promise<void> {
    try {
      await this.initializeIndexedDB();
      this.startBackgroundCleanup();
      this.isInitialized = true;
    } catch (error) {
      console.error('Failed to initialize page cache:', error);
    }
  }

  /**
   * Initialize IndexedDB for persistent caching
   */
  private async initializeIndexedDB(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PDFPageCache', 1);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains('pages')) {
          const store = db.createObjectStore('pages', { keyPath: 'id' });
          store.createIndex('documentId', 'documentId', { unique: false });
          store.createIndex('pageNumber', 'pageNumber', { unique: false });
          store.createIndex('lastAccessed', 'lastAccessed', { unique: false });
          store.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  /**
   * Generate cache key for a page
   */
  private getCacheKey(documentId: string, pageNumber: number, scale: number, rotation: number): string {
    return `${documentId}_${pageNumber}_${scale}_${rotation}`;
  }

  /**
   * Get page from cache (memory first, then IndexedDB)
   */
  async getPage(
    documentId: string, 
    pageNumber: number, 
    scale: number, 
    rotation: number
  ): Promise<CachedPageData | null> {
    const key = this.getCacheKey(documentId, pageNumber, scale, rotation);

    // Check memory cache first
    const memoryResult = this.memoryCache.get(key);
    if (memoryResult) {
      memoryResult.lastAccessed = Date.now();
      memoryResult.accessCount++;
      return memoryResult;
    }

    // Check IndexedDB cache
    if (!this.db) return null;

    try {
      const transaction = this.db.transaction(['pages'], 'readonly');
      const store = transaction.objectStore('pages');
      const request = store.get(key);

      return new Promise((resolve) => {
        request.onsuccess = () => {
          const result = request.result;
          if (result) {
            // Move to memory cache for faster access
            this.addToMemoryCache(key, result);
            result.lastAccessed = Date.now();
            result.accessCount++;
            
            // Update access stats in IndexedDB
            this.updatePageStats(key, result.lastAccessed, result.accessCount);
          }
          resolve(result || null);
        };
        request.onerror = () => resolve(null);
      });
    } catch (error) {
      console.error('Error reading from IndexedDB cache:', error);
      return null;
    }
  }

  /**
   * Store page in cache
   */
  async setPage(pageData: Omit<CachedPageData, 'timestamp' | 'accessCount' | 'lastAccessed' | 'size'>): Promise<void> {
    const key = this.getCacheKey(pageData.documentId, pageData.pageNumber, pageData.scale, pageData.rotation);
    
    // Calculate size and add metadata
    const size = this.calculateDataSize(pageData.imageData, pageData.textContent);
    const fullPageData: CachedPageData = {
      ...pageData,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      size,
    };

    // Compress image if enabled
    if (this.config.compressionEnabled) {
      fullPageData.imageData = await this.compressImage(pageData.imageData);
      fullPageData.size = this.calculateDataSize(fullPageData.imageData, pageData.textContent);
    }

    // Add to memory cache
    this.addToMemoryCache(key, fullPageData);

    // Add to IndexedDB cache
    await this.addToIndexedDBCache(key, fullPageData);
  }

  /**
   * Add page to memory cache with eviction
   */
  private addToMemoryCache(key: string, pageData: CachedPageData): void {
    // Check if we need to evict pages
    while (
      (this.memoryCache.size >= this.config.maxMemoryPages ||
       this.memoryCacheSize + pageData.size > this.config.maxMemorySize) &&
      this.memoryCache.size > 0
    ) {
      this.evictFromMemoryCache();
    }

    this.memoryCache.set(key, pageData);
    this.memoryCacheSize += pageData.size;
  }

  /**
   * Evict page from memory cache based on policy
   */
  private evictFromMemoryCache(): void {
    if (this.memoryCache.size === 0) return;

    let keyToEvict: string;

    switch (this.config.evictionPolicy) {
      case 'lru':
        keyToEvict = this.findLRUKey();
        break;
      case 'lfu':
        keyToEvict = this.findLFUKey();
        break;
      case 'hybrid':
      default:
        keyToEvict = this.findHybridEvictionKey();
        break;
    }

    const pageData = this.memoryCache.get(keyToEvict);
    if (pageData) {
      this.memoryCache.delete(keyToEvict);
      this.memoryCacheSize -= pageData.size;
    }
  }

  /**
   * Find least recently used key
   */
  private findLRUKey(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, data] of this.memoryCache) {
      if (data.lastAccessed < oldestTime) {
        oldestTime = data.lastAccessed;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  /**
   * Find least frequently used key
   */
  private findLFUKey(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;

    for (const [key, data] of this.memoryCache) {
      if (data.accessCount < leastCount) {
        leastCount = data.accessCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  /**
   * Find key to evict using hybrid strategy (LRU + LFU)
   */
  private findHybridEvictionKey(): string {
    let bestKey = '';
    let bestScore = Infinity;
    const now = Date.now();

    for (const [key, data] of this.memoryCache) {
      // Hybrid score: combines recency and frequency
      const timeSinceAccess = now - data.lastAccessed;
      const score = timeSinceAccess / (data.accessCount + 1);
      
      if (score < bestScore) {
        bestScore = score;
        bestKey = key;
      }
    }

    return bestKey;
  }

  /**
   * Add page to IndexedDB cache
   */
  private async addToIndexedDBCache(key: string, pageData: CachedPageData): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['pages'], 'readwrite');
      const store = transaction.objectStore('pages');
      
      await new Promise<void>((resolve, reject) => {
        const request = store.put({ ...pageData, id: key });
        request.onsuccess = () => resolve();
        request.onerror = () => reject(request.error);
      });

      // Check if we need to clean up IndexedDB
      await this.cleanupIndexedDBIfNeeded();
    } catch (error) {
      console.error('Error writing to IndexedDB cache:', error);
    }
  }

  /**
   * Update page access statistics in IndexedDB
   */
  private async updatePageStats(key: string, lastAccessed: number, accessCount: number): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['pages'], 'readwrite');
      const store = transaction.objectStore('pages');
      
      const getRequest = store.get(key);
      getRequest.onsuccess = () => {
        const data = getRequest.result;
        if (data) {
          data.lastAccessed = lastAccessed;
          data.accessCount = accessCount;
          store.put(data);
        }
      };
    } catch (error) {
      console.error('Error updating page stats:', error);
    }
  }

  /**
   * Clean up IndexedDB cache if it exceeds limits
   */
  private async cleanupIndexedDBIfNeeded(): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['pages'], 'readonly');
      const store = transaction.objectStore('pages');
      const countRequest = store.count();

      countRequest.onsuccess = async () => {
        const count = countRequest.result;
        
        if (count > this.config.maxIndexedDBPages) {
          await this.cleanupIndexedDB(count - this.config.maxIndexedDBPages);
        }
      };
    } catch (error) {
      console.error('Error during IndexedDB cleanup:', error);
    }
  }

  /**
   * Clean up old entries from IndexedDB
   */
  private async cleanupIndexedDB(entriesToRemove: number): Promise<void> {
    if (!this.db) return;

    try {
      const transaction = this.db.transaction(['pages'], 'readwrite');
      const store = transaction.objectStore('pages');
      const index = store.index('lastAccessed');
      
      const request = index.openCursor();
      let removed = 0;

      request.onsuccess = (event) => {
        const cursor = (event.target as IDBRequest).result;
        if (cursor && removed < entriesToRemove) {
          cursor.delete();
          removed++;
          cursor.continue();
        }
      };
    } catch (error) {
      console.error('Error cleaning up IndexedDB:', error);
    }
  }

  /**
   * Compress image data
   */
  private async compressImage(imageData: string): Promise<string> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        const compressedData = canvas.toDataURL('image/jpeg', this.config.compressionQuality);
        resolve(compressedData);
      };

      img.onerror = () => resolve(imageData); // Fallback to original
      img.src = imageData;
    });
  }

  /**
   * Calculate data size in bytes
   */
  private calculateDataSize(imageData: string, textContent?: string): number {
    let size = imageData.length * 0.75; // Base64 overhead
    if (textContent) {
      size += textContent.length * 2; // UTF-16 encoding
    }
    return Math.round(size);
  }

  /**
   * Start background cleanup process
   */
  private startBackgroundCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.performBackgroundCleanup();
    }, this.config.backgroundCleanupInterval);
  }

  /**
   * Perform background cleanup
   */
  private performBackgroundCleanup(): void {
    // Clean up memory cache if it's getting too large
    if (this.memoryCacheSize > this.config.maxMemorySize * 0.9) {
      const targetSize = this.config.maxMemorySize * 0.7;
      while (this.memoryCacheSize > targetSize && this.memoryCache.size > 0) {
        this.evictFromMemoryCache();
      }
    }
  }

  /**
   * Clear all caches
   */
  async clearAll(): Promise<void> {
    // Clear memory cache
    this.memoryCache.clear();
    this.memoryCacheSize = 0;

    // Clear IndexedDB cache
    if (this.db) {
      try {
        const transaction = this.db.transaction(['pages'], 'readwrite');
        const store = transaction.objectStore('pages');
        await new Promise<void>((resolve, reject) => {
          const request = store.clear();
          request.onsuccess = () => resolve();
          request.onerror = () => reject(request.error);
        });
      } catch (error) {
        console.error('Error clearing IndexedDB cache:', error);
      }
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    memoryPages: number;
    memorySizeMB: number;
    memoryUtilization: number;
  } {
    return {
      memoryPages: this.memoryCache.size,
      memorySizeMB: Math.round(this.memoryCacheSize / (1024 * 1024) * 100) / 100,
      memoryUtilization: this.memoryCacheSize / this.config.maxMemorySize,
    };
  }

  /**
   * Get page from cache by document ID and page number (any scale/rotation)
   */
  async getPageVariants(documentId: string, pageNumber: number): Promise<CachedPageData[]> {
    const variants: CachedPageData[] = [];

    // Check memory cache
    for (const [, data] of this.memoryCache) {
      if (data.documentId === documentId && data.pageNumber === pageNumber) {
        variants.push(data);
      }
    }

    // Check IndexedDB if no memory results
    if (variants.length === 0 && this.db) {
      try {
        const transaction = this.db.transaction(['pages'], 'readonly');
        const store = transaction.objectStore('pages');
        const index = store.index('documentId');
        const request = index.getAll(documentId);

        return new Promise((resolve) => {
          request.onsuccess = () => {
            const results = request.result.filter(
              (item: { pageNumber: number }) => item.pageNumber === pageNumber
            );
            resolve(results);
          };
          request.onerror = () => resolve([]);
        });
      } catch (error) {
        console.error('Error getting page variants:', error);
      }
    }

    return variants;
  }

  /**
   * Preload pages around a specific page
   */
  async preloadAdjacentPages(
    documentId: string,
    centerPage: number,
    range: number = 2,
    scale: number = 1.0,
    rotation: number = 0
  ): Promise<void> {
    const pagesToPreload: number[] = [];

    for (let i = centerPage - range; i <= centerPage + range; i++) {
      if (i > 0 && i !== centerPage) {
        pagesToPreload.push(i);
      }
    }

    // Check which pages are not cached
    const uncachedPages: number[] = [];
    for (const pageNum of pagesToPreload) {
      const cached = await this.getPage(documentId, pageNum, scale, rotation);
      if (!cached) {
        uncachedPages.push(pageNum);
      }
    }

    // Trigger preload event for uncached pages
    if (uncachedPages.length > 0) {
      // This would be handled by the virtual renderer or PDF viewer
      console.log(`Preload needed for pages: ${uncachedPages.join(', ')}`);
    }
  }

  /**
   * Destroy cache and cleanup resources
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.memoryCache.clear();
    this.memoryCacheSize = 0;

    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}
