/**
 * Testing Utilities
 * Helper functions for testing and debugging the PDF viewer components
 */

import { isBrowser, safeLocalStorageGetItem, safeJSONParse } from './browser-utils';

export interface TestResult {
  name: string;
  passed: boolean;
  error?: string;
  details?: unknown;
  duration: number;
}

export interface SystemInfo {
  userAgent: string;
  platform: string;
  language: string;
  cookieEnabled: boolean;
  onLine: boolean;
  localStorage: boolean;
  sessionStorage: boolean;
  webWorkers: boolean;
  speechSynthesis: boolean;
  intersectionObserver: boolean;
  resizeObserver: boolean;
  viewport: { width: number; height: number };
  colorScheme: 'light' | 'dark' | 'unknown';
  reducedMotion: boolean;
  highContrast: boolean;
}

export class TestRunner {
  private tests: Array<() => Promise<TestResult> | TestResult> = [];
  private results: TestResult[] = [];

  public addTest(name: string, testFn: () => Promise<unknown> | unknown): void {
    this.tests.push(async () => {
      const startTime = performance.now();
      
      try {
        const result = await testFn();
        const duration = performance.now() - startTime;
        
        return {
          name,
          passed: true,
          details: result,
          duration,
        };
      } catch (error) {
        const duration = performance.now() - startTime;
        
        return {
          name,
          passed: false,
          error: error instanceof Error ? error.message : String(error),
          duration,
        };
      }
    });
  }

  public async runTests(): Promise<TestResult[]> {
    this.results = [];
    
    for (const test of this.tests) {
      const result = await test();
      this.results.push(result);
    }
    
    return this.results;
  }

  public getResults(): TestResult[] {
    return [...this.results];
  }

  public getSummary(): { total: number; passed: number; failed: number; duration: number } {
    const total = this.results.length;
    const passed = this.results.filter(r => r.passed).length;
    const failed = total - passed;
    const duration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    return { total, passed, failed, duration };
  }

  public clear(): void {
    this.tests = [];
    this.results = [];
  }
}

export const getSystemInfo = (): SystemInfo => {
  const defaultInfo: SystemInfo = {
    userAgent: '',
    platform: '',
    language: 'en',
    cookieEnabled: false,
    onLine: false,
    localStorage: false,
    sessionStorage: false,
    webWorkers: false,
    speechSynthesis: false,
    intersectionObserver: false,
    resizeObserver: false,
    viewport: { width: 0, height: 0 },
    colorScheme: 'unknown',
    reducedMotion: false,
    highContrast: false,
  };

  if (!isBrowser()) return defaultInfo;

  try {
    return {
      userAgent: navigator.userAgent || '',
      platform: navigator.platform || '',
      language: navigator.language || 'en',
      cookieEnabled: navigator.cookieEnabled || false,
      onLine: navigator.onLine || false,
      localStorage: (() => {
        try {
          const test = '__test__';
          localStorage.setItem(test, test);
          localStorage.removeItem(test);
          return true;
        } catch {
          return false;
        }
      })(),
      sessionStorage: (() => {
        try {
          const test = '__test__';
          sessionStorage.setItem(test, test);
          sessionStorage.removeItem(test);
          return true;
        } catch {
          return false;
        }
      })(),
      webWorkers: 'Worker' in window,
      speechSynthesis: 'speechSynthesis' in window,
      intersectionObserver: 'IntersectionObserver' in window,
      resizeObserver: 'ResizeObserver' in window,
      viewport: {
        width: window.innerWidth || 0,
        height: window.innerHeight || 0,
      },
      colorScheme: (() => {
        if (window.matchMedia) {
          if (window.matchMedia('(prefers-color-scheme: dark)').matches) return 'dark';
          if (window.matchMedia('(prefers-color-scheme: light)').matches) return 'light';
        }
        return 'unknown';
      })(),
      reducedMotion: window.matchMedia ? window.matchMedia('(prefers-reduced-motion: reduce)').matches : false,
      highContrast: window.matchMedia ? window.matchMedia('(prefers-contrast: high)').matches : false,
    };
  } catch {
    return defaultInfo;
  }
};

export const testAccessibilityFeatures = async (): Promise<TestResult[]> => {
  const runner = new TestRunner();

  runner.addTest('Screen Reader Detection', () => {
    const indicators = [
      navigator.userAgent.includes('NVDA'),
      navigator.userAgent.includes('JAWS'),
      'speechSynthesis' in window,
      document.querySelector('[aria-live]') !== null,
    ];
    
    return {
      detected: indicators.some(Boolean),
      indicators,
    };
  });

  runner.addTest('ARIA Live Regions', () => {
    const liveRegions = document.querySelectorAll('[aria-live]');
    return {
      count: liveRegions.length,
      regions: Array.from(liveRegions).map(el => ({
        id: el.id,
        role: el.getAttribute('role'),
        live: el.getAttribute('aria-live'),
      })),
    };
  });

  runner.addTest('Focus Management', () => {
    const focusableElements = document.querySelectorAll(
      'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
    );
    
    return {
      count: focusableElements.length,
      hasTabIndex: Array.from(focusableElements).some(el => el.hasAttribute('tabindex')),
    };
  });

  runner.addTest('Keyboard Navigation', () => {
    const hasKeyboardHandlers = document.addEventListener !== undefined;
    const hasTabIndex = document.querySelector('[tabindex]') !== null;
    
    return {
      eventListenersSupported: hasKeyboardHandlers,
      tabIndexPresent: hasTabIndex,
    };
  });

  return runner.runTests();
};

export const testStorageFeatures = async (): Promise<TestResult[]> => {
  const runner = new TestRunner();

  runner.addTest('Local Storage', () => {
    const test = '__localStorage_test__';
    localStorage.setItem(test, 'test');
    const result = localStorage.getItem(test) === 'test';
    localStorage.removeItem(test);
    return { available: result };
  });

  runner.addTest('Session Storage', () => {
    const test = '__sessionStorage_test__';
    sessionStorage.setItem(test, 'test');
    const result = sessionStorage.getItem(test) === 'test';
    sessionStorage.removeItem(test);
    return { available: result };
  });

  runner.addTest('Storage Data Integrity', () => {
    const testData = { test: true, number: 42, array: [1, 2, 3] };
    const serialized = JSON.stringify(testData);
    
    localStorage.setItem('__integrity_test__', serialized);
    const retrieved = localStorage.getItem('__integrity_test__');
    const parsed = JSON.parse(retrieved || '{}');
    
    localStorage.removeItem('__integrity_test__');
    
    return {
      serialized: serialized.length > 0,
      retrieved: retrieved !== null,
      parsed: JSON.stringify(parsed) === serialized,
    };
  });

  return runner.runTests();
};

export const testPerformanceFeatures = async (): Promise<TestResult[]> => {
  const runner = new TestRunner();

  runner.addTest('Web Workers', () => {
    return { available: 'Worker' in window };
  });

  runner.addTest('Intersection Observer', () => {
    return { available: 'IntersectionObserver' in window };
  });

  runner.addTest('Resize Observer', () => {
    return { available: 'ResizeObserver' in window };
  });

  runner.addTest('Performance API', () => {
    return {
      available: 'performance' in window,
      now: 'now' in (window.performance || {}),
      mark: 'mark' in (window.performance || {}),
      measure: 'measure' in (window.performance || {}),
    };
  });

  return runner.runTests();
};

export const testPDFViewerComponents = async (): Promise<TestResult[]> => {
  const runner = new TestRunner();

  runner.addTest('Search Components', () => {
    const searchElements = document.querySelectorAll('[data-search]');
    return {
      count: searchElements.length,
      hasSearchInput: document.querySelector('input[type="search"]') !== null,
    };
  });

  runner.addTest('Navigation Components', () => {
    const navElements = document.querySelectorAll('[data-navigation]');
    return {
      count: navElements.length,
      hasPageControls: document.querySelector('[data-page-control]') !== null,
    };
  });

  runner.addTest('Accessibility Components', () => {
    const a11yElements = document.querySelectorAll('[data-accessibility]');
    return {
      count: a11yElements.length,
      hasScreenReaderSupport: document.querySelector('[data-screen-reader]') !== null,
    };
  });

  return runner.runTests();
};

export const runAllTests = async (): Promise<{
  accessibility: TestResult[];
  storage: TestResult[];
  performance: TestResult[];
  components: TestResult[];
  systemInfo: SystemInfo;
}> => {
  const [accessibility, storage, performance, components] = await Promise.all([
    testAccessibilityFeatures(),
    testStorageFeatures(),
    testPerformanceFeatures(),
    testPDFViewerComponents(),
  ]);

  return {
    accessibility,
    storage,
    performance,
    components,
    systemInfo: getSystemInfo(),
  };
};

export const generateTestReport = async (): Promise<string> => {
  const results = await runAllTests();
  
  const report = {
    timestamp: new Date().toISOString(),
    systemInfo: results.systemInfo,
    testResults: {
      accessibility: results.accessibility,
      storage: results.storage,
      performance: results.performance,
      components: results.components,
    },
    summary: {
      accessibility: {
        total: results.accessibility.length,
        passed: results.accessibility.filter(r => r.passed).length,
      },
      storage: {
        total: results.storage.length,
        passed: results.storage.filter(r => r.passed).length,
      },
      performance: {
        total: results.performance.length,
        passed: results.performance.filter(r => r.passed).length,
      },
      components: {
        total: results.components.length,
        passed: results.components.filter(r => r.passed).length,
      },
    },
  };

  return JSON.stringify(report, null, 2);
};

export const debugComponent = (componentName: string): object | null => {
  if (!isBrowser()) return null;

  const elements = document.querySelectorAll(`[data-component="${componentName}"]`);
  const state = safeJSONParse(safeLocalStorageGetItem(`${componentName}-state`), {});
  
  return {
    elements: Array.from(elements).map(el => ({
      id: el.id,
      className: el.className,
      attributes: Array.from(el.attributes).map(attr => ({
        name: attr.name,
        value: attr.value,
      })),
    })),
    state,
    localStorage: Object.keys(localStorage).filter(key => key.includes(componentName)),
  };
};
