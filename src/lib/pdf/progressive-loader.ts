/**
 * Progressive PDF Loading & Streaming System
 * Implements chunked loading, streaming, and progressive enhancement for large PDFs
 */

import * as pdfjsLib from 'pdfjs-dist';

export interface ProgressiveLoadingConfig {
  chunkSize: number;
  maxConcurrentChunks: number;
  enableStreaming: boolean;
  enableRangeRequests: boolean;
  preloadPages: number;
  adaptiveQuality: boolean;
  networkThrottling: boolean;
}

export interface LoadingProgress {
  bytesLoaded: number;
  bytesTotal: number;
  percentage: number;
  pagesLoaded: number;
  pagesTotal: number;
  currentChunk: number;
  totalChunks: number;
  estimatedTimeRemaining: number;
  downloadSpeed: number;
}

export interface StreamingOptions {
  url: string;
  rangeSupport: boolean;
  contentLength: number;
  acceptRanges: boolean;
}

export type LoadingEventType = 
  | 'progress'
  | 'chunk-loaded'
  | 'page-ready'
  | 'metadata-loaded'
  | 'error'
  | 'complete';

export interface LoadingEvent {
  type: LoadingEventType;
  progress: LoadingProgress;
  data?: unknown;
  error?: Error;
}

const DEFAULT_CONFIG: ProgressiveLoadingConfig = {
  chunkSize: 1024 * 1024, // 1MB chunks
  maxConcurrentChunks: 3,
  enableStreaming: true,
  enableRangeRequests: true,
  preloadPages: 5,
  adaptiveQuality: true,
  networkThrottling: false,
};

export class ProgressivePDFLoader {
  private config: ProgressiveLoadingConfig;
  private eventListeners: Map<LoadingEventType, Set<(event: LoadingEvent) => void>> = new Map();
  private loadingTask: pdfjsLib.PDFDocumentLoadingTask | null = null;
  private document: pdfjsLib.PDFDocumentProxy | null = null;
  private chunks: Map<number, ArrayBuffer> = new Map();
  private loadedPages: Set<number> = new Set();
  private isLoading = false;
  private startTime = 0;
  private bytesLoaded = 0;
  private bytesTotal = 0;
  private downloadSpeeds: number[] = [];

  constructor(config: Partial<ProgressiveLoadingConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.initializeEventMaps();
  }

  private initializeEventMaps(): void {
    const eventTypes: LoadingEventType[] = ['progress', 'chunk-loaded', 'page-ready', 'metadata-loaded', 'error', 'complete'];
    eventTypes.forEach(type => {
      this.eventListeners.set(type, new Set());
    });
  }

  public addEventListener(type: LoadingEventType, listener: (event: LoadingEvent) => void): void {
    this.eventListeners.get(type)?.add(listener);
  }

  public removeEventListener(type: LoadingEventType, listener: (event: LoadingEvent) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(event: LoadingEvent): void {
    this.eventListeners.get(event.type)?.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error('Error in loading event listener:', error);
      }
    });
  }

  public async loadFromURL(url: string): Promise<pdfjsLib.PDFDocumentProxy> {
    if (this.isLoading) {
      throw new Error('Loading already in progress');
    }

    this.isLoading = true;
    this.startTime = Date.now();
    this.bytesLoaded = 0;
    this.bytesTotal = 0;

    try {
      // Check if server supports range requests
      const streamingOptions = await this.checkStreamingSupport(url);
      
      if (this.config.enableStreaming && streamingOptions.rangeSupport) {
        return await this.loadWithStreaming(url, streamingOptions);
      } else {
        return await this.loadWithChunking(url);
      }
    } catch (error) {
      this.isLoading = false;
      this.emit({
        type: 'error',
        progress: this.getProgress(),
        error: error as Error,
      });
      throw error;
    }
  }

  public async loadFromFile(file: File): Promise<pdfjsLib.PDFDocumentProxy> {
    if (this.isLoading) {
      throw new Error('Loading already in progress');
    }

    this.isLoading = true;
    this.startTime = Date.now();
    this.bytesTotal = file.size;

    try {
      if (file.size > this.config.chunkSize * 2) {
        return await this.loadFileWithChunking(file);
      } else {
        return await this.loadFileDirectly(file);
      }
    } catch (error) {
      this.isLoading = false;
      this.emit({
        type: 'error',
        progress: this.getProgress(),
        error: error as Error,
      });
      throw error;
    }
  }

  private async checkStreamingSupport(url: string): Promise<StreamingOptions> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentLength = parseInt(response.headers.get('content-length') || '0');
      const acceptRanges = response.headers.get('accept-ranges') === 'bytes';

      return {
        url,
        rangeSupport: acceptRanges && contentLength > 0,
        contentLength,
        acceptRanges,
      };
    } catch {
      return {
        url,
        rangeSupport: false,
        contentLength: 0,
        acceptRanges: false,
      };
    }
  }

  private async loadWithStreaming(url: string, options: StreamingOptions): Promise<pdfjsLib.PDFDocumentProxy> {
    this.bytesTotal = options.contentLength;

    // Create a custom range request transport
    // const transport = new ProgressiveTransport(url, {
      chunkSize: this.config.chunkSize,
      maxConcurrentChunks: this.config.maxConcurrentChunks,
      onProgress: (loaded, _total) => {
        this.bytesLoaded = loaded;
        this.updateDownloadSpeed();
        this.emit({
          type: 'progress',
          progress: this.getProgress(),
        });
      },
      onChunkLoaded: (chunkIndex, chunk) => {
        this.chunks.set(chunkIndex, chunk);
        this.emit({
          type: 'chunk-loaded',
          progress: this.getProgress(),
          data: { chunkIndex, chunkSize: chunk.byteLength },
        });
      },
    });

    // Load PDF with custom transport
    this.loadingTask = pdfjsLib.getDocument({
      url,
      httpHeaders: {
        'Range': 'bytes=0-',
      },
      withCredentials: false,
      isEvalSupported: false,
      disableAutoFetch: true,
      disableStream: false,
      disableRange: false,
    });

    this.setupLoadingTaskHandlers();
    this.document = await this.loadingTask.promise;

    // Emit metadata loaded event
    this.emit({
      type: 'metadata-loaded',
      progress: this.getProgress(),
      data: {
        numPages: this.document.numPages,
        fingerprint: this.document.fingerprint,
        info: await this.document.getMetadata(),
      },
    });

    // Start preloading pages
    this.preloadPages();

    this.isLoading = false;
    this.emit({
      type: 'complete',
      progress: this.getProgress(),
    });

    return this.document;
  }

  private async loadWithChunking(url: string): Promise<pdfjsLib.PDFDocumentProxy> {
    // First, get the file size
    const headResponse = await fetch(url, { method: 'HEAD' });
    this.bytesTotal = parseInt(headResponse.headers.get('content-length') || '0');

    // Load in chunks
    const chunks: ArrayBuffer[] = [];
    const totalChunks = Math.ceil(this.bytesTotal / this.config.chunkSize);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.config.chunkSize;
      const end = Math.min(start + this.config.chunkSize - 1, this.bytesTotal - 1);

      const response = await fetch(url, {
        headers: {
          'Range': `bytes=${start}-${end}`,
        },
      });

      const chunk = await response.arrayBuffer();
      chunks.push(chunk);
      this.bytesLoaded += chunk.byteLength;

      this.updateDownloadSpeed();
      this.emit({
        type: 'chunk-loaded',
        progress: this.getProgress(),
        data: { chunkIndex: i, chunkSize: chunk.byteLength },
      });

      this.emit({
        type: 'progress',
        progress: this.getProgress(),
      });
    }

    // Combine chunks
    const combinedBuffer = this.combineChunks(chunks);

    // Load PDF from combined buffer
    this.loadingTask = pdfjsLib.getDocument({
      data: combinedBuffer,
      isEvalSupported: false,
    });

    this.setupLoadingTaskHandlers();
    this.document = await this.loadingTask.promise;

    this.emit({
      type: 'metadata-loaded',
      progress: this.getProgress(),
      data: {
        numPages: this.document.numPages,
        fingerprint: this.document.fingerprint,
      },
    });

    this.preloadPages();

    this.isLoading = false;
    this.emit({
      type: 'complete',
      progress: this.getProgress(),
    });

    return this.document;
  }

  private async loadFileWithChunking(file: File): Promise<pdfjsLib.PDFDocumentProxy> {
    const chunks: ArrayBuffer[] = [];
    const totalChunks = Math.ceil(file.size / this.config.chunkSize);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * this.config.chunkSize;
      const end = Math.min(start + this.config.chunkSize, file.size);
      
      const chunk = file.slice(start, end);
      const arrayBuffer = await chunk.arrayBuffer();
      
      chunks.push(arrayBuffer);
      this.bytesLoaded += arrayBuffer.byteLength;

      this.updateDownloadSpeed();
      this.emit({
        type: 'chunk-loaded',
        progress: this.getProgress(),
        data: { chunkIndex: i, chunkSize: arrayBuffer.byteLength },
      });

      this.emit({
        type: 'progress',
        progress: this.getProgress(),
      });
    }

    const combinedBuffer = this.combineChunks(chunks);

    this.loadingTask = pdfjsLib.getDocument({
      data: combinedBuffer,
      isEvalSupported: false,
    });

    this.setupLoadingTaskHandlers();
    this.document = await this.loadingTask.promise;

    this.emit({
      type: 'metadata-loaded',
      progress: this.getProgress(),
      data: {
        numPages: this.document.numPages,
        fingerprint: this.document.fingerprint,
      },
    });

    this.preloadPages();

    this.isLoading = false;
    this.emit({
      type: 'complete',
      progress: this.getProgress(),
    });

    return this.document;
  }

  private async loadFileDirectly(file: File): Promise<pdfjsLib.PDFDocumentProxy> {
    const arrayBuffer = await file.arrayBuffer();
    this.bytesLoaded = arrayBuffer.byteLength;

    this.loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      isEvalSupported: false,
    });

    this.setupLoadingTaskHandlers();
    this.document = await this.loadingTask.promise;

    this.emit({
      type: 'metadata-loaded',
      progress: this.getProgress(),
      data: {
        numPages: this.document.numPages,
        fingerprint: this.document.fingerprint,
      },
    });

    this.isLoading = false;
    this.emit({
      type: 'complete',
      progress: this.getProgress(),
    });

    return this.document;
  }

  private setupLoadingTaskHandlers(): void {
    if (!this.loadingTask) return;

    this.loadingTask.onProgress = (progress) => {
      if (progress.total) {
        this.bytesTotal = progress.total;
        this.bytesLoaded = progress.loaded;
        this.updateDownloadSpeed();
        
        this.emit({
          type: 'progress',
          progress: this.getProgress(),
        });
      }
    };
  }

  private async preloadPages(): Promise<void> {
    if (!this.document) return;

    const pagesToPreload = Math.min(this.config.preloadPages, this.document.numPages);
    
    for (let i = 1; i <= pagesToPreload; i++) {
      try {
        const page = await this.document.getPage(i);
        this.loadedPages.add(i);
        
        this.emit({
          type: 'page-ready',
          progress: this.getProgress(),
          data: { pageNumber: i, page },
        });
      } catch (error) {
        console.warn(`Failed to preload page ${i}:`, error);
      }
    }
  }

  private combineChunks(chunks: ArrayBuffer[]): Uint8Array {
    const totalLength = chunks.reduce((sum, chunk) => sum + chunk.byteLength, 0);
    const combined = new Uint8Array(totalLength);
    
    let offset = 0;
    for (const chunk of chunks) {
      combined.set(new Uint8Array(chunk), offset);
      offset += chunk.byteLength;
    }
    
    return combined;
  }

  private updateDownloadSpeed(): void {
    const elapsed = (Date.now() - this.startTime) / 1000;
    const speed = this.bytesLoaded / elapsed;
    
    this.downloadSpeeds.push(speed);
    if (this.downloadSpeeds.length > 10) {
      this.downloadSpeeds.shift();
    }
  }

  private getAverageDownloadSpeed(): number {
    if (this.downloadSpeeds.length === 0) return 0;
    return this.downloadSpeeds.reduce((sum, speed) => sum + speed, 0) / this.downloadSpeeds.length;
  }

  private getProgress(): LoadingProgress {
    const percentage = this.bytesTotal > 0 ? (this.bytesLoaded / this.bytesTotal) * 100 : 0;
    const remainingBytes = this.bytesTotal - this.bytesLoaded;
    const avgSpeed = this.getAverageDownloadSpeed();
    const estimatedTimeRemaining = avgSpeed > 0 ? remainingBytes / avgSpeed : 0;

    return {
      bytesLoaded: this.bytesLoaded,
      bytesTotal: this.bytesTotal,
      percentage,
      pagesLoaded: this.loadedPages.size,
      pagesTotal: this.document?.numPages || 0,
      currentChunk: Math.floor(this.bytesLoaded / this.config.chunkSize),
      totalChunks: Math.ceil(this.bytesTotal / this.config.chunkSize),
      estimatedTimeRemaining,
      downloadSpeed: avgSpeed,
    };
  }

  public cancel(): void {
    if (this.loadingTask) {
      this.loadingTask.destroy();
      this.loadingTask = null;
    }
    this.isLoading = false;
    this.chunks.clear();
    this.loadedPages.clear();
  }

  public getDocument(): pdfjsLib.PDFDocumentProxy | null {
    return this.document;
  }

  public isPageLoaded(pageNumber: number): boolean {
    return this.loadedPages.has(pageNumber);
  }

  public getLoadingProgress(): LoadingProgress {
    return this.getProgress();
  }
}

// Custom transport for range requests
class ProgressiveTransport {
  private url: string;
  private options: {
    chunkSize: number;
    maxConcurrentChunks: number;
    onProgress: (loaded: number, total: number) => void;
    onChunkLoaded: (chunkIndex: number, chunk: ArrayBuffer) => void;
  };

  constructor(url: string, options: unknown) {
    this.url = url;
    this.options = options;
  }

  // Implementation would depend on PDF.js transport interface
  // This is a simplified version for demonstration
}
