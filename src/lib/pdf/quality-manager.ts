/**
 * PDF Rendering Quality Manager
 * Adaptive quality settings based on device capabilities and user preferences
 */

export interface DeviceCapabilities {
  screenDensity: number;
  memoryGB: number;
  cpuCores: number;
  gpuTier: 'low' | 'medium' | 'high' | 'unknown';
  batteryLevel: number | null;
  isLowPowerMode: boolean;
  connectionType: 'slow-2g' | '2g' | '3g' | '4g' | 'wifi' | 'unknown';
  isTouch: boolean;
  maxTextureSize: number;
}

export interface QualitySettings {
  renderScale: number;
  textLayerMode: 'disable' | 'enable' | 'enhance';
  annotationMode: 'disable' | 'enable' | 'enhance';
  imageQuality: 'low' | 'medium' | 'high' | 'ultra';
  enableWebGL: boolean;
  enableHardwareAcceleration: boolean;
  cacheStrategy: 'minimal' | 'balanced' | 'aggressive';
  preloadPages: number;
  maxConcurrentRenders: number;
  enableProgressiveRendering: boolean;
  compressionLevel: number; // 0-9
  antiAliasing: boolean;
  subpixelRendering: boolean;
}

export interface UserPreferences {
  preferredQuality: 'auto' | 'low' | 'medium' | 'high' | 'ultra';
  prioritizeBattery: boolean;
  prioritizeSpeed: boolean;
  enableAnimations: boolean;
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  colorBlindnessType: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
}

export interface QualityProfile {
  name: string;
  description: string;
  settings: QualitySettings;
  deviceRequirements: Partial<DeviceCapabilities>;
}

const QUALITY_PROFILES: QualityProfile[] = [
  {
    name: 'ultra',
    description: 'Maximum quality for high-end devices',
    settings: {
      renderScale: 2.0,
      textLayerMode: 'enhance',
      annotationMode: 'enhance',
      imageQuality: 'ultra',
      enableWebGL: true,
      enableHardwareAcceleration: true,
      cacheStrategy: 'aggressive',
      preloadPages: 10,
      maxConcurrentRenders: 4,
      enableProgressiveRendering: true,
      compressionLevel: 0,
      antiAliasing: true,
      subpixelRendering: true,
    },
    deviceRequirements: {
      memoryGB: 8,
      cpuCores: 4,
      gpuTier: 'high',
    },
  },
  {
    name: 'high',
    description: 'High quality for modern devices',
    settings: {
      renderScale: 1.5,
      textLayerMode: 'enhance',
      annotationMode: 'enable',
      imageQuality: 'high',
      enableWebGL: true,
      enableHardwareAcceleration: true,
      cacheStrategy: 'balanced',
      preloadPages: 5,
      maxConcurrentRenders: 3,
      enableProgressiveRendering: true,
      compressionLevel: 2,
      antiAliasing: true,
      subpixelRendering: true,
    },
    deviceRequirements: {
      memoryGB: 4,
      cpuCores: 2,
      gpuTier: 'medium',
    },
  },
  {
    name: 'medium',
    description: 'Balanced quality and performance',
    settings: {
      renderScale: 1.0,
      textLayerMode: 'enable',
      annotationMode: 'enable',
      imageQuality: 'medium',
      enableWebGL: false,
      enableHardwareAcceleration: true,
      cacheStrategy: 'balanced',
      preloadPages: 3,
      maxConcurrentRenders: 2,
      enableProgressiveRendering: true,
      compressionLevel: 4,
      antiAliasing: true,
      subpixelRendering: false,
    },
    deviceRequirements: {
      memoryGB: 2,
      cpuCores: 1,
    },
  },
  {
    name: 'low',
    description: 'Optimized for low-end devices',
    settings: {
      renderScale: 0.75,
      textLayerMode: 'enable',
      annotationMode: 'disable',
      imageQuality: 'low',
      enableWebGL: false,
      enableHardwareAcceleration: false,
      cacheStrategy: 'minimal',
      preloadPages: 1,
      maxConcurrentRenders: 1,
      enableProgressiveRendering: false,
      compressionLevel: 6,
      antiAliasing: false,
      subpixelRendering: false,
    },
    deviceRequirements: {
      memoryGB: 1,
    },
  },
  {
    name: 'battery-saver',
    description: 'Optimized for battery life',
    settings: {
      renderScale: 0.8,
      textLayerMode: 'enable',
      annotationMode: 'disable',
      imageQuality: 'low',
      enableWebGL: false,
      enableHardwareAcceleration: false,
      cacheStrategy: 'minimal',
      preloadPages: 1,
      maxConcurrentRenders: 1,
      enableProgressiveRendering: false,
      compressionLevel: 8,
      antiAliasing: false,
      subpixelRendering: false,
    },
    deviceRequirements: {},
  },
];

export class QualityManager {
  private deviceCapabilities: DeviceCapabilities;
  private userPreferences: UserPreferences;
  private currentSettings: QualitySettings;
  private performanceMetrics: {
    averageRenderTime: number;
    memoryUsage: number;
    frameRate: number;
    batteryDrain: number;
  };

  constructor(userPreferences: Partial<UserPreferences> = {}) {
    this.deviceCapabilities = this.detectDeviceCapabilities();
    this.userPreferences = this.getDefaultPreferences(userPreferences);
    this.currentSettings = this.calculateOptimalSettings();
    this.performanceMetrics = {
      averageRenderTime: 0,
      memoryUsage: 0,
      frameRate: 60,
      batteryDrain: 0,
    };
  }

  private detectDeviceCapabilities(): DeviceCapabilities {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    return {
      screenDensity: window.devicePixelRatio || 1,
      memoryGB: this.estimateDeviceMemory(),
      cpuCores: navigator.hardwareConcurrency || 2,
      gpuTier: this.detectGPUTier(gl),
      batteryLevel: this.getBatteryLevel(),
      isLowPowerMode: this.isLowPowerMode(),
      connectionType: this.getConnectionType(),
      isTouch: 'ontouchstart' in window,
      maxTextureSize: this.getMaxTextureSize(gl),
    };
  }

  private estimateDeviceMemory(): number {
    // Use Device Memory API if available
    if ('deviceMemory' in navigator) {
      return (navigator as Navigator & { deviceMemory: number }).deviceMemory;
    }

    // Fallback estimation based on other factors
    const userAgent = navigator.userAgent;
    if (/iPhone|iPad/.test(userAgent)) {
      // iOS device estimation
      if (/iPhone.*15|iPad.*15/.test(userAgent)) return 6;
      if (/iPhone.*14|iPad.*14/.test(userAgent)) return 4;
      if (/iPhone.*13|iPad.*13/.test(userAgent)) return 4;
      return 2;
    }

    if (/Android/.test(userAgent)) {
      // Android device estimation
      const cores = navigator.hardwareConcurrency || 2;
      if (cores >= 8) return 8;
      if (cores >= 4) return 4;
      return 2;
    }

    // Desktop estimation
    const cores = navigator.hardwareConcurrency || 4;
    if (cores >= 8) return 16;
    if (cores >= 4) return 8;
    return 4;
  }

  private detectGPUTier(gl: WebGLRenderingContext | null): DeviceCapabilities['gpuTier'] {
    if (!gl) return 'unknown';

    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (!debugInfo) return 'unknown';

    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);


    // High-end GPUs
    if (/RTX|GTX 16|GTX 20|GTX 30|GTX 40|RX 6|RX 7|M1|M2|A15|A16/.test(renderer)) {
      return 'high';
    }

    // Medium-end GPUs
    if (/GTX|RX|Intel Iris|Adreno 6|Mali-G/.test(renderer)) {
      return 'medium';
    }

    // Low-end or integrated GPUs
    return 'low';
  }

  private getBatteryLevel(): number | null {
    // Battery API is deprecated but still available in some browsers
    if ('getBattery' in navigator) {
      (navigator as Navigator & { getBattery: () => Promise<{ level: number }> }).getBattery().then((battery) => {
        return battery.level;
      }).catch(() => null);
    }
    return null;
  }

  private isLowPowerMode(): boolean {
    // Detect low power mode indicators
    if (this.deviceCapabilities?.batteryLevel && this.deviceCapabilities.batteryLevel < 0.2) {
      return true;
    }

    // Check for reduced motion preference as indicator
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  }

  private getConnectionType(): DeviceCapabilities['connectionType'] {
    if ('connection' in navigator) {
      const connection = (navigator as Navigator & { connection: { effectiveType: string } }).connection;
      return connection.effectiveType || 'unknown';
    }
    return 'unknown';
  }

  private getMaxTextureSize(gl: WebGLRenderingContext | null): number {
    if (!gl) return 2048;
    return gl.getParameter(gl.MAX_TEXTURE_SIZE) || 2048;
  }

  private getDefaultPreferences(overrides: Partial<UserPreferences>): UserPreferences {
    return {
      preferredQuality: 'auto',
      prioritizeBattery: false,
      prioritizeSpeed: false,
      enableAnimations: true,
      reducedMotion: window.matchMedia('(prefers-reduced-motion: reduce)').matches,
      highContrast: window.matchMedia('(prefers-contrast: high)').matches,
      largeText: false,
      colorBlindnessType: 'none',
      ...overrides,
    };
  }

  private calculateOptimalSettings(): QualitySettings {
    // Start with user preference if not auto
    if (this.userPreferences.preferredQuality !== 'auto') {
      const profile = QUALITY_PROFILES.find(p => p.name === this.userPreferences.preferredQuality);
      if (profile) {
        return this.adjustSettingsForDevice(profile.settings);
      }
    }

    // Auto-detect best profile based on device capabilities
    const suitableProfiles = QUALITY_PROFILES.filter(profile => 
      this.isProfileSuitable(profile)
    );

    // Choose the highest quality suitable profile
    const selectedProfile = suitableProfiles[0] || QUALITY_PROFILES[QUALITY_PROFILES.length - 1];
    
    return this.adjustSettingsForDevice(selectedProfile.settings);
  }

  private isProfileSuitable(profile: QualityProfile): boolean {
    const req = profile.deviceRequirements;
    const caps = this.deviceCapabilities;

    // Check memory requirement
    if (req.memoryGB && caps.memoryGB < req.memoryGB) return false;
    
    // Check CPU requirement
    if (req.cpuCores && caps.cpuCores < req.cpuCores) return false;
    
    // Check GPU requirement
    if (req.gpuTier) {
      const tierOrder = { low: 0, medium: 1, high: 2, unknown: -1 };
      if (tierOrder[caps.gpuTier] < tierOrder[req.gpuTier]) return false;
    }

    return true;
  }

  private adjustSettingsForDevice(baseSettings: QualitySettings): QualitySettings {
    const settings = { ...baseSettings };

    // Adjust for battery saver mode
    if (this.userPreferences.prioritizeBattery || this.deviceCapabilities.isLowPowerMode) {
      settings.renderScale = Math.min(settings.renderScale, 1.0);
      settings.enableWebGL = false;
      settings.enableHardwareAcceleration = false;
      settings.preloadPages = Math.min(settings.preloadPages, 2);
      settings.maxConcurrentRenders = 1;
      settings.compressionLevel = Math.max(settings.compressionLevel, 6);
    }

    // Adjust for speed priority
    if (this.userPreferences.prioritizeSpeed) {
      settings.enableProgressiveRendering = true;
      settings.maxConcurrentRenders = Math.max(settings.maxConcurrentRenders, 2);
      settings.cacheStrategy = 'aggressive';
    }

    // Adjust for accessibility preferences
    if (this.userPreferences.reducedMotion) {
      settings.enableProgressiveRendering = false;
    }

    if (this.userPreferences.highContrast) {
      settings.antiAliasing = false;
      settings.subpixelRendering = false;
    }

    // Adjust for connection type
    if (['slow-2g', '2g'].includes(this.deviceCapabilities.connectionType)) {
      settings.preloadPages = Math.min(settings.preloadPages, 1);
      settings.compressionLevel = Math.max(settings.compressionLevel, 7);
    }

    // Adjust for touch devices
    if (this.deviceCapabilities.isTouch) {
      settings.renderScale = Math.max(settings.renderScale, 1.0);
    }

    return settings;
  }

  public getCurrentSettings(): QualitySettings {
    return { ...this.currentSettings };
  }

  public updateUserPreferences(preferences: Partial<UserPreferences>): void {
    this.userPreferences = { ...this.userPreferences, ...preferences };
    this.currentSettings = this.calculateOptimalSettings();
  }

  public updatePerformanceMetrics(metrics: Partial<typeof this.performanceMetrics>): void {
    this.performanceMetrics = { ...this.performanceMetrics, ...metrics };
    
    // Auto-adjust settings based on performance
    this.autoAdjustForPerformance();
  }

  private autoAdjustForPerformance(): void {
    const { averageRenderTime, frameRate, memoryUsage } = this.performanceMetrics;

    // If performance is poor, reduce quality
    if (averageRenderTime > 1000 || frameRate < 30 || memoryUsage > 0.8) {
      this.currentSettings = this.adjustSettingsForDevice({
        ...this.currentSettings,
        renderScale: Math.max(this.currentSettings.renderScale * 0.9, 0.5),
        preloadPages: Math.max(this.currentSettings.preloadPages - 1, 1),
        maxConcurrentRenders: Math.max(this.currentSettings.maxConcurrentRenders - 1, 1),
        compressionLevel: Math.min(this.currentSettings.compressionLevel + 1, 9),
      });
    }

    // If performance is excellent, try to increase quality
    if (averageRenderTime < 200 && frameRate >= 60 && memoryUsage < 0.5) {
      this.currentSettings = this.adjustSettingsForDevice({
        ...this.currentSettings,
        renderScale: Math.min(this.currentSettings.renderScale * 1.1, 2.0),
        preloadPages: Math.min(this.currentSettings.preloadPages + 1, 10),
        compressionLevel: Math.max(this.currentSettings.compressionLevel - 1, 0),
      });
    }
  }

  public getDeviceCapabilities(): DeviceCapabilities {
    return { ...this.deviceCapabilities };
  }

  public getUserPreferences(): UserPreferences {
    return { ...this.userPreferences };
  }

  public getAvailableProfiles(): QualityProfile[] {
    return QUALITY_PROFILES.map(profile => ({
      ...profile,
      settings: { ...profile.settings },
      deviceRequirements: { ...profile.deviceRequirements },
    }));
  }

  public setCustomSettings(settings: Partial<QualitySettings>): void {
    this.currentSettings = { ...this.currentSettings, ...settings };
  }

  public resetToOptimal(): void {
    this.currentSettings = this.calculateOptimalSettings();
  }

  public exportSettings(): string {
    return JSON.stringify({
      userPreferences: this.userPreferences,
      customSettings: this.currentSettings,
      deviceCapabilities: this.deviceCapabilities,
    }, null, 2);
  }

  public importSettings(settingsJson: string): boolean {
    try {
      const imported = JSON.parse(settingsJson);
      
      if (imported.userPreferences) {
        this.updateUserPreferences(imported.userPreferences);
      }
      
      if (imported.customSettings) {
        this.setCustomSettings(imported.customSettings);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }
}
