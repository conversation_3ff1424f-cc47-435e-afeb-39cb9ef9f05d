/**
 * Comprehensive Keyboard Navigation System
 * Advanced keyboard shortcuts and navigation patterns for PDF viewer
 */

export interface KeyboardShortcut {
  id: string;
  name: string;
  description: string;
  keys: string[];
  category: 'navigation' | 'zoom' | 'view' | 'accessibility' | 'search' | 'annotation' | 'general';
  action: () => void;
  enabled: boolean;
  customizable: boolean;
  context?: 'global' | 'document' | 'search' | 'annotation';
}

export interface KeyboardNavigationConfig {
  enableGlobalShortcuts: boolean;
  enableContextualShortcuts: boolean;
  enableCustomShortcuts: boolean;
  enableHelpOverlay: boolean;
  enableKeyboardTraps: boolean;
  enableFocusManagement: boolean;
  shortcutDelay: number;
  repeatDelay: number;
  enableHapticFeedback: boolean;
}

export interface NavigationState {
  currentContext: 'global' | 'document' | 'search' | 'annotation' | 'menu';
  focusedElement: HTMLElement | null;
  focusHistory: HTMLElement[];
  keySequence: string[];
  lastKeyTime: number;
  isComboActive: boolean;
  activeShortcuts: Set<string>;
}

export interface KeyboardEvent {
  type: 'keydown' | 'keyup' | 'shortcut' | 'sequence';
  key: string;
  code: string;
  modifiers: {
    ctrl: boolean;
    alt: boolean;
    shift: boolean;
    meta: boolean;
  };
  target: HTMLElement;
  shortcut?: KeyboardShortcut;
  sequence?: string[];
  timestamp: number;
}

const DEFAULT_CONFIG: KeyboardNavigationConfig = {
  enableGlobalShortcuts: true,
  enableContextualShortcuts: true,
  enableCustomShortcuts: true,
  enableHelpOverlay: true,
  enableKeyboardTraps: true,
  enableFocusManagement: true,
  shortcutDelay: 500,
  repeatDelay: 100,
  enableHapticFeedback: false,
};

export class ComprehensiveKeyboardNavigation {
  private config: KeyboardNavigationConfig;
  private shortcuts: Map<string, KeyboardShortcut> = new Map();
  private customShortcuts: Map<string, KeyboardShortcut> = new Map();
  private navigationState: NavigationState;
  private eventListeners: Map<string, Set<(event: KeyboardEvent) => void>> = new Map();
  private keySequenceTimer: number | null = null;
  private repeatTimer: number | null = null;
  private helpOverlay: HTMLElement | null = null;
  private focusTrap: HTMLElement | null = null;

  constructor(config: Partial<KeyboardNavigationConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.navigationState = {
      currentContext: 'global',
      focusedElement: null,
      focusHistory: [],
      keySequence: [],
      lastKeyTime: 0,
      isComboActive: false,
      activeShortcuts: new Set(),
    };

    this.initializeDefaultShortcuts();
    this.setupEventListeners();
    this.loadCustomShortcuts();
  }

  private initializeDefaultShortcuts(): void {
    const defaultShortcuts: Omit<KeyboardShortcut, 'action'>[] = [
      // Navigation shortcuts
      { id: 'next-page', name: 'Next Page', description: 'Go to next page', keys: ['ArrowRight'], category: 'navigation', enabled: true, customizable: true },
      { id: 'prev-page', name: 'Previous Page', description: 'Go to previous page', keys: ['ArrowLeft'], category: 'navigation', enabled: true, customizable: true },
      { id: 'first-page', name: 'First Page', description: 'Go to first page', keys: ['Home'], category: 'navigation', enabled: true, customizable: true },
      { id: 'last-page', name: 'Last Page', description: 'Go to last page', keys: ['End'], category: 'navigation', enabled: true, customizable: true },
      { id: 'page-down', name: 'Page Down', description: 'Scroll down one page', keys: ['PageDown', 'Space'], category: 'navigation', enabled: true, customizable: true },
      { id: 'page-up', name: 'Page Up', description: 'Scroll up one page', keys: ['PageUp', 'Shift+Space'], category: 'navigation', enabled: true, customizable: true },

      // Zoom shortcuts
      { id: 'zoom-in', name: 'Zoom In', description: 'Increase zoom level', keys: ['Ctrl+=', 'Ctrl+Plus'], category: 'zoom', enabled: true, customizable: true },
      { id: 'zoom-out', name: 'Zoom Out', description: 'Decrease zoom level', keys: ['Ctrl+-', 'Ctrl+Minus'], category: 'zoom', enabled: true, customizable: true },
      { id: 'zoom-reset', name: 'Reset Zoom', description: 'Reset zoom to 100%', keys: ['Ctrl+0'], category: 'zoom', enabled: true, customizable: true },
      { id: 'zoom-fit', name: 'Fit to Page', description: 'Fit page to window', keys: ['Ctrl+1'], category: 'zoom', enabled: true, customizable: true },
      { id: 'zoom-fit-width', name: 'Fit to Width', description: 'Fit page width to window', keys: ['Ctrl+2'], category: 'zoom', enabled: true, customizable: true },

      // View shortcuts
      { id: 'fullscreen', name: 'Toggle Fullscreen', description: 'Enter or exit fullscreen mode', keys: ['F11'], category: 'view', enabled: true, customizable: true },
      { id: 'rotate-clockwise', name: 'Rotate Clockwise', description: 'Rotate page 90° clockwise', keys: ['Ctrl+Shift+R'], category: 'view', enabled: true, customizable: true },
      { id: 'rotate-counter', name: 'Rotate Counter-clockwise', description: 'Rotate page 90° counter-clockwise', keys: ['Ctrl+Shift+L'], category: 'view', enabled: true, customizable: true },
      { id: 'toggle-sidebar', name: 'Toggle Sidebar', description: 'Show or hide sidebar', keys: ['Ctrl+Shift+S'], category: 'view', enabled: true, customizable: true },

      // Search shortcuts
      { id: 'search', name: 'Search', description: 'Open search dialog', keys: ['Ctrl+F'], category: 'search', enabled: true, customizable: true },
      { id: 'search-next', name: 'Find Next', description: 'Find next search result', keys: ['F3', 'Ctrl+G'], category: 'search', enabled: true, customizable: true },
      { id: 'search-prev', name: 'Find Previous', description: 'Find previous search result', keys: ['Shift+F3', 'Ctrl+Shift+G'], category: 'search', enabled: true, customizable: true },
      { id: 'search-close', name: 'Close Search', description: 'Close search dialog', keys: ['Escape'], category: 'search', enabled: true, customizable: true, context: 'search' },

      // Accessibility shortcuts
      { id: 'next-heading', name: 'Next Heading', description: 'Navigate to next heading', keys: ['Alt+H'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'prev-heading', name: 'Previous Heading', description: 'Navigate to previous heading', keys: ['Alt+Shift+H'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'next-paragraph', name: 'Next Paragraph', description: 'Navigate to next paragraph', keys: ['Alt+P'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'prev-paragraph', name: 'Previous Paragraph', description: 'Navigate to previous paragraph', keys: ['Alt+Shift+P'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'next-list', name: 'Next List', description: 'Navigate to next list', keys: ['Alt+L'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'next-table', name: 'Next Table', description: 'Navigate to next table', keys: ['Alt+T'], category: 'accessibility', enabled: true, customizable: true },
      { id: 'toggle-screen-reader', name: 'Toggle Screen Reader Mode', description: 'Enable or disable screen reader mode', keys: ['Alt+Shift+A'], category: 'accessibility', enabled: true, customizable: true },

      // General shortcuts
      { id: 'help', name: 'Show Help', description: 'Show keyboard shortcuts help', keys: ['?', 'F1'], category: 'general', enabled: true, customizable: true },
      { id: 'escape', name: 'Escape', description: 'Cancel current action or close dialogs', keys: ['Escape'], category: 'general', enabled: true, customizable: false },
      { id: 'goto-page', name: 'Go to Page', description: 'Open go to page dialog', keys: ['Ctrl+G'], category: 'general', enabled: true, customizable: true },
      { id: 'print', name: 'Print', description: 'Print document', keys: ['Ctrl+P'], category: 'general', enabled: true, customizable: true },
      { id: 'download', name: 'Download', description: 'Download document', keys: ['Ctrl+S'], category: 'general', enabled: true, customizable: true },
    ];

    defaultShortcuts.forEach(shortcut => {
      this.shortcuts.set(shortcut.id, {
        ...shortcut,
        action: () => this.emit('shortcut-triggered', { shortcut }),
      });
    });
  }

  private setupEventListeners(): void {
    if (typeof document === 'undefined') return;

    document.addEventListener('keydown', this.handleKeyDown.bind(this), true);
    document.addEventListener('keyup', this.handleKeyUp.bind(this), true);
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));

    // Handle visibility change to reset state
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.resetNavigationState();
      }
    });
  }

  private handleKeyDown(event: globalThis.KeyboardEvent): void {
    const keyboardEvent = this.createKeyboardEvent('keydown', event);
    
    // Update navigation state
    this.updateNavigationState(keyboardEvent);
    
    // Check for shortcuts
    const matchedShortcut = this.findMatchingShortcut(keyboardEvent);
    
    if (matchedShortcut) {
      // Check if shortcut is enabled and context matches
      if (this.isShortcutActive(matchedShortcut)) {
        event.preventDefault();
        event.stopPropagation();
        
        this.executeShortcut(matchedShortcut);
        this.emit('shortcut-executed', { shortcut: matchedShortcut, event: keyboardEvent });
        
        // Haptic feedback
        if (this.config.enableHapticFeedback && 'vibrate' in navigator) {
          navigator.vibrate(50);
        }
      }
    }
    
    // Handle key sequences
    this.handleKeySequence(keyboardEvent);
    
    this.emit('keydown', keyboardEvent);
  }

  private handleKeyUp(event: globalThis.KeyboardEvent): void {
    const keyboardEvent = this.createKeyboardEvent('keyup', event);
    this.emit('keyup', keyboardEvent);
  }

  private handleFocusIn(event: FocusEvent): void {
    const target = event.target as HTMLElement;
    this.updateFocus(target);
  }

  private handleFocusOut(_event: FocusEvent): void {
    // Update focus history
    if (this.navigationState.focusedElement) {
      this.navigationState.focusHistory.push(this.navigationState.focusedElement);
      
      // Limit history size
      if (this.navigationState.focusHistory.length > 10) {
        this.navigationState.focusHistory.shift();
      }
    }
  }

  private createKeyboardEvent(type: 'keydown' | 'keyup', event: globalThis.KeyboardEvent): KeyboardEvent {
    return {
      type,
      key: event.key,
      code: event.code,
      modifiers: {
        ctrl: event.ctrlKey,
        alt: event.altKey,
        shift: event.shiftKey,
        meta: event.metaKey,
      },
      target: event.target as HTMLElement,
      timestamp: Date.now(),
    };
  }

  private updateNavigationState(event: KeyboardEvent): void {
    this.navigationState.lastKeyTime = event.timestamp;
    
    // Update key sequence
    const keyString = this.getKeyString(event);
    this.navigationState.keySequence.push(keyString);
    
    // Limit sequence length
    if (this.navigationState.keySequence.length > 5) {
      this.navigationState.keySequence.shift();
    }
    
    // Update context based on focused element
    this.updateContext(event.target);
  }

  private updateContext(target: HTMLElement): void {
    if (target.closest('[data-search-panel]')) {
      this.navigationState.currentContext = 'search';
    } else if (target.closest('[data-annotation-panel]')) {
      this.navigationState.currentContext = 'annotation';
    } else if (target.closest('[data-menu]')) {
      this.navigationState.currentContext = 'menu';
    } else if (target.closest('[data-pdf-viewer]')) {
      this.navigationState.currentContext = 'document';
    } else {
      this.navigationState.currentContext = 'global';
    }
  }

  private updateFocus(element: HTMLElement): void {
    this.navigationState.focusedElement = element;
    this.emit('focus-changed', { element, context: this.navigationState.currentContext });
  }

  private getKeyString(event: KeyboardEvent): string {
    const modifiers = [];
    if (event.modifiers.ctrl) modifiers.push('Ctrl');
    if (event.modifiers.alt) modifiers.push('Alt');
    if (event.modifiers.shift) modifiers.push('Shift');
    if (event.modifiers.meta) modifiers.push('Meta');
    
    return [...modifiers, event.key].join('+');
  }

  private findMatchingShortcut(event: KeyboardEvent): KeyboardShortcut | null {
    const keyString = this.getKeyString(event);
    
    // Check custom shortcuts first
    for (const shortcut of this.customShortcuts.values()) {
      if (shortcut.keys.includes(keyString)) {
        return shortcut;
      }
    }
    
    // Check default shortcuts
    for (const shortcut of this.shortcuts.values()) {
      if (shortcut.keys.includes(keyString)) {
        return shortcut;
      }
    }
    
    return null;
  }

  private isShortcutActive(shortcut: KeyboardShortcut): boolean {
    if (!shortcut.enabled) return false;
    
    // Check context
    if (shortcut.context && shortcut.context !== this.navigationState.currentContext) {
      return false;
    }
    
    // Check if global shortcuts are enabled
    if (!this.config.enableGlobalShortcuts && shortcut.context === 'global') {
      return false;
    }
    
    // Check if contextual shortcuts are enabled
    if (!this.config.enableContextualShortcuts && shortcut.context !== 'global') {
      return false;
    }
    
    return true;
  }

  private executeShortcut(shortcut: KeyboardShortcut): void {
    this.navigationState.activeShortcuts.add(shortcut.id);
    
    try {
      shortcut.action();
    } catch (error) {
      console.error('Error executing shortcut:', shortcut.id, error);
    }
    
    // Remove from active shortcuts after delay
    setTimeout(() => {
      this.navigationState.activeShortcuts.delete(shortcut.id);
    }, this.config.shortcutDelay);
  }

  private handleKeySequence(event: KeyboardEvent): void {
    // Clear sequence timer
    if (this.keySequenceTimer) {
      clearTimeout(this.keySequenceTimer);
    }
    
    // Set new timer to clear sequence
    this.keySequenceTimer = window.setTimeout(() => {
      this.navigationState.keySequence = [];
      this.navigationState.isComboActive = false;
    }, this.config.shortcutDelay);
    
    // Check for sequence shortcuts
    const sequence = this.navigationState.keySequence.join(' ');
    this.emit('key-sequence', { sequence, event });
  }

  public registerShortcut(shortcut: Omit<KeyboardShortcut, 'id'> & { id?: string }): string {
    const id = shortcut.id || `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    const fullShortcut: KeyboardShortcut = {
      ...shortcut,
      id,
      customizable: true,
    };
    
    this.customShortcuts.set(id, fullShortcut);
    this.saveCustomShortcuts();
    
    this.emit('shortcut-registered', { shortcut: fullShortcut });
    return id;
  }

  public unregisterShortcut(id: string): boolean {
    const removed = this.customShortcuts.delete(id);
    if (removed) {
      this.saveCustomShortcuts();
      this.emit('shortcut-unregistered', { id });
    }
    return removed;
  }

  public updateShortcut(id: string, updates: Partial<KeyboardShortcut>): boolean {
    const shortcut = this.shortcuts.get(id) || this.customShortcuts.get(id);
    if (!shortcut || !shortcut.customizable) return false;
    
    Object.assign(shortcut, updates);
    
    if (this.customShortcuts.has(id)) {
      this.saveCustomShortcuts();
    }
    
    this.emit('shortcut-updated', { shortcut });
    return true;
  }

  public getShortcuts(category?: KeyboardShortcut['category']): KeyboardShortcut[] {
    const allShortcuts = [
      ...Array.from(this.shortcuts.values()),
      ...Array.from(this.customShortcuts.values()),
    ];
    
    return category 
      ? allShortcuts.filter(s => s.category === category)
      : allShortcuts;
  }

  public getShortcutsByContext(context: string): KeyboardShortcut[] {
    return this.getShortcuts().filter(s => !s.context || s.context === context);
  }

  public showHelpOverlay(): void {
    if (!this.config.enableHelpOverlay || this.helpOverlay || typeof document === 'undefined') return;

    this.helpOverlay = this.createHelpOverlay();
    document.body.appendChild(this.helpOverlay);

    // Focus the overlay
    this.helpOverlay.focus();

    this.emit('help-shown', {});
  }

  public hideHelpOverlay(): void {
    if (this.helpOverlay) {
      this.helpOverlay.remove();
      this.helpOverlay = null;
      this.emit('help-hidden', {});
    }
  }

  private createHelpOverlay(): HTMLElement {
    const overlay = document.createElement('div');
    overlay.className = 'keyboard-help-overlay';
    overlay.setAttribute('role', 'dialog');
    overlay.setAttribute('aria-label', 'Keyboard Shortcuts Help');
    overlay.setAttribute('tabindex', '-1');
    
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.8);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
      background: white;
      border-radius: 8px;
      padding: 24px;
      max-width: 800px;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    `;
    
    content.innerHTML = this.generateHelpContent();
    overlay.appendChild(content);
    
    // Close on escape or click outside
    overlay.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        this.hideHelpOverlay();
      }
    });
    
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        this.hideHelpOverlay();
      }
    });
    
    return overlay;
  }

  private generateHelpContent(): string {
    const categories = ['navigation', 'zoom', 'view', 'search', 'accessibility', 'general'] as const;
    const shortcuts = this.getShortcuts();
    
    let html = '<h2>Keyboard Shortcuts</h2>';
    
    categories.forEach(category => {
      const categoryShortcuts = shortcuts.filter(s => s.category === category && s.enabled);
      if (categoryShortcuts.length === 0) return;
      
      html += `<h3>${category.charAt(0).toUpperCase() + category.slice(1)}</h3>`;
      html += '<table style="width: 100%; margin-bottom: 20px;">';
      
      categoryShortcuts.forEach(shortcut => {
        html += `
          <tr>
            <td style="padding: 4px 8px; font-family: monospace; background: #f5f5f5; border-radius: 4px; margin-right: 8px;">
              ${shortcut.keys.join(' or ')}
            </td>
            <td style="padding: 4px 8px;">
              <strong>${shortcut.name}</strong><br>
              <small style="color: #666;">${shortcut.description}</small>
            </td>
          </tr>
        `;
      });
      
      html += '</table>';
    });
    
    html += '<p style="text-align: center; margin-top: 20px;"><small>Press Escape to close</small></p>';
    
    return html;
  }

  private loadCustomShortcuts(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const saved = localStorage.getItem('keyboard-shortcuts-custom');
      if (saved) {
        const shortcuts = JSON.parse(saved);
        if (Array.isArray(shortcuts)) {
          shortcuts.forEach((shortcut: KeyboardShortcut) => {
            if (shortcut && typeof shortcut === 'object') {
              this.customShortcuts.set(shortcut.id, shortcut);
            }
          });
        }
      }
    } catch (error) {
      console.warn('Failed to load custom shortcuts:', error);
    }
  }

  private saveCustomShortcuts(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const shortcuts = Array.from(this.customShortcuts.values());
      localStorage.setItem('keyboard-shortcuts-custom', JSON.stringify(shortcuts));
    } catch (error) {
      console.warn('Failed to save custom shortcuts:', error);
    }
  }

  private resetNavigationState(): void {
    this.navigationState.keySequence = [];
    this.navigationState.isComboActive = false;
    this.navigationState.activeShortcuts.clear();
    
    if (this.keySequenceTimer) {
      clearTimeout(this.keySequenceTimer);
      this.keySequenceTimer = null;
    }
    
    if (this.repeatTimer) {
      clearTimeout(this.repeatTimer);
      this.repeatTimer = null;
    }
  }

  public getNavigationState(): NavigationState {
    return { ...this.navigationState };
  }

  public setContext(context: NavigationState['currentContext']): void {
    this.navigationState.currentContext = context;
    this.emit('context-changed', { context });
  }

  public addEventListener(type: string, listener: (event: unknown) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (event: unknown) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: unknown): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Keyboard navigation event listener error:', error);
      }
    });
  }

  public destroy(): void {
    this.resetNavigationState();
    this.hideHelpOverlay();
    this.eventListeners.clear();
    
    document.removeEventListener('keydown', this.handleKeyDown.bind(this), true);
    document.removeEventListener('keyup', this.handleKeyUp.bind(this), true);
    document.removeEventListener('focusin', this.handleFocusIn.bind(this));
    document.removeEventListener('focusout', this.handleFocusOut.bind(this));
  }
}
