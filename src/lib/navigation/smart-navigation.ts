/**
 * Smart Navigation System
 * Intelligent navigation with reading progress, smart bookmarks, and content analysis
 */

export interface ReadingProgress {
  documentId: string;
  currentPage: number;
  totalPages: number;
  percentage: number;
  timeSpent: number; // in milliseconds
  lastReadDate: Date;
  readingSpeed: number; // pages per minute
  estimatedTimeRemaining: number; // in minutes
  completedSections: string[];
  bookmarks: SmartBookmark[];
}

export interface SmartBookmark {
  id: string;
  documentId: string;
  pageNumber: number;
  title: string;
  description?: string;
  type: 'manual' | 'auto' | 'important' | 'reference' | 'note';
  category?: string;
  tags: string[];
  createdAt: Date;
  lastAccessed?: Date;
  accessCount: number;
  context: {
    surroundingText: string;
    position: { x: number; y: number };
    viewport: { zoom: number; rotation: number };
  };
  metadata: {
    importance: number; // 0-1 scale
    relevance: number; // 0-1 scale
    difficulty: number; // 0-1 scale
    estimatedReadTime: number; // in minutes
  };
}

export interface DocumentOutline {
  id: string;
  title: string;
  level: number;
  pageNumber: number;
  children: DocumentOutline[];
  type: 'heading' | 'section' | 'chapter' | 'appendix' | 'reference';
  importance: number;
  estimatedReadTime: number;
  isRead: boolean;
  lastVisited?: Date;
}

export interface ReadingSession {
  id: string;
  documentId: string;
  startTime: Date;
  endTime?: Date;
  pagesRead: number[];
  timePerPage: Map<number, number>;
  totalTime: number;
  averageReadingSpeed: number;
  focusScore: number; // 0-1 based on interaction patterns
  comprehensionIndicators: {
    backtracking: number; // how often user goes back
    pauseTime: number; // time spent paused
    annotationCount: number;
    searchCount: number;
  };
}

export interface NavigationPreferences {
  autoBookmarkImportantSections: boolean;
  trackReadingProgress: boolean;
  enableSmartSuggestions: boolean;
  readingSpeedTarget: number; // pages per minute
  preferredNavigationMode: 'linear' | 'topic-based' | 'adaptive';
  bookmarkCategories: string[];
  reminderSettings: {
    enabled: boolean;
    interval: number; // in minutes
    type: 'gentle' | 'persistent';
  };
}

const DEFAULT_PREFERENCES: NavigationPreferences = {
  autoBookmarkImportantSections: true,
  trackReadingProgress: true,
  enableSmartSuggestions: true,
  readingSpeedTarget: 2, // 2 pages per minute
  preferredNavigationMode: 'adaptive',
  bookmarkCategories: ['Important', 'Reference', 'Questions', 'Summary'],
  reminderSettings: {
    enabled: false,
    interval: 30,
    type: 'gentle',
  },
};

export class SmartNavigationManager {
  private readingProgress: Map<string, ReadingProgress> = new Map();
  private documentOutlines: Map<string, DocumentOutline[]> = new Map();
  private readingSessions: Map<string, ReadingSession[]> = new Map();
  private preferences: NavigationPreferences;
  private currentSession: ReadingSession | null = null;
  private pageStartTime: number = 0;
  private eventListeners: Map<string, Set<(data: unknown) => void>> = new Map();

  constructor(preferences: Partial<NavigationPreferences> = {}) {
    this.preferences = { ...DEFAULT_PREFERENCES, ...preferences };
    this.loadFromStorage();
    this.setupEventListeners();
  }

  private loadFromStorage(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const progress = localStorage.getItem('smart-navigation-progress');
      if (progress) {
        const data = JSON.parse(progress);
        if (data && typeof data === 'object') {
          for (const [docId, progressData] of Object.entries(data)) {
            if (progressData && typeof progressData === 'object') {
              const typedData = progressData as unknown;
              this.readingProgress.set(docId, {
                ...typedData,
                lastReadDate: new Date(typedData.lastReadDate || Date.now()),
                bookmarks: Array.isArray(typedData.bookmarks) ? typedData.bookmarks.map((b: unknown) => ({
                  ...b,
                  createdAt: new Date(b.createdAt || Date.now()),
                  lastAccessed: b.lastAccessed ? new Date(b.lastAccessed) : undefined,
                })) : [],
              });
            }
          }
        }
      }

      const sessions = localStorage.getItem('smart-navigation-sessions');
      if (sessions) {
        const data = JSON.parse(sessions);
        if (data && typeof data === 'object') {
          for (const [docId, sessionData] of Object.entries(data)) {
            if (Array.isArray(sessionData)) {
              this.readingSessions.set(docId, sessionData.map((s: unknown) => ({
                ...s,
                startTime: new Date(s.startTime || Date.now()),
                endTime: s.endTime ? new Date(s.endTime) : undefined,
                timePerPage: new Map(Array.isArray(s.timePerPage) ? s.timePerPage : []),
              })));
            }
          }
        }
      }
    } catch (error) {
      console.warn('Failed to load navigation data from storage:', error);
    }
  }

  private saveToStorage(): void {
    if (typeof localStorage === 'undefined') return;

    try {
      const progressData = Object.fromEntries(
        Array.from(this.readingProgress.entries()).map(([docId, progress]) => [
          docId,
          {
            ...progress,
            timePerPage: Array.from(progress.timePerPage || []),
          },
        ])
      );
      localStorage.setItem('smart-navigation-progress', JSON.stringify(progressData));

      const sessionData = Object.fromEntries(
        Array.from(this.readingSessions.entries()).map(([docId, sessions]) => [
          docId,
          sessions.map(session => ({
            ...session,
            timePerPage: Array.from(session.timePerPage?.entries() || []),
          })),
        ])
      );
      localStorage.setItem('smart-navigation-sessions', JSON.stringify(sessionData));
    } catch (error) {
      console.warn('Failed to save navigation data to storage:', error);
    }
  }

  private setupEventListeners(): void {
    // Auto-save every 30 seconds
    setInterval(() => {
      this.saveToStorage();
    }, 30000);

    // Save on page unload
    window.addEventListener('beforeunload', () => {
      this.endCurrentSession();
      this.saveToStorage();
    });
  }

  public startReadingSession(documentId: string): void {
    this.endCurrentSession(); // End any existing session

    this.currentSession = {
      id: `session-${Date.now()}`,
      documentId,
      startTime: new Date(),
      pagesRead: [],
      timePerPage: new Map(),
      totalTime: 0,
      averageReadingSpeed: 0,
      focusScore: 1.0,
      comprehensionIndicators: {
        backtracking: 0,
        pauseTime: 0,
        annotationCount: 0,
        searchCount: 0,
      },
    };

    this.emit('session-started', { session: this.currentSession });
  }

  public endCurrentSession(): void {
    if (!this.currentSession) return;

    this.currentSession.endTime = new Date();
    this.currentSession.totalTime = 
      this.currentSession.endTime.getTime() - this.currentSession.startTime.getTime();

    // Calculate average reading speed
    const uniquePages = new Set(this.currentSession.pagesRead).size;
    const timeInMinutes = this.currentSession.totalTime / (1000 * 60);
    this.currentSession.averageReadingSpeed = uniquePages / timeInMinutes;

    // Store session
    const docId = this.currentSession.documentId;
    if (!this.readingSessions.has(docId)) {
      this.readingSessions.set(docId, []);
    }
    this.readingSessions.get(docId)!.push(this.currentSession);

    this.emit('session-ended', { session: this.currentSession });
    this.currentSession = null;
  }

  public trackPageVisit(documentId: string, pageNumber: number, totalPages: number): void {
    const now = Date.now();
    
    // Track time on previous page
    if (this.pageStartTime > 0 && this.currentSession) {
      const timeOnPage = now - this.pageStartTime;
      this.currentSession.timePerPage.set(pageNumber - 1, timeOnPage);
    }
    
    this.pageStartTime = now;

    // Update current session
    if (this.currentSession && this.currentSession.documentId === documentId) {
      this.currentSession.pagesRead.push(pageNumber);
    }

    // Update reading progress
    let progress = this.readingProgress.get(documentId);
    if (!progress) {
      progress = {
        documentId,
        currentPage: pageNumber,
        totalPages,
        percentage: 0,
        timeSpent: 0,
        lastReadDate: new Date(),
        readingSpeed: 0,
        estimatedTimeRemaining: 0,
        completedSections: [],
        bookmarks: [],
      };
      this.readingProgress.set(documentId, progress);
    }

    progress.currentPage = pageNumber;
    progress.totalPages = totalPages;
    progress.percentage = (pageNumber / totalPages) * 100;
    progress.lastReadDate = new Date();

    // Calculate reading speed and estimated time
    this.updateReadingMetrics(progress);

    // Auto-bookmark important sections
    if (this.preferences.autoBookmarkImportantSections) {
      this.checkForAutoBookmark(documentId, pageNumber);
    }

    this.emit('page-visited', { documentId, pageNumber, progress });
  }

  private updateReadingMetrics(progress: ReadingProgress): void {
    const sessions = this.readingSessions.get(progress.documentId) || [];
    
    if (sessions.length > 0) {
      const totalTime = sessions.reduce((sum, session) => sum + session.totalTime, 0);
      const totalPages = sessions.reduce((sum, session) => 
        sum + new Set(session.pagesRead).size, 0);
      
      progress.timeSpent = totalTime;
      progress.readingSpeed = totalPages / (totalTime / (1000 * 60)); // pages per minute
      
      const remainingPages = progress.totalPages - progress.currentPage;
      progress.estimatedTimeRemaining = remainingPages / progress.readingSpeed;
    }
  }

  private checkForAutoBookmark(documentId: string, pageNumber: number): void {
    // Simple heuristics for auto-bookmarking
    // In a real implementation, this would use content analysis
    
    const shouldBookmark = 
      pageNumber === 1 || // First page
      pageNumber % 10 === 0 || // Every 10th page
      this.isLikelyImportantSection(documentId, pageNumber);

    if (shouldBookmark) {
      this.createAutoBookmark(documentId, pageNumber);
    }
  }

  private isLikelyImportantSection(_documentId: string, _pageNumber: number): boolean {
    // Placeholder for content analysis
    // Would analyze text for headings, keywords, etc.
    return Math.random() < 0.1; // 10% chance for demo
  }

  private createAutoBookmark(documentId: string, pageNumber: number): void {
    const bookmark: SmartBookmark = {
      id: `auto-${documentId}-${pageNumber}-${Date.now()}`,
      documentId,
      pageNumber,
      title: `Auto Bookmark - Page ${pageNumber}`,
      type: 'auto',
      tags: ['auto-generated'],
      createdAt: new Date(),
      accessCount: 0,
      context: {
        surroundingText: '', // Would be extracted from page content
        position: { x: 0, y: 0 },
        viewport: { zoom: 1, rotation: 0 },
      },
      metadata: {
        importance: 0.5,
        relevance: 0.5,
        difficulty: 0.5,
        estimatedReadTime: 2,
      },
    };

    this.addBookmark(bookmark);
  }

  public addBookmark(bookmark: SmartBookmark): void {
    const progress = this.readingProgress.get(bookmark.documentId);
    if (progress) {
      progress.bookmarks.push(bookmark);
      this.emit('bookmark-added', { bookmark });
    }
  }

  public removeBookmark(documentId: string, bookmarkId: string): void {
    const progress = this.readingProgress.get(documentId);
    if (progress) {
      const index = progress.bookmarks.findIndex(b => b.id === bookmarkId);
      if (index !== -1) {
        const removed = progress.bookmarks.splice(index, 1)[0];
        this.emit('bookmark-removed', { bookmark: removed });
      }
    }
  }

  public getBookmarks(documentId: string, type?: SmartBookmark['type']): SmartBookmark[] {
    const progress = this.readingProgress.get(documentId);
    if (!progress) return [];

    let bookmarks = progress.bookmarks;
    if (type) {
      bookmarks = bookmarks.filter(b => b.type === type);
    }

    return bookmarks.sort((a, b) => b.metadata.importance - a.metadata.importance);
  }

  public getReadingProgress(documentId: string): ReadingProgress | null {
    return this.readingProgress.get(documentId) || null;
  }

  public getReadingSuggestions(documentId: string): Array<{
    type: 'bookmark' | 'section' | 'review';
    title: string;
    description: string;
    pageNumber: number;
    importance: number;
  }> {
    const progress = this.readingProgress.get(documentId);
    if (!progress) return [];

    const suggestions = [];

    // Suggest unvisited important bookmarks
    const importantBookmarks = progress.bookmarks
      .filter(b => b.metadata.importance > 0.7 && b.accessCount === 0)
      .slice(0, 3);

    suggestions.push(...importantBookmarks.map(bookmark => ({
      type: 'bookmark' as const,
      title: `Review: ${bookmark.title}`,
      description: bookmark.description || 'Important section you haven\'t visited',
      pageNumber: bookmark.pageNumber,
      importance: bookmark.metadata.importance,
    })));

    // Suggest review of difficult sections
    const sessions = this.readingSessions.get(documentId) || [];
    const slowPages = new Map<number, number>();
    
    sessions.forEach(session => {
      session.timePerPage.forEach((time, page) => {
        const avgTime = session.totalTime / session.pagesRead.length;
        if (time > avgTime * 1.5) { // 50% slower than average
          slowPages.set(page, (slowPages.get(page) || 0) + 1);
        }
      });
    });

    Array.from(slowPages.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 2)
      .forEach(([page, count]) => {
        suggestions.push({
          type: 'review',
          title: `Review Page ${page}`,
          description: `You spent extra time on this page (${count} sessions)`,
          pageNumber: page,
          importance: 0.6,
        });
      });

    return suggestions.sort((a, b) => b.importance - a.importance);
  }

  public getDocumentOutline(documentId: string): DocumentOutline[] {
    return this.documentOutlines.get(documentId) || [];
  }

  public setDocumentOutline(documentId: string, outline: DocumentOutline[]): void {
    this.documentOutlines.set(documentId, outline);
    this.emit('outline-updated', { documentId, outline });
  }

  public markSectionAsRead(documentId: string, sectionId: string): void {
    const progress = this.readingProgress.get(documentId);
    if (progress && !progress.completedSections.includes(sectionId)) {
      progress.completedSections.push(sectionId);
      this.emit('section-completed', { documentId, sectionId });
    }
  }

  public getReadingStats(documentId: string): {
    totalTimeSpent: number;
    averageReadingSpeed: number;
    pagesRead: number;
    completionPercentage: number;
    sessionCount: number;
    bookmarkCount: number;
    focusScore: number;
  } {
    const progress = this.readingProgress.get(documentId);
    const sessions = this.readingSessions.get(documentId) || [];

    if (!progress) {
      return {
        totalTimeSpent: 0,
        averageReadingSpeed: 0,
        pagesRead: 0,
        completionPercentage: 0,
        sessionCount: 0,
        bookmarkCount: 0,
        focusScore: 0,
      };
    }

    const uniquePagesRead = new Set();
    sessions.forEach(session => {
      session.pagesRead.forEach(page => uniquePagesRead.add(page));
    });

    const avgFocusScore = sessions.length > 0 
      ? sessions.reduce((sum, s) => sum + s.focusScore, 0) / sessions.length
      : 0;

    return {
      totalTimeSpent: progress.timeSpent,
      averageReadingSpeed: progress.readingSpeed,
      pagesRead: uniquePagesRead.size,
      completionPercentage: progress.percentage,
      sessionCount: sessions.length,
      bookmarkCount: progress.bookmarks.length,
      focusScore: avgFocusScore,
    };
  }

  public exportReadingData(documentId: string): unknown {
    return {
      progress: this.readingProgress.get(documentId),
      sessions: this.readingSessions.get(documentId),
      outline: this.documentOutlines.get(documentId),
      stats: this.getReadingStats(documentId),
      exportDate: new Date().toISOString(),
    };
  }

  public addEventListener(type: string, listener: (data: unknown) => void): void {
    if (!this.eventListeners.has(type)) {
      this.eventListeners.set(type, new Set());
    }
    this.eventListeners.get(type)!.add(listener);
  }

  public removeEventListener(type: string, listener: (data: unknown) => void): void {
    this.eventListeners.get(type)?.delete(listener);
  }

  private emit(type: string, data: unknown): void {
    this.eventListeners.get(type)?.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error('Navigation event listener error:', error);
      }
    });
  }

  public destroy(): void {
    this.endCurrentSession();
    this.saveToStorage();
    this.eventListeners.clear();
  }
}
