"use client";

/**
 * Service Worker Registration and Communication
 * Handles registration, updates, and messaging with the service worker
 */

// Check if service workers are supported
export const isServiceWorkerSupported = 
  typeof window !== 'undefined' && 'serviceWorker' in navigator;

// Service worker status
export type ServiceWorkerStatus = 
  | 'unsupported'
  | 'unregistered'
  | 'registering'
  | 'registered'
  | 'updated'
  | 'error';

// Service worker events
export type ServiceWorkerEvent = 
  | 'registration'
  | 'update'
  | 'error'
  | 'offline'
  | 'online';

// Service worker event listener
export type ServiceWorkerEventListener = (event: ServiceWorkerEvent, data?: unknown) => void;

// Cache statistics
export interface CacheStats {
  pdfEntries: number;
  thumbnailEntries: number;
  totalEntries: number;
  estimatedSize?: number;
}

// Service worker manager class
export class ServiceWorkerManager {
  private static instance: ServiceWorkerManager;
  private registration: ServiceWorkerRegistration | null = null;
  private status: ServiceWorkerStatus = 'unregistered';
  private eventListeners: ServiceWorkerEventListener[] = [];
  private offlineMode = false;

  // Private constructor for singleton pattern
  private constructor() {
    if (typeof window !== 'undefined') {
      // Listen for online/offline events
      window.addEventListener('online', () => {
        this.offlineMode = false;
        this.notifyListeners('online');
      });
      
      window.addEventListener('offline', () => {
        this.offlineMode = true;
        this.notifyListeners('offline');
      });
      
      // Check initial online status
      this.offlineMode = typeof navigator !== 'undefined' && !navigator.onLine;
    }
  }

  // Get singleton instance
  public static getInstance(): ServiceWorkerManager {
    if (!ServiceWorkerManager.instance) {
      ServiceWorkerManager.instance = new ServiceWorkerManager();
    }
    return ServiceWorkerManager.instance;
  }

  // Register service worker
  public async register(): Promise<boolean> {
    if (!isServiceWorkerSupported) {
      this.status = 'unsupported';
      this.notifyListeners('error', { message: 'Service workers not supported' });
      return false;
    }

    if (this.registration) {
      return true; // Already registered
    }

    try {
      this.status = 'registering';
      
      // Register the service worker
      this.registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
      });
      
      this.status = 'registered';
      this.notifyListeners('registration', { registration: this.registration });
      
      // Set up update listener
      this.registration.onupdatefound = () => {
        const installingWorker = this.registration?.installing;
        
        if (installingWorker) {
          installingWorker.onstatechange = () => {
            if (installingWorker.state === 'installed') {
              if (navigator.serviceWorker.controller) {
                // New service worker available
                this.status = 'updated';
                this.notifyListeners('update');
              }
            }
          };
        }
      };
      
      return true;
    } catch (error) {
      console.error('Service worker registration failed:', error);
      this.status = 'error';
      this.notifyListeners('error', { error });
      return false;
    }
  }

  // Unregister service worker
  public async unregister(): Promise<boolean> {
    if (!this.registration) {
      return true; // Already unregistered
    }

    try {
      const success = await this.registration.unregister();
      if (success) {
        this.registration = null;
        this.status = 'unregistered';
        return true;
      }
      return false;
    } catch (error) {
      console.error('Service worker unregistration failed:', error);
      return false;
    }
  }

  // Update service worker
  public async update(): Promise<boolean> {
    if (!this.registration) {
      return false;
    }

    try {
      await this.registration.update();
      return true;
    } catch (error) {
      console.error('Service worker update failed:', error);
      return false;
    }
  }

  // Get current status
  public getStatus(): ServiceWorkerStatus {
    return this.status;
  }

  // Check if offline
  public isOffline(): boolean {
    return this.offlineMode;
  }

  // Add event listener
  public addEventListener(listener: ServiceWorkerEventListener): void {
    this.eventListeners.push(listener);
  }

  // Remove event listener
  public removeEventListener(listener: ServiceWorkerEventListener): void {
    this.eventListeners = this.eventListeners.filter(l => l !== listener);
  }

  // Notify all listeners
  private notifyListeners(event: ServiceWorkerEvent, data?: unknown): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event, data);
      } catch (error) {
        console.error('Error in service worker event listener:', error);
      }
    });
  }

  // Send message to service worker
  public async sendMessage<T = unknown>(
    type: string,
    data: unknown = {}
  ): Promise<T> {
    if (!this.registration || !this.registration.active) {
      throw new Error('Service worker not active');
    }

    return new Promise((resolve, reject) => {
      const messageChannel = new MessageChannel();
      
      messageChannel.port1.onmessage = (event) => {
        if (event.data.error) {
          reject(event.data.error);
        } else {
          resolve(event.data);
        }
      };

      this.registration.active.postMessage(
        { type, data },
        [messageChannel.port2]
      );
    });
  }

  // Cache a PDF document
  public async cachePDF(url: string, documentId: string): Promise<boolean> {
    try {
      const result = await this.sendMessage<{ success: boolean }>('CACHE_PDF', { url, documentId });
      return result.success;
    } catch (error) {
      console.error('Failed to cache PDF:', error);
      return false;
    }
  }

  // Cache a thumbnail
  public async cacheThumbnail(
    imageData: string,
    documentId: string,
    pageNumber: number
  ): Promise<boolean> {
    try {
      const result = await this.sendMessage<{ success: boolean }>(
        'CACHE_THUMBNAIL',
        { imageData, documentId, pageNumber }
      );
      return result.success;
    } catch (error) {
      console.error('Failed to cache thumbnail:', error);
      return false;
    }
  }

  // Clear cache
  public async clearCache(type: 'all' | 'pdf' | 'thumbnails' = 'all'): Promise<boolean> {
    try {
      const result = await this.sendMessage<{ success: boolean }>('CLEAR_CACHE', { type });
      return result.success;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return false;
    }
  }

  // Get cache statistics
  public async getCacheStats(): Promise<CacheStats> {
    try {
      const result = await this.sendMessage<{ success: boolean; stats: any }>('GET_CACHE_STATS');
      
      if (result.success && result.stats) {
        return {
          pdfEntries: result.stats['cobalt-pdf-documents-v1']?.entryCount || 0,
          thumbnailEntries: result.stats['cobalt-thumbnails-v1']?.entryCount || 0,
          totalEntries: 
            (result.stats['cobalt-pdf-documents-v1']?.entryCount || 0) +
            (result.stats['cobalt-thumbnails-v1']?.entryCount || 0) +
            (result.stats['cobalt-static-cache-v1']?.entryCount || 0),
          estimatedSize: undefined, // Can't easily get size
        };
      }
      
      return {
        pdfEntries: 0,
        thumbnailEntries: 0,
        totalEntries: 0,
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return {
        pdfEntries: 0,
        thumbnailEntries: 0,
        totalEntries: 0,
      };
    }
  }
}

// Get service worker manager instance
export const getServiceWorkerManager = (): ServiceWorkerManager => {
  return ServiceWorkerManager.getInstance();
};

// Initialize service worker
export const initServiceWorker = async (): Promise<boolean> => {
  const manager = getServiceWorkerManager();
  return await manager.register();
};

// React hook for service worker status
export const useServiceWorker = () => {
  if (typeof window === 'undefined') {
    return {
      isSupported: false,
      status: 'unsupported' as ServiceWorkerStatus,
      isOffline: false,
      register: async () => false,
      update: async () => false,
      unregister: async () => false,
      cachePDF: async () => false,
      cacheThumbnail: async () => false,
      clearCache: async () => false,
      getCacheStats: async () => ({ pdfEntries: 0, thumbnailEntries: 0, totalEntries: 0 }),
    };
  }

  const manager = getServiceWorkerManager();
  
  return {
    isSupported: isServiceWorkerSupported,
    status: manager.getStatus(),
    isOffline: manager.isOffline(),
    register: () => manager.register(),
    update: () => manager.update(),
    unregister: () => manager.unregister(),
    cachePDF: (url: string, documentId: string) => manager.cachePDF(url, documentId),
    cacheThumbnail: (imageData: string, documentId: string, pageNumber: number) => 
      manager.cacheThumbnail(imageData, documentId, pageNumber),
    clearCache: (type?: 'all' | 'pdf' | 'thumbnails') => manager.clearCache(type),
    getCacheStats: () => manager.getCacheStats(),
  };
};
