import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// Mock components that use keyboard shortcuts
const MockPDFViewer = ({ onKeyDown }: { onKeyDown?: (e: KeyboardEvent) => void }) => {
  return (
    <div
      data-testid="pdf-viewer"
      tabIndex={0}
      onKeyDown={(e) => onKeyDown?.(e.nativeEvent as KeyboardEvent)}
    >
      PDF Viewer Content
    </div>
  )
}

const MockPDFFloatingToolbar = ({ 
  onPrevPage, 
  onNextPage, 
  onZoomIn, 
  onZoomOut,
  onToggleFullscreen 
}: {
  onPrevPage?: () => void;
  onNextPage?: () => void;
  onZoomIn?: () => void;
  onZoomOut?: () => void;
  onToggleFullscreen?: () => void;
}) => {
  return (
    <div data-testid="floating-toolbar">
      <button onClick={onPrevPage} data-testid="prev-page">Previous</button>
      <button onClick={onNextPage} data-testid="next-page">Next</button>
      <button onClick={onZoomIn} data-testid="zoom-in">Zoom In</button>
      <button onClick={onZoomOut} data-testid="zoom-out">Zoom Out</button>
      <button onClick={onToggleFullscreen} data-testid="fullscreen">Fullscreen</button>
    </div>
  )
}

describe('Keyboard Shortcuts', () => {
  let mockCallbacks: {
    onPrevPage: ReturnType<typeof vi.fn>;
    onNextPage: ReturnType<typeof vi.fn>;
    onZoomIn: ReturnType<typeof vi.fn>;
    onZoomOut: ReturnType<typeof vi.fn>;
    onToggleFullscreen: ReturnType<typeof vi.fn>;
    onSearch: ReturnType<typeof vi.fn>;
    onBookmark: ReturnType<typeof vi.fn>;
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockCallbacks = {
      onPrevPage: vi.fn(),
      onNextPage: vi.fn(),
      onZoomIn: vi.fn(),
      onZoomOut: vi.fn(),
      onToggleFullscreen: vi.fn(),
      onSearch: vi.fn(),
      onBookmark: vi.fn(),
    }
  })

  describe('Navigation Shortcuts', () => {
    it('handles arrow key navigation', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'ArrowLeft') mockCallbacks.onPrevPage()
        if (e.key === 'ArrowRight') mockCallbacks.onNextPage()
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{ArrowLeft}')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1)

      await user.keyboard('{ArrowRight}')
      expect(mockCallbacks.onNextPage).toHaveBeenCalledTimes(1)
    })

    it('handles page up/down navigation', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'PageUp') mockCallbacks.onPrevPage()
        if (e.key === 'PageDown') mockCallbacks.onNextPage()
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{PageUp}')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1)

      await user.keyboard('{PageDown}')
      expect(mockCallbacks.onNextPage).toHaveBeenCalledTimes(1)
    })

    it('handles home/end navigation', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'Home') mockCallbacks.onPrevPage() // Go to first page
        if (e.key === 'End') mockCallbacks.onNextPage() // Go to last page
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Home}')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1)

      await user.keyboard('{End}')
      expect(mockCallbacks.onNextPage).toHaveBeenCalledTimes(1)
    })
  })

  describe('Zoom Shortcuts', () => {
    it('handles zoom in/out with plus/minus keys', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === '+' || e.key === '=') mockCallbacks.onZoomIn()
        if (e.key === '-') mockCallbacks.onZoomOut()
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{+}')
      expect(mockCallbacks.onZoomIn).toHaveBeenCalledTimes(1)

      await user.keyboard('{-}')
      expect(mockCallbacks.onZoomOut).toHaveBeenCalledTimes(1)

      await user.keyboard('{=}')
      expect(mockCallbacks.onZoomIn).toHaveBeenCalledTimes(2)
    })

    it('handles Ctrl+zoom shortcuts', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.ctrlKey && (e.key === '+' || e.key === '=')) {
          e.preventDefault()
          mockCallbacks.onZoomIn()
        }
        if (e.ctrlKey && e.key === '-') {
          e.preventDefault()
          mockCallbacks.onZoomOut()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Control>}{+}{/Control}')
      expect(mockCallbacks.onZoomIn).toHaveBeenCalledTimes(1)

      await user.keyboard('{Control>}{-}{/Control}')
      expect(mockCallbacks.onZoomOut).toHaveBeenCalledTimes(1)
    })
  })

  describe('Function Key Shortcuts', () => {
    it('handles F11 for fullscreen toggle', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'F11') {
          e.preventDefault()
          mockCallbacks.onToggleFullscreen()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{F11}')
      expect(mockCallbacks.onToggleFullscreen).toHaveBeenCalledTimes(1)
    })

    it('handles Escape key for closing modals/fullscreen', async () => {
      const user = userEvent.setup()
      const onEscape = vi.fn()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          onEscape()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Escape}')
      expect(onEscape).toHaveBeenCalledTimes(1)
    })
  })

  describe('Search Shortcuts', () => {
    it('handles Ctrl+F for search', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.ctrlKey && e.key === 'f') {
          e.preventDefault()
          mockCallbacks.onSearch()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Control>}f{/Control}')
      expect(mockCallbacks.onSearch).toHaveBeenCalledTimes(1)
    })

    it('handles F3 for find next', async () => {
      const user = userEvent.setup()
      const onFindNext = vi.fn()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.key === 'F3') {
          onFindNext()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{F3}')
      expect(onFindNext).toHaveBeenCalledTimes(1)
    })

    it('handles Shift+F3 for find previous', async () => {
      const user = userEvent.setup()
      const onFindPrev = vi.fn()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.shiftKey && e.key === 'F3') {
          onFindPrev()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Shift>}{F3}{/Shift}')
      expect(onFindPrev).toHaveBeenCalledTimes(1)
    })
  })

  describe('Bookmark Shortcuts', () => {
    it('handles Ctrl+D for bookmark', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.ctrlKey && e.key === 'd') {
          e.preventDefault()
          mockCallbacks.onBookmark()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Control>}d{/Control}')
      expect(mockCallbacks.onBookmark).toHaveBeenCalledTimes(1)
    })
  })

  describe('Toolbar Button Shortcuts', () => {
    it('provides keyboard access to toolbar buttons', async () => {
      const user = userEvent.setup()

      render(
        <MockPDFFloatingToolbar
          onPrevPage={mockCallbacks.onPrevPage}
          onNextPage={mockCallbacks.onNextPage}
          onZoomIn={mockCallbacks.onZoomIn}
          onZoomOut={mockCallbacks.onZoomOut}
          onToggleFullscreen={mockCallbacks.onToggleFullscreen}
        />
      )

      // Tab through buttons and activate with Enter/Space
      await user.tab()
      await user.keyboard('{Enter}')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1)

      await user.tab()
      await user.keyboard(' ')
      expect(mockCallbacks.onNextPage).toHaveBeenCalledTimes(1)

      await user.tab()
      await user.keyboard('{Enter}')
      expect(mockCallbacks.onZoomIn).toHaveBeenCalledTimes(1)

      await user.tab()
      await user.keyboard(' ')
      expect(mockCallbacks.onZoomOut).toHaveBeenCalledTimes(1)

      await user.tab()
      await user.keyboard('{Enter}')
      expect(mockCallbacks.onToggleFullscreen).toHaveBeenCalledTimes(1)
    })
  })

  describe('Accessibility Features', () => {
    it('maintains focus management during keyboard navigation', async () => {
      const user = userEvent.setup()

      render(
        <div>
          <MockPDFViewer />
          <MockPDFFloatingToolbar
            onPrevPage={mockCallbacks.onPrevPage}
            onNextPage={mockCallbacks.onNextPage}
            onZoomIn={mockCallbacks.onZoomIn}
            onZoomOut={mockCallbacks.onZoomOut}
            onToggleFullscreen={mockCallbacks.onToggleFullscreen}
          />
        </div>
      )

      const viewer = screen.getByTestId('pdf-viewer')
      
      // Focus should be manageable
      viewer.focus()
      expect(document.activeElement).toBe(viewer)

      // Tab should move to toolbar
      await user.tab()
      expect(document.activeElement).toBe(screen.getByTestId('prev-page'))
    })

    it('provides proper ARIA labels for keyboard shortcuts', () => {
      render(
        <MockPDFFloatingToolbar
          onPrevPage={mockCallbacks.onPrevPage}
          onNextPage={mockCallbacks.onNextPage}
          onZoomIn={mockCallbacks.onZoomIn}
          onZoomOut={mockCallbacks.onZoomOut}
          onToggleFullscreen={mockCallbacks.onToggleFullscreen}
        />
      )

      // Buttons should be accessible
      expect(screen.getByTestId('prev-page')).toBeInTheDocument()
      expect(screen.getByTestId('next-page')).toBeInTheDocument()
      expect(screen.getByTestId('zoom-in')).toBeInTheDocument()
      expect(screen.getByTestId('zoom-out')).toBeInTheDocument()
      expect(screen.getByTestId('fullscreen')).toBeInTheDocument()
    })
  })

  describe('Keyboard Event Handling', () => {
    it('prevents default behavior for handled shortcuts', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        if (e.ctrlKey && e.key === 'f') {
          e.preventDefault()
          mockCallbacks.onSearch()
        }
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      await user.keyboard('{Control>}f{/Control}')
      expect(mockCallbacks.onSearch).toHaveBeenCalledTimes(1)
    })

    it('allows default behavior for unhandled keys', async () => {
      const user = userEvent.setup()
      const onKeyDown = vi.fn((e: KeyboardEvent) => {
        // Only handle specific keys, let others pass through
        if (e.key === 'ArrowLeft') {
          mockCallbacks.onPrevPage()
        }
        // Don't prevent default for other keys
      })

      render(<MockPDFViewer onKeyDown={onKeyDown} />)
      
      const viewer = screen.getByTestId('pdf-viewer')
      viewer.focus()

      // Handled key
      await user.keyboard('{ArrowLeft}')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1)

      // Unhandled key should not cause errors
      await user.keyboard('a')
      expect(mockCallbacks.onPrevPage).toHaveBeenCalledTimes(1) // No additional calls
    })
  })
})
