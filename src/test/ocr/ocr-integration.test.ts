import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useOCR } from '@/hooks/use-ocr';
import { TesseractEngine } from '@/lib/ocr/tesseract-engine';
import { OCRManager } from '@/lib/ocr/ocr-manager';

// Mock Tesseract.js
vi.mock('tesseract.js', () => {
  const mockWorker = {
    setParameters: vi.fn().mockResolvedValue(undefined),
    terminate: vi.fn().mockResolvedValue(undefined),
  };

  const mockScheduler = {
    addWorker: vi.fn(),
    addJob: vi.fn().mockImplementation(() => {
      // Mock OCR result
      return Promise.resolve({
        data: {
          text: 'Mock OCR text result',
          confidence: 85,
          words: [
            {
              text: 'Mock',
              confidence: 90,
              bbox: { x0: 10, y0: 10, x1: 50, y1: 30 },
              baseline: { x0: 10, y0: 25, x1: 50, y1: 25 },
            },
            {
              text: 'OCR',
              confidence: 85,
              bbox: { x0: 55, y0: 10, x1: 85, y1: 30 },
              baseline: { x0: 55, y0: 25, x1: 85, y1: 25 },
            },
            {
              text: 'text',
              confidence: 80,
              bbox: { x0: 90, y0: 10, x1: 125, y1: 30 },
              baseline: { x0: 90, y0: 25, x1: 125, y1: 25 },
            },
          ],
          lines: [
            {
              text: 'Mock OCR text',
              confidence: 85,
              bbox: { x0: 10, y0: 10, x1: 125, y1: 30 },
              words: [],
            },
          ],
          paragraphs: [
            {
              text: 'Mock OCR text result',
              confidence: 85,
              bbox: { x0: 10, y0: 10, x1: 125, y1: 30 },
              lines: [],
            },
          ],
          imageWidth: 800,
          imageHeight: 600,
        },
      });
    }),
    terminate: vi.fn().mockResolvedValue(undefined),
  };

  return {
    default: {
      createWorker: vi.fn().mockImplementation(() => Promise.resolve(mockWorker)),
      createScheduler: vi.fn().mockImplementation(() => mockScheduler),
      OEM: {
        LSTM_ONLY: 1,
      },
      PSM: {
        AUTO: 3,
      },
    },
    createWorker: vi.fn().mockImplementation(() => Promise.resolve(mockWorker)),
    createScheduler: vi.fn().mockImplementation(() => mockScheduler),
    OEM: {
      LSTM_ONLY: 1,
    },
    PSM: {
      AUTO: 3,
    },
  };
});

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock PDF document
const createMockPDFDocument = () => ({
  numPages: 3,
  getPage: vi.fn().mockImplementation((pageNumber) => 
    Promise.resolve({
      pageNumber,
      getViewport: vi.fn().mockReturnValue({
        width: 800,
        height: 1000,
        scale: 2.0,
      }),
      render: vi.fn().mockReturnValue({
        promise: Promise.resolve(),
      }),
    })
  ),
});

// Mock canvas
const mockCanvas = {
  width: 800,
  height: 1000,
  getContext: vi.fn().mockReturnValue({
    drawImage: vi.fn(),
    getImageData: vi.fn().mockReturnValue({
      data: new Uint8ClampedArray(800 * 1000 * 4),
      width: 800,
      height: 1000,
    }),
    putImageData: vi.fn(),
  }),
  toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock-image-data'),
};

Object.defineProperty(document, 'createElement', {
  value: vi.fn().mockImplementation((tagName) => {
    if (tagName === 'canvas') {
      return mockCanvas;
    }
    return {};
  }),
});

describe('TesseractEngine', () => {
  let engine: TesseractEngine;

  beforeEach(() => {
    vi.clearAllMocks();
    engine = new TesseractEngine(1, { language: 'eng' });
  });

  afterEach(async () => {
    if (engine) {
      await engine.terminate();
    }
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const progressCallback = vi.fn();
      
      await engine.initialize(progressCallback);
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
          message: 'OCR engine initialized successfully',
        })
      );
      
      const status = engine.getStatus();
      expect(status.isInitialized).toBe(true);
      expect(status.workerCount).toBe(1);
    });

    it('should not reinitialize if already initialized', async () => {
      await engine.initialize();
      const status1 = engine.getStatus();
      
      await engine.initialize();
      const status2 = engine.getStatus();
      
      expect(status1).toEqual(status2);
    });
  });

  describe('Image Processing', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    it('should process image and return OCR result', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      const progressCallback = vi.fn();
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
        onProgress: progressCallback,
      });
      
      expect(result).toMatchObject({
        pageNumber: 1,
        text: 'Mock OCR text result',
        confidence: 85,
        language: 'eng',
      });
      
      expect(result.words).toHaveLength(3);
      expect(result.lines).toHaveLength(1);
      expect(result.paragraphs).toHaveLength(1);
      expect(result.processingTime).toBeGreaterThan(0);
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should process batch of images', async () => {
      const images = [
        { data: 'data:image/png;base64,mock1', pageNumber: 1 },
        { data: 'data:image/png;base64,mock2', pageNumber: 2 },
      ];
      
      const progressCallback = vi.fn();
      const pageCompleteCallback = vi.fn();
      
      const results = await engine.processBatch(images, {
        onProgress: progressCallback,
        onPageComplete: pageCompleteCallback,
      });
      
      expect(results).toHaveLength(2);
      expect(results[0].pageNumber).toBe(1);
      expect(results[1].pageNumber).toBe(2);
      
      expect(pageCompleteCallback).toHaveBeenCalledTimes(2);
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should handle processing errors gracefully', async () => {
      // Mock error in scheduler
      const mockScheduler = {
        addJob: vi.fn().mockRejectedValue(new Error('OCR processing failed')),
      };
      
      // Replace the scheduler
      (engine as unknown as { scheduler: typeof mockScheduler }).scheduler = mockScheduler;
      
      const progressCallback = vi.fn();
      
      await expect(
        engine.processImage('invalid-image-data', {
          pageNumber: 1,
          onProgress: progressCallback,
        })
      ).rejects.toThrow('OCR processing failed');
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'error',
          message: expect.stringContaining('Failed to process page 1'),
        })
      );
    });
  });

  describe('Image Preprocessing', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    it('should apply preprocessing options', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
        preprocessingOptions: {
          enhanceContrast: true,
          adjustBrightness: 10,
          adjustContrast: 20,
          scaleImage: 2.0,
        },
      });
      
      expect(result).toBeDefined();
      expect(result.pageNumber).toBe(1);
    });
  });
});

describe('OCRManager', () => {
  let manager: OCRManager;
  let mockPDFDocument: object;

  beforeEach(() => {
    vi.clearAllMocks();
    manager = OCRManager.getInstance({
      maxWorkers: 1,
      enableCaching: false, // Disable caching for tests
    });
    mockPDFDocument = createMockPDFDocument();
  });

  afterEach(async () => {
    await manager.terminate();
  });

  describe('Initialization', () => {
    it('should initialize OCR manager', async () => {
      const progressCallback = vi.fn();
      
      await manager.initialize(progressCallback);
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should return singleton instance', () => {
      const manager1 = OCRManager.getInstance();
      const manager2 = OCRManager.getInstance();
      
      expect(manager1).toBe(manager2);
    });
  });

  describe('Document Processing', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it('should process document pages', async () => {
      const progressCallback = vi.fn();
      const pageCompleteCallback = vi.fn();
      
      const result = await manager.processDocument(
        mockPDFDocument,
        {
          documentId: 'test-doc',
          pages: [1, 2],
          language: 'eng',
        },
        progressCallback,
        pageCompleteCallback
      );
      
      expect(result).toBeDefined();
      expect(result.documentId).toBe('test-doc');
      expect(result.totalPages).toBe(2);
      expect(result.metadata.status).toBe('completed');
      
      expect(pageCompleteCallback).toHaveBeenCalledTimes(2);
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should process single page', async () => {
      const progressCallback = vi.fn();
      
      const result = await manager.processPage(
        mockPDFDocument,
        1,
        {
          documentId: 'test-doc',
          language: 'eng',
          onProgress: progressCallback,
        }
      );
      
      expect(result).toBeDefined();
      expect(result.pageNumber).toBe(1);
      expect(result.text).toBe('Mock OCR text result');
      expect(result.confidence).toBe(85);
    });

    it('should handle processing errors', async () => {
      // Mock PDF document that throws error
      const errorPDFDocument = {
        ...mockPDFDocument,
        getPage: vi.fn().mockRejectedValue(new Error('Failed to get page')),
      };
      
      await expect(
        manager.processPage(errorPDFDocument, 1, {
          documentId: 'test-doc',
        })
      ).rejects.toThrow();
    });
  });

  describe('Job Management', () => {
    beforeEach(async () => {
      await manager.initialize();
    });

    it('should queue OCR job', async () => {
      const jobId = await manager.queueOCRJob({
        documentId: 'test-doc',
        pages: [1, 2, 3],
        language: 'eng',
        priority: 'high',
      });
      
      expect(jobId).toBeDefined();
      expect(typeof jobId).toBe('string');
      
      const stats = manager.getStats();
      expect(stats.queuedJobs).toBeGreaterThan(0);
    });

    it('should cancel OCR job', async () => {
      await manager.queueOCRJob({
        documentId: 'test-doc',
        pages: [1],
        language: 'eng',
      });
      
      const cancelled = manager.cancelJob('test-doc');
      expect(cancelled).toBe(true);
    });

    it('should get job status', async () => {
      const status = manager.getJobStatus('non-existent-doc');
      expect(status).toBeNull();
    });
  });

  describe('Statistics', () => {
    it('should return OCR statistics', () => {
      const stats = manager.getStats();
      
      expect(stats).toMatchObject({
        activeJobs: expect.any(Number),
        queuedJobs: expect.any(Number),
        isInitialized: expect.any(Boolean),
        engineStatus: expect.any(Object),
      });
    });
  });
});

describe('useOCR Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  describe('Initialization', () => {
    it('should initialize OCR hook', async () => {
      const { result } = renderHook(() => useOCR({
        autoInitialize: true,
      }));
      
      // Wait for initialization
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });
      
      expect(result.current.isReady).toBe(true);
      expect(result.current.isInitialized).toBe(true);
      expect(result.current.isInitializing).toBe(false);
    });

    it('should not auto-initialize when disabled', () => {
      const { result } = renderHook(() => useOCR({
        autoInitialize: false,
      }));
      
      expect(result.current.isInitialized).toBe(false);
      expect(result.current.isInitializing).toBe(false);
    });
  });

  describe('Document Processing', () => {
    it('should process document with OCR', async () => {
      const { result } = renderHook(() => useOCR());
      
      // Wait for initialization
      await act(async () => {
        await result.current.initialize();
      });
      
      const mockPDFDocument = createMockPDFDocument();
      
      await act(async () => {
        const ocrResult = await result.current.processDocument(mockPDFDocument, {
          documentId: 'test-doc',
          pages: [1],
        });
        
        expect(ocrResult).toBeDefined();
        expect(ocrResult?.documentId).toBe('test-doc');
      });
      
      expect(result.current.results.size).toBe(1);
    });

    it('should process single page', async () => {
      const { result } = renderHook(() => useOCR());
      
      await act(async () => {
        await result.current.initialize();
      });
      
      const mockPDFDocument = createMockPDFDocument();
      
      await act(async () => {
        const pageResult = await result.current.processPage(mockPDFDocument, 1, {
          documentId: 'test-doc',
        });
        
        expect(pageResult).toBeDefined();
        expect(pageResult?.pageNumber).toBe(1);
        expect(pageResult?.text).toBe('Mock OCR text result');
      });
    });
  });

  describe('Search Functionality', () => {
    it('should search OCR text', async () => {
      const { result } = renderHook(() => useOCR());
      
      await act(async () => {
        await result.current.initialize();
      });
      
      const mockPDFDocument = createMockPDFDocument();
      
      // Process a document first
      await act(async () => {
        await result.current.processDocument(mockPDFDocument, {
          documentId: 'test-doc',
          pages: [1],
        });
      });
      
      // Search for text
      const searchResults = result.current.searchOCRText('test-doc', 'Mock', {
        caseSensitive: false,
      });
      
      expect(searchResults).toHaveLength(1);
      expect(searchResults[0].pageNumber).toBe(1);
      expect(searchResults[0].matches).toHaveLength(1);
      expect(searchResults[0].matches[0].text).toBe('Mock');
    });
  });

  describe('Cache Management', () => {
    it('should clear cache', async () => {
      const { result } = renderHook(() => useOCR());
      
      await act(async () => {
        await result.current.initialize();
      });
      
      await act(async () => {
        await result.current.clearCache('test-doc');
      });
      
      expect(result.current.hasError).toBe(false);
    });
  });

  describe('Error Handling', () => {
    it('should handle initialization errors', async () => {
      // Mock initialization failure
      const { result } = renderHook(() => useOCR({
        autoInitialize: false,
      }));
      
      // Mock OCRManager to throw error
      vi.spyOn(OCRManager, 'getInstance').mockImplementation(() => {
        throw new Error('Initialization failed');
      });
      
      await act(async () => {
        await result.current.initialize();
      });
      
      expect(result.current.hasError).toBe(true);
      expect(result.current.error).toContain('Initialization failed');
    });

    it('should clear errors', async () => {
      const { result } = renderHook(() => useOCR());
      
      // Simulate error state
      await act(async () => {
        (result.current as { setState: (fn: (prev: object) => object) => void }).setState((prev: object) => ({
          ...prev,
          error: 'Test error',
        }));
      });
      
      expect(result.current.hasError).toBe(true);
      
      act(() => {
        result.current.clearError();
      });
      
      expect(result.current.hasError).toBe(false);
    });
  });
});
