import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock browser APIs
Object.defineProperty(global, 'indexedDB', {
  value: {
    open: vi.fn(),
    deleteDatabase: vi.fn(),
  },
});

Object.defineProperty(global, 'localStorage', {
  value: {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  },
});

Object.defineProperty(global, 'navigator', {
  value: {
    clipboard: {
      writeText: vi.fn().mockResolvedValue(undefined),
    },
  },
});

// Mock Tesseract.js
vi.mock('tesseract.js', () => {
  const mockWorker = {
    setParameters: vi.fn().mockResolvedValue(undefined),
    terminate: vi.fn().mockResolvedValue(undefined),
  };

  const mockScheduler = {
    addWorker: vi.fn(),
    addJob: vi.fn().mockImplementation(() => {
      return Promise.resolve({
        data: {
          text: 'Mock OCR text result',
          confidence: 85,
          words: [
            {
              text: 'Mock',
              confidence: 90,
              bbox: { x0: 10, y0: 10, x1: 50, y1: 30 },
              baseline: { x0: 10, y0: 25, x1: 50, y1: 25 },
            },
          ],
          lines: [
            {
              text: 'Mock OCR text',
              confidence: 85,
              bbox: { x0: 10, y0: 10, x1: 125, y1: 30 },
              words: [],
            },
          ],
          paragraphs: [
            {
              text: 'Mock OCR text result',
              confidence: 85,
              bbox: { x0: 10, y0: 10, x1: 125, y1: 30 },
              lines: [],
            },
          ],
          imageWidth: 800,
          imageHeight: 600,
        },
      });
    }),
    terminate: vi.fn().mockResolvedValue(undefined),
  };

  return {
    default: {
      createWorker: vi.fn().mockImplementation(() => Promise.resolve(mockWorker)),
      createScheduler: vi.fn().mockImplementation(() => mockScheduler),
      OEM: { LSTM_ONLY: 1 },
      PSM: { AUTO: 3 },
    },
    createWorker: vi.fn().mockImplementation(() => Promise.resolve(mockWorker)),
    createScheduler: vi.fn().mockImplementation(() => mockScheduler),
    OEM: { LSTM_ONLY: 1 },
    PSM: { AUTO: 3 },
  };
});

// Mock canvas
Object.defineProperty(document, 'createElement', {
  value: vi.fn().mockImplementation((tagName) => {
    if (tagName === 'canvas') {
      return {
        width: 800,
        height: 1000,
        getContext: vi.fn().mockReturnValue({
          drawImage: vi.fn(),
          getImageData: vi.fn().mockReturnValue({
            data: new Uint8ClampedArray(800 * 1000 * 4),
            width: 800,
            height: 1000,
          }),
          putImageData: vi.fn(),
          translate: vi.fn(),
          rotate: vi.fn(),
        }),
        toDataURL: vi.fn().mockReturnValue('data:image/png;base64,mock-image-data'),
      };
    }
    return {};
  }),
});

// Mock Image
Object.defineProperty(global, 'Image', {
  value: class MockImage {
    onload: (() => void) | null = null;
    onerror: (() => void) | null = null;
    src: string = '';
    width: number = 800;
    height: number = 600;

    constructor() {
      setTimeout(() => {
        if (this.onload) this.onload();
      }, 0);
    }
  },
});

import { TesseractEngine } from '@/lib/ocr/tesseract-engine';
import type { OCRConfig, ImagePreprocessingOptions } from '@/lib/ocr/tesseract-engine';

describe('TesseractEngine Core Functionality', () => {
  let engine: TesseractEngine;

  beforeEach(() => {
    vi.clearAllMocks();
    engine = new TesseractEngine(1, { language: 'eng' });
  });

  afterEach(async () => {
    if (engine) {
      await engine.terminate();
    }
  });

  describe('Engine Configuration', () => {
    it('should create engine with default configuration', () => {
      const defaultEngine = new TesseractEngine();
      const status = defaultEngine.getStatus();
      
      expect(status.isInitialized).toBe(false);
      expect(status.workerCount).toBe(0);
      expect(status.config.language).toBe('eng');
    });

    it('should create engine with custom configuration', () => {
      const customConfig: Partial<OCRConfig> = {
        language: 'spa',
        preserveInterwordSpaces: false,
      };
      
      const customEngine = new TesseractEngine(2, customConfig);
      const status = customEngine.getStatus();
      
      expect(status.config.language).toBe('spa');
      expect(status.config.preserveInterwordSpaces).toBe(false);
    });
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const progressCallback = vi.fn();
      
      await engine.initialize(progressCallback);
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
          message: 'OCR engine initialized successfully',
        })
      );
      
      const status = engine.getStatus();
      expect(status.isInitialized).toBe(true);
      expect(status.workerCount).toBe(1);
    });

    it('should not reinitialize if already initialized', async () => {
      await engine.initialize();
      const status1 = engine.getStatus();
      
      await engine.initialize();
      const status2 = engine.getStatus();
      
      expect(status1).toEqual(status2);
    });

    it('should track initialization progress', async () => {
      const progressCallback = vi.fn();
      
      await engine.initialize(progressCallback);
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'initializing',
          progress: 0,
          message: 'Initializing OCR engine...',
        })
      );
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });
  });

  describe('Image Processing', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    it('should process image and return OCR result', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      const progressCallback = vi.fn();
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
        onProgress: progressCallback,
      });
      
      expect(result).toMatchObject({
        pageNumber: 1,
        text: 'Mock OCR text result',
        confidence: 85,
        language: 'eng',
      });
      
      expect(result.words).toHaveLength(1);
      expect(result.lines).toHaveLength(1);
      expect(result.paragraphs).toHaveLength(1);
      expect(result.processingTime).toBeGreaterThan(0);
      expect(result.id).toBeDefined();
      
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should process batch of images', async () => {
      const images = [
        { data: 'data:image/png;base64,mock1', pageNumber: 1 },
        { data: 'data:image/png;base64,mock2', pageNumber: 2 },
      ];
      
      const progressCallback = vi.fn();
      const pageCompleteCallback = vi.fn();
      
      const results = await engine.processBatch(images, {
        onProgress: progressCallback,
        onPageComplete: pageCompleteCallback,
      });
      
      expect(results).toHaveLength(2);
      expect(results[0].pageNumber).toBe(1);
      expect(results[1].pageNumber).toBe(2);
      
      expect(pageCompleteCallback).toHaveBeenCalledTimes(2);
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'complete',
          progress: 100,
        })
      );
    });

    it('should handle empty batch', async () => {
      const results = await engine.processBatch([]);
      expect(results).toHaveLength(0);
    });

    it('should apply preprocessing options', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      const preprocessingOptions: ImagePreprocessingOptions = {
        enhanceContrast: true,
        adjustBrightness: 10,
        adjustContrast: 20,
        scaleImage: 2.0,
      };
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
        preprocessingOptions,
      });
      
      expect(result).toBeDefined();
      expect(result.pageNumber).toBe(1);
      expect(result.text).toBe('Mock OCR text result');
    });
  });

  describe('Data Extraction', () => {
    beforeEach(async () => {
      await engine.initialize();
    });

    it('should extract structured data from OCR result', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
      });
      
      // Check words structure
      expect(result.words[0]).toMatchObject({
        text: 'Mock',
        confidence: 90,
        bbox: {
          x0: 10,
          y0: 10,
          x1: 50,
          y1: 30,
        },
        baseline: {
          x0: 10,
          y0: 25,
          x1: 50,
          y1: 25,
        },
      });
      
      // Check lines structure
      expect(result.lines[0]).toMatchObject({
        text: 'Mock OCR text',
        confidence: 85,
        bbox: {
          x0: 10,
          y0: 10,
          x1: 125,
          y1: 30,
        },
      });
      
      // Check paragraphs structure
      expect(result.paragraphs[0]).toMatchObject({
        text: 'Mock OCR text result',
        confidence: 85,
        bbox: {
          x0: 10,
          y0: 10,
          x1: 125,
          y1: 30,
        },
      });
    });

    it('should include image metadata', async () => {
      const mockImageData = 'data:image/png;base64,mock-image-data';
      
      const result = await engine.processImage(mockImageData, {
        pageNumber: 1,
      });
      
      expect(result.imageInfo).toMatchObject({
        width: 800,
        height: 600,
        channels: 3,
      });
      
      expect(result.metadata).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should throw error when not initialized', async () => {
      const uninitializedEngine = new TesseractEngine();
      
      await expect(
        uninitializedEngine.processImage('data:image/png;base64,test')
      ).rejects.toThrow('OCR engine not initialized');
    });

    it('should handle processing errors gracefully', async () => {
      await engine.initialize();

      // Mock scheduler to throw error
      const mockScheduler = {
        addJob: vi.fn().mockRejectedValue(new Error('Processing failed')),
        terminate: vi.fn().mockResolvedValue(undefined),
      };

      (engine as unknown as { scheduler: typeof mockScheduler }).scheduler = mockScheduler;

      const progressCallback = vi.fn();

      await expect(
        engine.processImage('invalid-image-data', {
          pageNumber: 1,
          onProgress: progressCallback,
        })
      ).rejects.toThrow('Processing failed');

      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          status: 'error',
          message: expect.stringContaining('Failed to process page 1'),
        })
      );
    });
  });

  describe('Cleanup', () => {
    it('should terminate workers properly', async () => {
      await engine.initialize();
      
      const status1 = engine.getStatus();
      expect(status1.isInitialized).toBe(true);
      
      await engine.terminate();
      
      const status2 = engine.getStatus();
      expect(status2.isInitialized).toBe(false);
      expect(status2.workerCount).toBe(0);
    });
  });
});

describe('OCR Configuration and Options', () => {
  it('should support different language configurations', () => {
    const configs = [
      { language: 'eng' },
      { language: 'spa' },
      { language: ['eng', 'spa'] },
    ];
    
    configs.forEach(config => {
      const engine = new TesseractEngine(1, config);
      const status = engine.getStatus();
      expect(status.config.language).toEqual(config.language);
    });
  });

  it('should support preprocessing options', () => {
    // Should not throw when creating with these options
    expect(() => {
      new TesseractEngine(1, {
        language: 'eng',
      });
      // Options would be passed to processImage method
    }).not.toThrow();
  });
});
