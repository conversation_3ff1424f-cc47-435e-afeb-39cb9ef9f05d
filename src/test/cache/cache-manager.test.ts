import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { CacheManager, getCacheManager } from '@/lib/cache/cache-manager';

// Mock IndexedDB
const mockIndexedDB = {
  open: vi.fn().mockImplementation(() => ({
    onsuccess: null,
    onerror: null,
    onupgradeneeded: null,
    result: {
      objectStoreNames: { contains: vi.fn().mockReturnValue(false) },
      createObjectStore: vi.fn().mockReturnValue({
        createIndex: vi.fn(),
      }),
      transaction: vi.fn().mockReturnValue({
        objectStore: vi.fn().mockReturnValue({
          get: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
          put: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
          delete: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
          clear: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
          count: vi.fn().mockReturnValue({ onsuccess: null }),
          index: vi.fn().mockReturnValue({
            openCursor: vi.fn().mockReturnValue({ onsuccess: null }),
            getAll: vi.fn().mockReturnValue({ onsuccess: null, onerror: null }),
          }),
        }),
      }),
      close: vi.fn(),
    },
  })),
};

// Mock service worker
const mockServiceWorker = {
  register: vi.fn().mockResolvedValue(true),
  getStatus: vi.fn().mockReturnValue('registered'),
  isOffline: vi.fn().mockReturnValue(false),
  cachePDF: vi.fn().mockResolvedValue(true),
  cacheThumbnail: vi.fn().mockResolvedValue(true),
  clearCache: vi.fn().mockResolvedValue(true),
  getCacheStats: vi.fn().mockResolvedValue({
    pdfEntries: 5,
    thumbnailEntries: 10,
    totalEntries: 15,
  }),
};

// Mock the service worker module
vi.mock('@/lib/service-worker', () => ({
  getServiceWorkerManager: () => mockServiceWorker,
}));

// Mock page cache
const mockPageCache = {
  getPage: vi.fn().mockResolvedValue(null),
  setPage: vi.fn().mockResolvedValue(undefined),
  preloadAdjacentPages: vi.fn().mockResolvedValue(undefined),
  clearAll: vi.fn().mockResolvedValue(undefined),
  getStats: vi.fn().mockReturnValue({
    memoryPages: 5,
    memorySizeMB: 25.5,
    memoryUtilization: 0.5,
  }),
  destroy: vi.fn(),
};

// Mock thumbnail cache
const mockThumbnailCache = {
  getThumbnail: vi.fn().mockResolvedValue(null),
  setThumbnail: vi.fn().mockResolvedValue(undefined),
  generateThumbnail: vi.fn().mockResolvedValue(null),
  clearAll: vi.fn().mockResolvedValue(undefined),
  getStats: vi.fn().mockReturnValue({
    memoryThumbnails: 10,
    memorySizeMB: 5.2,
    memoryUtilization: 0.3,
    activeGenerations: 2,
    queuedGenerations: 1,
  }),
  destroy: vi.fn(),
};

// Mock the cache classes
vi.mock('@/lib/cache/page-cache', () => ({
  PageCache: vi.fn().mockImplementation(() => mockPageCache),
}));

vi.mock('@/lib/cache/thumbnail-cache', () => ({
  ThumbnailCache: vi.fn().mockImplementation(() => mockThumbnailCache),
}));

// Setup global mocks
Object.defineProperty(global, 'indexedDB', {
  value: mockIndexedDB,
  writable: true,
});

Object.defineProperty(global, 'window', {
  value: {
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  },
  writable: true,
});

describe('CacheManager', () => {
  let cacheManager: CacheManager;

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset singleton
    (CacheManager as unknown as { instance: null }).instance = null;
    cacheManager = getCacheManager();
  });

  afterEach(() => {
    cacheManager?.destroy();
  });

  describe('Initialization', () => {
    it('should create singleton instance', () => {
      const manager1 = getCacheManager();
      const manager2 = getCacheManager();
      expect(manager1).toBe(manager2);
    });

    it('should initialize with default config', () => {
      expect(cacheManager).toBeDefined();
      expect(cacheManager.isReady()).toBe(true);
    });

    it('should initialize with custom config', () => {
      const customConfig = {
        enableServiceWorker: false,
        enablePreloading: false,
      };
      
      const customManager = getCacheManager(customConfig);
      expect(customManager).toBeDefined();
      customManager.destroy();
    });
  });

  describe('Page Cache Operations', () => {
    const mockPageData = {
      documentId: 'test-doc',
      pageNumber: 1,
      scale: 1.0,
      rotation: 0,
      imageData: 'data:image/jpeg;base64,test',
      textContent: 'test content',
    };

    it('should get page from cache', async () => {
      const cachedPage = {
        ...mockPageData,
        timestamp: Date.now(),
        accessCount: 1,
        lastAccessed: Date.now(),
        size: 1024,
      };
      
      mockPageCache.getPage.mockResolvedValueOnce(cachedPage);
      
      const result = await cacheManager.getPage('test-doc', 1, 1.0, 0);
      
      expect(mockPageCache.getPage).toHaveBeenCalledWith('test-doc', 1, 1.0, 0);
      expect(result).toEqual(cachedPage);
    });

    it('should set page in cache', async () => {
      await cacheManager.setPage(mockPageData);
      
      expect(mockPageCache.setPage).toHaveBeenCalledWith(mockPageData);
      expect(mockServiceWorker.cachePDF).toHaveBeenCalledWith(
        mockPageData.imageData,
        `${mockPageData.documentId}_${mockPageData.pageNumber}`
      );
    });

    it('should handle service worker caching failure gracefully', async () => {
      mockServiceWorker.cachePDF.mockRejectedValueOnce(new Error('SW error'));
      
      // Should not throw
      await expect(cacheManager.setPage(mockPageData)).resolves.toBeUndefined();
      expect(mockPageCache.setPage).toHaveBeenCalled();
    });

    it('should preload pages', async () => {
      await cacheManager.preloadPages('test-doc', 5, 2, 1.0, 0);
      
      expect(mockPageCache.preloadAdjacentPages).toHaveBeenCalledWith('test-doc', 5, 2, 1.0, 0);
    });
  });

  describe('Thumbnail Cache Operations', () => {
    const mockThumbnailData = {
      documentId: 'test-doc',
      pageNumber: 1,
      scale: 1.0,
      imageData: 'data:image/jpeg;base64,thumbnail',
      width: 150,
      height: 200,
      quality: 0.8,
    };

    it('should get thumbnail from cache', async () => {
      const cachedThumbnail = {
        ...mockThumbnailData,
        timestamp: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        size: 512,
      };
      
      mockThumbnailCache.getThumbnail.mockResolvedValueOnce(cachedThumbnail);
      
      const result = await cacheManager.getThumbnail('test-doc', 1, 1.0);
      
      expect(mockThumbnailCache.getThumbnail).toHaveBeenCalledWith('test-doc', 1, 1.0);
      expect(result).toEqual(cachedThumbnail);
    });

    it('should set thumbnail in cache', async () => {
      await cacheManager.setThumbnail(mockThumbnailData);
      
      expect(mockThumbnailCache.setThumbnail).toHaveBeenCalledWith(mockThumbnailData);
      expect(mockServiceWorker.cacheThumbnail).toHaveBeenCalledWith(
        mockThumbnailData.imageData,
        mockThumbnailData.documentId,
        mockThumbnailData.pageNumber
      );
    });

    it('should generate thumbnail', async () => {
      const mockPDFPage = { render: vi.fn() };
      const generatedThumbnail = {
        ...mockThumbnailData,
        timestamp: Date.now(),
        lastAccessed: Date.now(),
        accessCount: 1,
        size: 512,
      };
      
      mockThumbnailCache.generateThumbnail.mockResolvedValueOnce(generatedThumbnail);
      
      const result = await cacheManager.generateThumbnail(mockPDFPage, 'test-doc', 1);
      
      expect(mockThumbnailCache.generateThumbnail).toHaveBeenCalledWith(
        mockPDFPage,
        'test-doc',
        1,
        undefined
      );
      expect(result).toEqual(generatedThumbnail);
    });
  });

  describe('Cache Management', () => {
    it('should clear all caches', async () => {
      await cacheManager.clearCache('all');
      
      expect(mockPageCache.clearAll).toHaveBeenCalled();
      expect(mockThumbnailCache.clearAll).toHaveBeenCalled();
      expect(mockServiceWorker.clearCache).toHaveBeenCalledWith('all');
    });

    it('should clear specific cache types', async () => {
      await cacheManager.clearCache('pages');
      expect(mockPageCache.clearAll).toHaveBeenCalled();
      expect(mockThumbnailCache.clearAll).not.toHaveBeenCalled();
      
      vi.clearAllMocks();
      
      await cacheManager.clearCache('thumbnails');
      expect(mockThumbnailCache.clearAll).toHaveBeenCalled();
      expect(mockPageCache.clearAll).not.toHaveBeenCalled();
      
      vi.clearAllMocks();
      
      await cacheManager.clearCache('serviceWorker');
      expect(mockServiceWorker.clearCache).toHaveBeenCalledWith('all');
    });

    it('should get comprehensive stats', async () => {
      const stats = await cacheManager.getStats();
      
      expect(stats).toEqual({
        pages: {
          memoryPages: 5,
          memorySizeMB: 25.5,
          memoryUtilization: 0.5,
        },
        thumbnails: {
          memoryThumbnails: 10,
          memorySizeMB: 5.2,
          memoryUtilization: 0.3,
          activeGenerations: 2,
          queuedGenerations: 1,
        },
        serviceWorker: {
          pdfEntries: 5,
          thumbnailEntries: 10,
          totalEntries: 15,
        },
        global: {
          totalMemoryMB: 30.7, // 25.5 + 5.2
          totalMemoryUtilization: expect.any(Number),
          isOnline: true,
          serviceWorkerActive: true,
        },
      });
    });

    it('should optimize cache when memory usage is high', async () => {
      // Create a spy for getStats to return high memory usage
      const getStatsSpy = vi.spyOn(cacheManager, 'getStats').mockResolvedValueOnce({
        pages: {
          memoryPages: 5,
          memorySizeMB: 25.5,
          memoryUtilization: 0.5,
        },
        thumbnails: {
          memoryThumbnails: 10,
          memorySizeMB: 5.2,
          memoryUtilization: 0.9, // High utilization
          activeGenerations: 2,
          queuedGenerations: 1,
        },
        serviceWorker: {
          pdfEntries: 5,
          thumbnailEntries: 10,
          totalEntries: 15,
        },
        global: {
          totalMemoryMB: 30.7,
          totalMemoryUtilization: 0.95, // Very high global utilization
          isOnline: true,
          serviceWorkerActive: true,
        },
      });

      await cacheManager.optimizeCache();

      // Should clear thumbnails due to high utilization
      expect(mockThumbnailCache.clearAll).toHaveBeenCalled();

      getStatsSpy.mockRestore();
    });
  });

  describe('Configuration', () => {
    it('should update configuration', () => {
      const updates = {
        enablePreloading: false,
        syncInterval: 60000,
      };
      
      expect(() => {
        cacheManager.updateConfig(updates);
      }).not.toThrow();
    });
  });

  describe('Preloading', () => {
    it('should schedule preload when getting pages', async () => {
      const mockPageData = {
        documentId: 'test-doc',
        pageNumber: 5,
        scale: 1.0,
        rotation: 0,
        imageData: 'data:image/jpeg;base64,test',
        timestamp: Date.now(),
        accessCount: 1,
        lastAccessed: Date.now(),
        size: 1024,
      };
      
      mockPageCache.getPage.mockResolvedValueOnce(mockPageData);
      
      await cacheManager.getPage('test-doc', 5, 1.0, 0);
      
      // Wait for preload to be scheduled
      await new Promise(resolve => setTimeout(resolve, 600));
      
      expect(mockPageCache.preloadAdjacentPages).toHaveBeenCalledWith('test-doc', 5, 2, 1.0, 0);
    });

    it('should not preload when disabled', async () => {
      // Reset singleton to ensure clean state
      (CacheManager as unknown as { instance: null }).instance = null;

      // Reset the mock to clear any previous calls
      mockPageCache.preloadAdjacentPages.mockClear();

      const noPreloadManager = getCacheManager({ enablePreloading: false });

      const mockPageData = {
        documentId: 'test-doc-no-preload',
        pageNumber: 10,
        scale: 1.0,
        rotation: 0,
        imageData: 'data:image/jpeg;base64,test-no-preload',
        timestamp: Date.now(),
        accessCount: 1,
        lastAccessed: Date.now(),
        size: 1024,
      };

      mockPageCache.getPage.mockResolvedValueOnce(mockPageData);

      await noPreloadManager.getPage('test-doc-no-preload', 10, 1.0, 0);

      // Wait to ensure no preload is triggered
      await new Promise(resolve => setTimeout(resolve, 600));

      // Since preloading is disabled, no preload calls should be made at all
      expect(mockPageCache.preloadAdjacentPages).not.toHaveBeenCalled();

      noPreloadManager.destroy();

      // Reset singleton for other tests
      (CacheManager as unknown as { instance: null }).instance = null;
    });
  });

  describe('Error Handling', () => {
    it('should handle page cache errors gracefully', async () => {
      mockPageCache.getPage.mockRejectedValueOnce(new Error('Cache error'));
      
      const result = await cacheManager.getPage('test-doc', 1);
      expect(result).toBeNull();
    });

    it('should handle thumbnail cache errors gracefully', async () => {
      mockThumbnailCache.getThumbnail.mockRejectedValueOnce(new Error('Thumbnail error'));
      
      const result = await cacheManager.getThumbnail('test-doc', 1);
      expect(result).toBeNull();
    });

    it('should handle stats errors gracefully', async () => {
      mockServiceWorker.getCacheStats.mockRejectedValueOnce(new Error('Stats error'));
      
      const stats = await cacheManager.getStats();
      expect(stats.serviceWorker).toEqual({
        pdfEntries: 0,
        thumbnailEntries: 0,
        totalEntries: 0,
      });
    });
  });

  describe('Cleanup', () => {
    it('should cleanup resources on destroy', () => {
      cacheManager.destroy();
      
      expect(mockPageCache.destroy).toHaveBeenCalled();
      expect(mockThumbnailCache.destroy).toHaveBeenCalled();
    });
  });
});
