import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { extractPDFDocument } from '@/lib/types/pdf'
import type { PDFDocumentProxy } from 'pdfjs-dist'

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Document: vi.fn(({ children, onLoadSuccess }) => {
    // Simulate successful load after a short delay
    setTimeout(() => {
      if (onLoadSuccess) {
        onLoadSuccess({
          numPages: 5,
          _pdfInfo: { pdfDocument: { getOutline: () => Promise.resolve([]) } }
        })
      }
    }, 100)
    return <div data-testid="pdf-document">{children}</div>
  }),
  Page: vi.fn(({ pageNumber, onLoadSuccess }) => {
    setTimeout(() => {
      if (onLoadSuccess) {
        onLoadSuccess({ width: 612, height: 792 })
      }
    }, 50)
    return <div data-testid={`pdf-page-${pageNumber}`}>Page {pageNumber}</div>
  }),
  pdfjs: {
    GlobalWorkerOptions: {},
    version: '3.11.174',
  },
}))

// Mock PDF types utility
vi.mock('@/lib/types/pdf', () => ({
  extractPDFDocument: vi.fn((pdf) => {
    if (pdf._pdfInfo?.pdfDocument) {
      return pdf._pdfInfo.pdfDocument
    }
    return pdf
  }),
}))

describe('PDF Document Loading', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Document Loading Success', () => {
    it('loads PDF document successfully', async () => {
      const onLoadSuccess = vi.fn()
      const file = new File(['test pdf content'], 'test.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={file}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument()

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith({
          numPages: 5,
          _pdfInfo: { pdfDocument: { getOutline: expect.any(Function) } }
        })
      })
    })

    it('extracts PDF document proxy correctly', () => {
      const mockPdfDocument = { getOutline: vi.fn() }
      const pdfWithInfo = {
        numPages: 3,
        _pdfInfo: { pdfDocument: mockPdfDocument }
      }

      const result = extractPDFDocument(pdfWithInfo)

      expect(result).toBe(mockPdfDocument)
      expect(extractPDFDocument).toHaveBeenCalledWith(pdfWithInfo)
    })

    it('handles direct PDF document proxy', () => {
      const mockPdfDocument = { getOutline: vi.fn(), numPages: 3 }

      const result = extractPDFDocument(mockPdfDocument)

      expect(result).toBe(mockPdfDocument)
    })

    it('loads document from URL', async () => {
      const onLoadSuccess = vi.fn()
      const url = 'https://example.com/test.pdf'

      render(
        <Document
          file={url}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument()

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalled()
      })
    })

    it('loads document from ArrayBuffer', async () => {
      const onLoadSuccess = vi.fn()
      const buffer = new ArrayBuffer(1024)

      render(
        <Document
          file={buffer}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument()

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalled()
      })
    })
  })

  describe('Document Loading Errors', () => {
    it('handles document load errors', async () => {
      const onLoadError = vi.fn()
      const file = new File(['invalid content'], 'invalid.pdf', { type: 'application/pdf' })

      // Mock Document to trigger error
      vi.mocked(Document).mockImplementationOnce(({ onLoadError: errorCallback }) => {
        setTimeout(() => {
          if (errorCallback) {
            errorCallback(new Error('Invalid PDF format'))
          }
        }, 100)
        return <div data-testid="pdf-document-error">Error loading PDF</div>
      })

      render(
        <Document
          file={file}
          onLoadError={onLoadError}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadError).toHaveBeenCalledWith(new Error('Invalid PDF format'))
      })
    })

    it('handles network errors for URL loading', async () => {
      const onLoadError = vi.fn()
      const url = 'https://invalid-url.com/nonexistent.pdf'

      vi.mocked(Document).mockImplementationOnce(({ onLoadError: errorCallback }) => {
        setTimeout(() => {
          if (errorCallback) {
            errorCallback(new Error('Failed to fetch'))
          }
        }, 100)
        return <div data-testid="pdf-document-error">Network Error</div>
      })

      render(
        <Document
          file={url}
          onLoadError={onLoadError}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadError).toHaveBeenCalledWith(new Error('Failed to fetch'))
      })
    })

    it('handles password-protected PDFs', async () => {
      const onLoadError = vi.fn()
      const file = new File(['encrypted pdf'], 'encrypted.pdf', { type: 'application/pdf' })

      vi.mocked(Document).mockImplementationOnce(({ onLoadError: errorCallback }) => {
        setTimeout(() => {
          if (errorCallback) {
            errorCallback(new Error('Password required'))
          }
        }, 100)
        return <div data-testid="pdf-document-error">Password Required</div>
      })

      render(
        <Document
          file={file}
          onLoadError={onLoadError}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadError).toHaveBeenCalledWith(new Error('Password required'))
      })
    })
  })

  describe('Document Metadata Extraction', () => {
    it('extracts document outline successfully', async () => {
      const mockOutline = [
        { title: 'Chapter 1', dest: [0, { name: 'XYZ' }, 0, 792, null] },
        { title: 'Chapter 2', dest: [1, { name: 'XYZ' }, 0, 792, null] }
      ]

      const mockPdfDocument = {
        getOutline: vi.fn().mockResolvedValue(mockOutline),
        numPages: 5
      }

      const pdfWithInfo = {
        numPages: 5,
        _pdfInfo: { pdfDocument: mockPdfDocument }
      }

      const extractedDoc = extractPDFDocument(pdfWithInfo) as PDFDocumentProxy
      const outline = await extractedDoc.getOutline()

      expect(outline).toEqual(mockOutline)
      expect(mockPdfDocument.getOutline).toHaveBeenCalled()
    })

    it('handles documents without outline', async () => {
      const mockPdfDocument = {
        getOutline: vi.fn().mockResolvedValue(null),
        numPages: 3
      }

      const pdfWithInfo = {
        numPages: 3,
        _pdfInfo: { pdfDocument: mockPdfDocument }
      }

      const extractedDoc = extractPDFDocument(pdfWithInfo) as PDFDocumentProxy
      const outline = await extractedDoc.getOutline()

      expect(outline).toBeNull()
      expect(mockPdfDocument.getOutline).toHaveBeenCalled()
    })

    it('handles outline extraction errors gracefully', async () => {
      const mockPdfDocument = {
        getOutline: vi.fn().mockRejectedValue(new Error('Outline extraction failed')),
        numPages: 3
      }

      const pdfWithInfo = {
        numPages: 3,
        _pdfInfo: { pdfDocument: mockPdfDocument }
      }

      const extractedDoc = extractPDFDocument(pdfWithInfo) as PDFDocumentProxy

      await expect(extractedDoc.getOutline()).rejects.toThrow('Outline extraction failed')
    })
  })

  describe('Document Properties', () => {
    it('provides correct page count', async () => {
      const onLoadSuccess = vi.fn()
      const file = new File(['test pdf'], 'test.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={file}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith(
          expect.objectContaining({ numPages: 5 })
        )
      })
    })

    it('provides PDF document proxy for advanced operations', async () => {
      const onLoadSuccess = vi.fn()
      const file = new File(['test pdf'], 'test.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={file}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            _pdfInfo: expect.objectContaining({
              pdfDocument: expect.any(Object)
            })
          })
        )
      })
    })
  })

  describe('Loading States', () => {
    it('shows loading state while document loads', () => {
      const file = new File(['test pdf'], 'test.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={file}
          loading={<div data-testid="loading">Loading PDF...</div>}
        >
          <div>PDF Content</div>
        </Document>
      )

      // The loading state should be visible initially
      expect(screen.getByTestId('pdf-document')).toBeInTheDocument()
    })

    it('handles custom loading component', () => {
      const CustomLoading = () => <div data-testid="custom-loading">Custom Loading...</div>
      const file = new File(['test pdf'], 'test.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={file}
          loading={<CustomLoading />}
        >
          <div>PDF Content</div>
        </Document>
      )

      expect(screen.getByTestId('pdf-document')).toBeInTheDocument()
    })
  })

  describe('File Type Validation', () => {
    it('accepts valid PDF files', async () => {
      const onLoadSuccess = vi.fn()
      const validPdfFile = new File(['%PDF-1.4'], 'valid.pdf', { type: 'application/pdf' })

      render(
        <Document
          file={validPdfFile}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalled()
      })
    })

    it('handles files with incorrect MIME type but valid PDF content', async () => {
      const onLoadSuccess = vi.fn()
      const pdfWithWrongMime = new File(['%PDF-1.4'], 'document.pdf', { type: 'text/plain' })

      render(
        <Document
          file={pdfWithWrongMime}
          onLoadSuccess={onLoadSuccess}
        >
          <div>PDF Content</div>
        </Document>
      )

      await waitFor(() => {
        expect(onLoadSuccess).toHaveBeenCalled()
      })
    })
  })
})
