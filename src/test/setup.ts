import '@testing-library/jest-dom'
import { vi } from 'vitest'
import React from 'react'

// Mock the cn utility function
vi.mock('@/lib/utils', () => ({
  cn: (...classes: (string | undefined | null | boolean)[]) =>
    classes.filter(Boolean).join(' ')
}))

// Mock react-pdf
vi.mock('react-pdf', () => ({
  Page: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-page', ...props }, children)
  }),
  Document: vi.fn().mockImplementation(({ children, ...props }: { children?: React.ReactNode; [key: string]: unknown }) => {
    return React.createElement('div', { 'data-testid': 'pdf-document', ...props }, children)
  }),
  pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: '',
    },
  },
}))

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}))

// Mock sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
  },
}))

// Mock service worker
const mockServiceWorkerManager = {
  initialize: vi.fn().mockResolvedValue(true),
  register: vi.fn().mockResolvedValue(true),
  isSupported: vi.fn().mockReturnValue(true),
  isRegistered: vi.fn().mockReturnValue(true),
  sendMessage: vi.fn().mockResolvedValue({ success: true }),
  cachePDF: vi.fn().mockResolvedValue(true),
  cacheThumbnail: vi.fn().mockResolvedValue(true),
  clearCache: vi.fn().mockResolvedValue(true),
  getCacheStats: vi.fn().mockResolvedValue({
    pdfEntries: 0,
    thumbnailEntries: 0,
    totalEntries: 0,
    estimatedSize: undefined
  }),
  destroy: vi.fn()
};

vi.mock('@/lib/service-worker', () => ({
  ServiceWorkerManager: vi.fn().mockImplementation(() => mockServiceWorkerManager),
  getServiceWorkerManager: vi.fn().mockReturnValue(mockServiceWorkerManager)
}))

// Mock document library
vi.mock('@/lib/document-library', () => ({
  documentLibrary: {
    initialize: vi.fn().mockResolvedValue(undefined),
    addDocument: vi.fn().mockResolvedValue({}),
    getDocument: vi.fn().mockResolvedValue(null),
    updateDocument: vi.fn().mockResolvedValue(undefined),
    removeDocument: vi.fn().mockResolvedValue(undefined),
    getAllDocuments: vi.fn().mockResolvedValue([]),
    searchDocuments: vi.fn().mockResolvedValue({ documents: [], totalCount: 0, facets: {} }),
    getRecentDocuments: vi.fn().mockResolvedValue([]),
    getFavoriteDocuments: vi.fn().mockResolvedValue([]),
    getPinnedDocuments: vi.fn().mockResolvedValue([]),
    createCollection: vi.fn().mockResolvedValue({}),
    getAllCollections: vi.fn().mockResolvedValue([]),
    addDocumentToCollection: vi.fn().mockResolvedValue(undefined),
    removeDocumentFromCollection: vi.fn().mockResolvedValue(undefined),
    getDocumentsInCollection: vi.fn().mockResolvedValue([]),
    initializeDefaultCollections: vi.fn().mockResolvedValue(undefined),
    getSettings: vi.fn().mockResolvedValue({}),
    updateSettings: vi.fn().mockResolvedValue(undefined),
  },
  DocumentLibraryStorage: vi.fn().mockImplementation(() => ({
    initialize: vi.fn().mockResolvedValue(undefined),
    addDocument: vi.fn().mockResolvedValue({}),
    getDocument: vi.fn().mockResolvedValue(null),
    updateDocument: vi.fn().mockResolvedValue(undefined),
    removeDocument: vi.fn().mockResolvedValue(undefined),
    getAllDocuments: vi.fn().mockResolvedValue([]),
    searchDocuments: vi.fn().mockResolvedValue({ documents: [], totalCount: 0, facets: {} }),
    getRecentDocuments: vi.fn().mockResolvedValue([]),
    getFavoriteDocuments: vi.fn().mockResolvedValue([]),
    getPinnedDocuments: vi.fn().mockResolvedValue([]),
    createCollection: vi.fn().mockResolvedValue({}),
    getAllCollections: vi.fn().mockResolvedValue([]),
    addDocumentToCollection: vi.fn().mockResolvedValue(undefined),
    removeDocumentFromCollection: vi.fn().mockResolvedValue(undefined),
    getDocumentsInCollection: vi.fn().mockResolvedValue([]),
    initializeDefaultCollections: vi.fn().mockResolvedValue(undefined),
    getSettings: vi.fn().mockResolvedValue({}),
    updateSettings: vi.fn().mockResolvedValue(undefined),
  }))
}))

// Mock mobile modules
vi.mock('@/lib/mobile/gesture-engine', () => ({
  GestureEngine: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    updateCallbacks: vi.fn(),
  }))
}));

vi.mock('@/hooks/use-layout-context', () => ({
  useLayoutContext: vi.fn().mockReturnValue({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    orientation: 'landscape',
    screenSize: { width: 1920, height: 1080 },
    breakpoint: 'xl'
  })
}));

// Mock Tesseract.js
vi.mock('tesseract.js', () => ({
  createScheduler: vi.fn().mockReturnValue({
    addWorker: vi.fn().mockResolvedValue(undefined),
    addJob: vi.fn().mockImplementation((action) => {
      if (action === 'recognize') {
        return Promise.resolve({
          data: {
            text: 'Mock OCR text content',
            confidence: 85,
            words: [
              {
                text: 'Mock',
                confidence: 90,
                bbox: { x0: 10, y0: 10, x1: 50, y1: 30 }
              },
              {
                text: 'OCR',
                confidence: 85,
                bbox: { x0: 55, y0: 10, x1: 85, y1: 30 }
              },
              {
                text: 'text',
                confidence: 80,
                bbox: { x0: 90, y0: 10, x1: 120, y1: 30 }
              }
            ]
          }
        });
      }
      return Promise.resolve({});
    }),
    terminate: vi.fn().mockResolvedValue(undefined),
  }),
  createWorker: vi.fn().mockReturnValue({
    load: vi.fn().mockResolvedValue(undefined),
    loadLanguage: vi.fn().mockResolvedValue(undefined),
    initialize: vi.fn().mockResolvedValue(undefined),
    recognize: vi.fn().mockResolvedValue({
      data: {
        text: 'Mock OCR text content',
        confidence: 85,
        words: []
      }
    }),
    terminate: vi.fn().mockResolvedValue(undefined),
  }),
  PSM: {
    SINGLE_BLOCK: 6,
    SINGLE_LINE: 7,
    SINGLE_WORD: 8,
  },
  OEM: {
    TESSERACT_LSTM_COMBINED: 1,
  }
}));

// Mock IndexedDB
const mockIDBRequest = {
  result: null,
  error: null,
  onsuccess: null,
  onerror: null,
  readyState: 'done',
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
};

const mockIDBTransaction = {
  objectStore: vi.fn().mockReturnValue({
    add: vi.fn().mockReturnValue(mockIDBRequest),
    put: vi.fn().mockReturnValue(mockIDBRequest),
    get: vi.fn().mockReturnValue(mockIDBRequest),
    delete: vi.fn().mockReturnValue(mockIDBRequest),
    clear: vi.fn().mockReturnValue(mockIDBRequest),
    count: vi.fn().mockReturnValue(mockIDBRequest),
    getAll: vi.fn().mockReturnValue(mockIDBRequest),
    getAllKeys: vi.fn().mockReturnValue(mockIDBRequest),
    index: vi.fn().mockReturnValue({
      get: vi.fn().mockReturnValue(mockIDBRequest),
      getAll: vi.fn().mockReturnValue(mockIDBRequest),
    }),
    createIndex: vi.fn(),
    deleteIndex: vi.fn(),
  }),
  abort: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
};

const mockIDBDatabase = {
  createObjectStore: vi.fn().mockReturnValue({
    createIndex: vi.fn(),
    deleteIndex: vi.fn(),
  }),
  deleteObjectStore: vi.fn(),
  transaction: vi.fn().mockReturnValue(mockIDBTransaction),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
  version: 1,
  name: 'test-db',
  objectStoreNames: [],
};

global.indexedDB = {
  open: vi.fn().mockImplementation(() => {
    const request = { ...mockIDBRequest };
    setTimeout(() => {
      request.result = mockIDBDatabase;
      if (request.onsuccess) request.onsuccess({ target: request });
    }, 0);
    return request;
  }),
  deleteDatabase: vi.fn().mockReturnValue(mockIDBRequest),
  databases: vi.fn().mockResolvedValue([]),
  cmp: vi.fn(),
};

// Global test utilities
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock TouchEvent for mobile tests
global.TouchEvent = vi.fn().mockImplementation((type, options = {}) => {
  const event = new Event(type, options)
  Object.defineProperty(event, 'touches', {
    value: options.touches || [],
    writable: false
  })
  Object.defineProperty(event, 'targetTouches', {
    value: options.targetTouches || [],
    writable: false
  })
  Object.defineProperty(event, 'changedTouches', {
    value: options.changedTouches || [],
    writable: false
  })
  return event
})

// Mock Touch constructor
global.Touch = vi.fn().mockImplementation((options = {}) => ({
  identifier: options.identifier || 0,
  target: options.target || null,
  clientX: options.clientX || 0,
  clientY: options.clientY || 0,
  pageX: options.pageX || 0,
  pageY: options.pageY || 0,
  screenX: options.screenX || 0,
  screenY: options.screenY || 0,
  radiusX: options.radiusX || 0,
  radiusY: options.radiusY || 0,
  rotationAngle: options.rotationAngle || 0,
  force: options.force || 1
}))

// Mock ImageData for canvas/image processing tests
global.ImageData = vi.fn().mockImplementation((width: number, height: number) => ({
  width,
  height,
  data: new Uint8ClampedArray(width * height * 4), // RGBA
}))