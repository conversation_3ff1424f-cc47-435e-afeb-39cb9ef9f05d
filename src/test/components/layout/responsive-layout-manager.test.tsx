import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { render, waitFor } from '@testing-library/react'
import ResponsiveLayoutManager, {
  DEFAULT_CONFIG,
  type ResponsiveLayoutConfig
} from '@/components/layout/responsive-layout-manager'

// Mock window resize functionality
const mockWindowSize = (width: number, height: number) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  })
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  })
  
  // Trigger resize event
  window.dispatchEvent(new Event('resize'))
}

// Test component to verify layout context
const TestChild = ({ testId = 'test-child' }: { testId?: string }) => (
  <div data-testid={testId} className="test-child">
    Test Content
  </div>
)

describe('ResponsiveLayoutManager', () => {
  const mockOnLayoutChange = vi.fn()
  
  beforeEach(() => {
    vi.clearAllMocks()
    // Set default window size
    mockWindowSize(1920, 1080)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe('Desktop Space Utilization Fix', () => {
    it('should utilize full available space on desktop screens', async () => {
      // Test large desktop viewport (1920px)
      mockWindowSize(1920, 1080)
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild testId="desktop-content" />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
      })

      const layoutContainer = container.querySelector('.responsive-layout-container') as HTMLElement
      const computedStyle = window.getComputedStyle(layoutContainer)
      
      // Should not have restrictive max-width since viewport (1920px) > maxContentWidth (1600px)
      expect(computedStyle.maxWidth).toBe('1600px')
      expect(computedStyle.marginLeft).toBe('auto')
      expect(computedStyle.marginRight).toBe('auto')
    })

    it('should use full width on smaller desktop screens', async () => {
      // Test smaller desktop viewport (1366px) - should use full width
      mockWindowSize(1366, 768)
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild testId="smaller-desktop-content" />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
      })

      const layoutContainer = container.querySelector('.responsive-layout-container') as HTMLElement
      const computedStyle = window.getComputedStyle(layoutContainer)
      
      // Should not have max-width constraint since viewport (1366px) < maxContentWidth (1600px)
      expect(computedStyle.maxWidth).toBe('')
    })

    it('should handle ultrawide displays properly', async () => {
      // Test ultrawide viewport (2560px)
      mockWindowSize(2560, 1440)
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild testId="ultrawide-content" />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
        expect(layoutContainer).toHaveAttribute('data-breakpoint', 'ultrawide')
      })

      // Should trigger layout change callback for ultrawide
      expect(mockOnLayoutChange).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'ultrawide',
          minWidth: 2560,
          columns: 4
        })
      )
    })

    it('should verify maxContentWidth configuration', () => {
      // Verify the fix: maxContentWidth should be 1600px for better desktop utilization
      expect(DEFAULT_CONFIG.maxContentWidth).toBe(1600)
      expect(DEFAULT_CONFIG.minContentWidth).toBe(320)
    })
  })

  describe('Breakpoint Detection', () => {
    const testCases = [
      { width: 375, height: 667, expectedBreakpoint: 'mobile' },
      { width: 768, height: 1024, expectedBreakpoint: 'tablet' },
      { width: 1024, height: 768, expectedBreakpoint: 'laptop' },
      { width: 1366, height: 768, expectedBreakpoint: 'desktop' },
      { width: 1920, height: 1080, expectedBreakpoint: 'large-desktop' },
      { width: 2560, height: 1440, expectedBreakpoint: 'ultrawide' },
    ]

    testCases.forEach(({ width, height, expectedBreakpoint }) => {
      it(`should detect ${expectedBreakpoint} breakpoint at ${width}x${height}`, async () => {
        mockWindowSize(width, height)
        
        const { container } = render(
          <ResponsiveLayoutManager 
            config={DEFAULT_CONFIG}
            onLayoutChange={mockOnLayoutChange}
          >
            <TestChild />
          </ResponsiveLayoutManager>
        )

        await waitFor(() => {
          const layoutContainer = container.querySelector('.responsive-layout-container')
          expect(layoutContainer).toHaveAttribute('data-breakpoint', expectedBreakpoint)
        })
      })
    })
  })

  describe('Layout Configuration', () => {
    it('should apply custom configuration', async () => {
      const customConfig: ResponsiveLayoutConfig = {
        ...DEFAULT_CONFIG,
        maxContentWidth: 1200,
        minContentWidth: 400
      }

      mockWindowSize(1400, 800)
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={customConfig}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
      })

      const layoutContainer = container.querySelector('.responsive-layout-container') as HTMLElement
      const computedStyle = window.getComputedStyle(layoutContainer)
      
      // Should apply custom maxContentWidth
      expect(computedStyle.maxWidth).toBe('1200px')
    })

    it('should handle responsive grid layout', async () => {
      mockWindowSize(1920, 1080) // large-desktop breakpoint
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
      })

      const layoutContainer = container.querySelector('.responsive-layout-container') as HTMLElement
      const computedStyle = window.getComputedStyle(layoutContainer)
      
      // Should have grid layout for large-desktop (3 columns)
      expect(computedStyle.display).toBe('grid')
      expect(computedStyle.gridTemplateColumns).toBe('repeat(3, 1fr)')
    })
  })

  describe('CSS Custom Properties', () => {
    it('should set correct CSS custom properties', async () => {
      mockWindowSize(1920, 1080)
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toBeInTheDocument()
      })

      const layoutContainer = container.querySelector('.responsive-layout-container') as HTMLElement
      
      // Check data attributes
      expect(layoutContainer).toHaveAttribute('data-breakpoint', 'large-desktop')
      expect(layoutContainer).toHaveAttribute('data-orientation', 'landscape')
      expect(layoutContainer).toHaveAttribute('data-density', 'comfortable')
    })
  })

  describe('Orientation Detection', () => {
    it('should detect portrait orientation', async () => {
      mockWindowSize(768, 1024) // Portrait tablet
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toHaveAttribute('data-orientation', 'portrait')
      })
    })

    it('should detect landscape orientation', async () => {
      mockWindowSize(1024, 768) // Landscape laptop
      
      const { container } = render(
        <ResponsiveLayoutManager 
          config={DEFAULT_CONFIG}
          onLayoutChange={mockOnLayoutChange}
        >
          <TestChild />
        </ResponsiveLayoutManager>
      )

      await waitFor(() => {
        const layoutContainer = container.querySelector('.responsive-layout-container')
        expect(layoutContainer).toHaveAttribute('data-orientation', 'landscape')
      })
    })
  })
})
