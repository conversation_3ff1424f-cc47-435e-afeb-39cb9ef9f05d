import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TouchGestureHandler, type GestureCallbacks } from '@/lib/mobile/touch-gestures';

// Mock DOM APIs
Object.defineProperty(global, 'navigator', {
  value: {
    vibrate: vi.fn(),
  },
  writable: true,
});

// Mock setTimeout/clearTimeout
global.setTimeout = vi.fn((fn, delay) => {
  if (typeof fn === 'function') {
    // Don't call immediately for long press tests
    if (delay && delay > 100) {
      // Simulate long press timeout
      setTimeout(() => fn(), 0);
    }
  }
  return 1;
}) as unknown as typeof setTimeout;

global.clearTimeout = vi.fn();

// Global helper functions for touch events
const createTouch = (id: number, x: number, y: number) => ({
  identifier: id,
  clientX: x,
  clientY: y,
  target: null, // Will be set when used
});

const createTouchEvent = (type: string, touches: Touch[], changedTouches?: Touch[]) => {
  const event = new Event(type) as TouchEvent;
  event.touches = touches;
  event.changedTouches = changedTouches || touches;
  event.preventDefault = vi.fn();
  event.stopPropagation = vi.fn();
  return event;
};

describe('TouchGestureHandler', () => {
  let element: HTMLElement;
  let gestureHandler: TouchGestureHandler;
  let mockCallbacks: GestureCallbacks;

  beforeEach(() => {
    vi.clearAllMocks();
    
    element = document.createElement('div');
    document.body.appendChild(element);
    
    mockCallbacks = {
      onTap: vi.fn(),
      onDoubleTap: vi.fn(),
      onLongPress: vi.fn(),
      onPanStart: vi.fn(),
      onPanMove: vi.fn(),
      onPanEnd: vi.fn(),
      onPinchStart: vi.fn(),
      onPinchMove: vi.fn(),
      onPinchEnd: vi.fn(),
      onSwipe: vi.fn(),
      onRotateStart: vi.fn(),
      onRotateMove: vi.fn(),
      onRotateEnd: vi.fn(),
    };

    gestureHandler = new TouchGestureHandler(element, mockCallbacks);
  });

  afterEach(() => {
    gestureHandler.destroy();
    document.body.removeChild(element);
  });

  describe('Initialization', () => {
    it('should initialize with default config', () => {
      expect(gestureHandler).toBeDefined();
    });

    it('should initialize with custom config', () => {
      const customHandler = new TouchGestureHandler(element, mockCallbacks, {
        tapThreshold: 20,
        enableHapticFeedback: false,
      });
      
      expect(customHandler).toBeDefined();
      customHandler.destroy();
    });

    it('should setup event listeners', () => {
      const addEventListenerSpy = vi.spyOn(element, 'addEventListener');
      
      new TouchGestureHandler(element, mockCallbacks);
      
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function), expect.any(Object));
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function), expect.any(Object));
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function), expect.any(Object));
      expect(addEventListenerSpy).toHaveBeenCalledWith('touchcancel', expect.any(Function), expect.any(Object));
    });
  });

  describe('Touch Events', () => {

    it('should handle single touch start', () => {
      const touch = createTouch(1, 100, 100);
      const event = createTouchEvent('touchstart', [touch]);
      
      element.dispatchEvent(event);
      
      expect(event.preventDefault).toHaveBeenCalled();
    });

    it('should handle touch move', () => {
      const startTouch = createTouch(1, 100, 100);
      const moveTouch = createTouch(1, 120, 120);
      
      const startEvent = createTouchEvent('touchstart', [startTouch]);
      const moveEvent = createTouchEvent('touchmove', [moveTouch]);
      
      element.dispatchEvent(startEvent);
      element.dispatchEvent(moveEvent);
      
      expect(moveEvent.preventDefault).toHaveBeenCalled();
    });

    it('should handle touch end', () => {
      const touch = createTouch(1, 100, 100);
      const startEvent = createTouchEvent('touchstart', [touch]);
      const endEvent = createTouchEvent('touchend', [], [touch]);
      
      element.dispatchEvent(startEvent);
      element.dispatchEvent(endEvent);
      
      expect(endEvent.preventDefault).toHaveBeenCalled();
    });
  });

  describe('Tap Gestures', () => {

    it('should detect single tap', () => {
      const touch = createTouch(1, 100, 100);
      
      const startEvent = createTouchEvent('touchstart', [touch]);
      const endEvent = createTouchEvent('touchend', [], [touch]);
      
      element.dispatchEvent(startEvent);
      element.dispatchEvent(endEvent);
      
      expect(mockCallbacks.onTap).toHaveBeenCalled();
    });

    it('should detect double tap', () => {
      const touch1 = createTouch(1, 100, 100);
      const touch2 = createTouch(2, 102, 102); // Close to first tap
      
      // First tap
      element.dispatchEvent(createTouchEvent('touchstart', [touch1]));
      element.dispatchEvent(createTouchEvent('touchend', [], [touch1]));
      
      // Second tap (should trigger double tap)
      element.dispatchEvent(createTouchEvent('touchstart', [touch2]));
      element.dispatchEvent(createTouchEvent('touchend', [], [touch2]));
      
      expect(mockCallbacks.onDoubleTap).toHaveBeenCalled();
    });

    it('should detect long press', () => {
      const touch = createTouch(1, 100, 100);
      
      element.dispatchEvent(createTouchEvent('touchstart', [touch]));
      
      // Long press timer should trigger
      expect(mockCallbacks.onLongPress).toHaveBeenCalled();
    });

    it('should cancel long press on movement', () => {
      const startTouch = createTouch(1, 100, 100);
      const moveTouch = createTouch(1, 150, 150); // Move beyond threshold
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch]));
      
      expect(global.clearTimeout).toHaveBeenCalled();
    });
  });

  describe('Pan Gestures', () => {

    it('should detect pan start', () => {
      const startTouch = createTouch(1, 100, 100);
      const moveTouch = createTouch(1, 130, 130); // Move beyond pan threshold
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch]));
      
      expect(mockCallbacks.onPanStart).toHaveBeenCalled();
    });

    it('should detect pan move', () => {
      const startTouch = createTouch(1, 100, 100);
      const moveTouch1 = createTouch(1, 130, 130);
      const moveTouch2 = createTouch(1, 140, 140);
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch1]));
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch2]));
      
      expect(mockCallbacks.onPanMove).toHaveBeenCalled();
    });

    it('should detect pan end', () => {
      const startTouch = createTouch(1, 100, 100);
      const moveTouch = createTouch(1, 130, 130);
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch]));
      element.dispatchEvent(createTouchEvent('touchend', [], [moveTouch]));
      
      expect(mockCallbacks.onPanEnd).toHaveBeenCalled();
    });
  });

  describe('Pinch Gestures', () => {

    it('should detect pinch start with two fingers', () => {
      const touch1 = createTouch(1, 100, 100);
      const touch2 = createTouch(2, 200, 200);
      
      element.dispatchEvent(createTouchEvent('touchstart', [touch1, touch2]));
      
      // Move fingers to change distance
      const moveTouch1 = createTouch(1, 90, 90);
      const moveTouch2 = createTouch(2, 210, 210);
      
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch1, moveTouch2]));
      
      expect(mockCallbacks.onPinchStart).toHaveBeenCalled();
    });

    it('should detect pinch move', () => {
      const touch1 = createTouch(1, 100, 100);
      const touch2 = createTouch(2, 200, 200);
      
      element.dispatchEvent(createTouchEvent('touchstart', [touch1, touch2]));
      
      // First move to start pinch
      const moveTouch1a = createTouch(1, 90, 90);
      const moveTouch2a = createTouch(2, 210, 210);
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch1a, moveTouch2a]));
      
      // Second move
      const moveTouch1b = createTouch(1, 80, 80);
      const moveTouch2b = createTouch(2, 220, 220);
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch1b, moveTouch2b]));
      
      expect(mockCallbacks.onPinchMove).toHaveBeenCalled();
    });

    it('should detect pinch end', () => {
      const touch1 = createTouch(1, 100, 100);
      const touch2 = createTouch(2, 200, 200);
      
      element.dispatchEvent(createTouchEvent('touchstart', [touch1, touch2]));
      
      // Move to start pinch
      const moveTouch1 = createTouch(1, 90, 90);
      const moveTouch2 = createTouch(2, 210, 210);
      element.dispatchEvent(createTouchEvent('touchmove', [moveTouch1, moveTouch2]));
      
      // End one finger
      element.dispatchEvent(createTouchEvent('touchend', [moveTouch2], [moveTouch1]));
      
      expect(mockCallbacks.onPinchEnd).toHaveBeenCalled();
    });
  });

  describe('Swipe Gestures', () => {

    it('should detect horizontal swipe', () => {
      const startTouch = createTouch(1, 100, 100);
      const endTouch = createTouch(1, 200, 100); // Horizontal swipe
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchend', [], [endTouch]));
      
      expect(mockCallbacks.onSwipe).toHaveBeenCalled();
      
      const swipeEvent = mockCallbacks.onSwipe.mock.calls[0][0];
      expect(swipeEvent.direction).toBe('right');
    });

    it('should detect vertical swipe', () => {
      const startTouch = createTouch(1, 100, 100);
      const endTouch = createTouch(1, 100, 200); // Vertical swipe
      
      element.dispatchEvent(createTouchEvent('touchstart', [startTouch]));
      element.dispatchEvent(createTouchEvent('touchend', [], [endTouch]));
      
      expect(mockCallbacks.onSwipe).toHaveBeenCalled();
      
      const swipeEvent = mockCallbacks.onSwipe.mock.calls[0][0];
      expect(swipeEvent.direction).toBe('down');
    });
  });

  describe('Configuration Updates', () => {
    it('should update config', () => {
      gestureHandler.updateConfig({
        tapThreshold: 20,
        enableHapticFeedback: false,
      });
      
      // Config should be updated (internal state)
      expect(gestureHandler).toBeDefined();
    });

    it('should update callbacks', () => {
      const newCallbacks = {
        onTap: vi.fn(),
      };
      
      gestureHandler.updateCallbacks(newCallbacks);
      
      // Callbacks should be updated (internal state)
      expect(gestureHandler).toBeDefined();
    });
  });

  describe('Haptic Feedback', () => {
    it('should trigger haptic feedback for tap', () => {
      const touch = createTouch(1, 100, 100);
      
      const startEvent = createTouchEvent('touchstart', [touch]);
      const endEvent = createTouchEvent('touchend', [], [touch]);
      
      element.dispatchEvent(startEvent);
      element.dispatchEvent(endEvent);
      
      expect(navigator.vibrate).toHaveBeenCalledWith([10]);
    });

    it('should not trigger haptic feedback when disabled', () => {
      gestureHandler.destroy();
      gestureHandler = new TouchGestureHandler(element, mockCallbacks, {
        enableHapticFeedback: false,
      });
      
      const touch = createTouch(1, 100, 100);
      
      const startEvent = createTouchEvent('touchstart', [touch]);
      const endEvent = createTouchEvent('touchend', [], [touch]);
      
      element.dispatchEvent(startEvent);
      element.dispatchEvent(endEvent);
      
      expect(navigator.vibrate).not.toHaveBeenCalled();
    });
  });

  describe('Touch Cancel', () => {
    it('should handle touch cancel event', () => {
      const touch = createTouch(1, 100, 100);
      
      element.dispatchEvent(createTouchEvent('touchstart', [touch]));
      element.dispatchEvent(createTouchEvent('touchcancel', [touch]));
      
      // Should not crash and should clear gesture state
      expect(gestureHandler).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    it('should handle missing touch properties gracefully', () => {
      const invalidEvent = new Event('touchstart') as TouchEvent;
      invalidEvent.changedTouches = [{}]; // Missing required properties
      invalidEvent.preventDefault = vi.fn();
      
      // Should not crash
      expect(() => {
        element.dispatchEvent(invalidEvent);
      }).not.toThrow();
    });

    it('should handle callback errors gracefully', () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Callback error');
      });
      
      gestureHandler.updateCallbacks({ onTap: errorCallback });
      
      const touch = createTouch(1, 100, 100);
      
      // Should not crash when callback throws
      expect(() => {
        element.dispatchEvent(createTouchEvent('touchstart', [touch]));
        element.dispatchEvent(createTouchEvent('touchend', [], [touch]));
      }).not.toThrow();
    });
  });

  describe('Cleanup', () => {
    it('should remove event listeners on destroy', () => {
      const removeEventListenerSpy = vi.spyOn(element, 'removeEventListener');
      
      gestureHandler.destroy();
      
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchstart', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchmove', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchend', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('touchcancel', expect.any(Function));
    });

    it('should clear timers on destroy', () => {
      gestureHandler.destroy();
      
      expect(global.clearTimeout).toHaveBeenCalled();
    });
  });
});
