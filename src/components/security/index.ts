// Security Components
export { default as DigitalWatermark, WatermarkConfigurator, WatermarkManager } from './digital-watermark';
export { default as AccessControlManager } from './access-control';

// Security Utilities
export { DocumentEncryption } from './document-encryption';
export { auditLogger, AuditLogger } from './audit-logger';

// Convenience audit functions
export {
  logDocumentOpen,
  logDocumentView,
  logDocumentPrint,
  logAccessDenied,
  logAnnotationAdd,
  logPermissionChange
} from './audit-logger';

// Re-export types for convenience
export type {
  EncryptionOptions,
  EncryptedDocument,
  AccessControl,
  DocumentPermission
} from './document-encryption';

export type {
  WatermarkConfig,
  WatermarkMetadata
} from './digital-watermark';

export type {
  AuditEvent,
  AuditAction,
  AuditFilter,
  AuditSummary
} from './audit-logger';
