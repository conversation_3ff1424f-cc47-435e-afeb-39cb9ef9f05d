"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import {
  Shield,
  Clock,
  Globe,
  Eye,
  Download,
  Printer,
  Copy,
  Edit,
  Share,
  Plus,
  Trash2,
  CheckCircle,
  Calendar,
  MapPin
} from 'lucide-react';
import { toast } from 'sonner';
import type { AccessControl, DocumentPermission } from './document-encryption';

interface AccessControlManagerProps {
  documentId: string;
  currentAccessControl: AccessControl;
  onAccessControlChange: (accessControl: AccessControl) => void;
  className?: string;
}

interface PermissionTemplate {
  name: string;
  description: string;
  permissions: DocumentPermission[];
  allowPrint: boolean;
  allowCopy: boolean;
  allowDownload: boolean;
  allowAnnotate: boolean;
}

const PERMISSION_TEMPLATES: PermissionTemplate[] = [
  {
    name: 'View Only',
    description: 'Can only view the document',
    permissions: ['view'],
    allowPrint: false,
    allowCopy: false,
    allowDownload: false,
    allowAnnotate: false
  },
  {
    name: 'Standard User',
    description: 'Can view, print, and copy',
    permissions: ['view', 'print', 'copy'],
    allowPrint: true,
    allowCopy: true,
    allowDownload: false,
    allowAnnotate: false
  },
  {
    name: 'Collaborator',
    description: 'Can view, annotate, and share',
    permissions: ['view', 'annotate', 'share'],
    allowPrint: true,
    allowCopy: true,
    allowDownload: false,
    allowAnnotate: true
  },
  {
    name: 'Full Access',
    description: 'All permissions including download',
    permissions: ['view', 'print', 'copy', 'download', 'annotate', 'share'],
    allowPrint: true,
    allowCopy: true,
    allowDownload: true,
    allowAnnotate: true
  },
  {
    name: 'Administrator',
    description: 'Complete administrative access',
    permissions: ['view', 'print', 'copy', 'download', 'annotate', 'share', 'admin'],
    allowPrint: true,
    allowCopy: true,
    allowDownload: true,
    allowAnnotate: true
  }
];

export default function AccessControlManager({
  documentId,
  currentAccessControl,
  onAccessControlChange,
  className
}: AccessControlManagerProps) {
  const [activeTab, setActiveTab] = useState<'permissions' | 'restrictions' | 'sharing'>('permissions');
  const [newIpAddress, setNewIpAddress] = useState('');
  const [expirationDate, setExpirationDate] = useState('');
  const [expirationTime, setExpirationTime] = useState('');

  // Update access control
  const updateAccessControl = useCallback((updates: Partial<AccessControl>) => {
    const updated = { ...currentAccessControl, ...updates };
    onAccessControlChange(updated);
  }, [currentAccessControl, onAccessControlChange]);

  // Apply permission template
  const applyTemplate = useCallback((template: PermissionTemplate) => {
    updateAccessControl({
      permissions: template.permissions,
      allowPrint: template.allowPrint,
      allowCopy: template.allowCopy,
      allowDownload: template.allowDownload,
      allowAnnotate: template.allowAnnotate
    });
    toast.success(`Applied ${template.name} template`);
  }, [updateAccessControl]);

  // Toggle permission
  const togglePermission = useCallback((permission: DocumentPermission) => {
    const currentPermissions = currentAccessControl.permissions || [];
    const hasPermission = currentPermissions.includes(permission);
    
    const newPermissions = hasPermission
      ? currentPermissions.filter(p => p !== permission)
      : [...currentPermissions, permission];
    
    updateAccessControl({ permissions: newPermissions });
  }, [currentAccessControl.permissions, updateAccessControl]);

  // Add IP restriction
  const addIpRestriction = useCallback(() => {
    if (!newIpAddress.trim()) return;
    
    const currentRestrictions = currentAccessControl.ipRestrictions || [];
    if (currentRestrictions.includes(newIpAddress)) {
      toast.error('IP address already in restrictions');
      return;
    }
    
    updateAccessControl({
      ipRestrictions: [...currentRestrictions, newIpAddress.trim()]
    });
    setNewIpAddress('');
    toast.success('IP restriction added');
  }, [newIpAddress, currentAccessControl.ipRestrictions, updateAccessControl]);

  // Remove IP restriction
  const removeIpRestriction = useCallback((ipAddress: string) => {
    const currentRestrictions = currentAccessControl.ipRestrictions || [];
    updateAccessControl({
      ipRestrictions: currentRestrictions.filter(ip => ip !== ipAddress)
    });
    toast.success('IP restriction removed');
  }, [currentAccessControl.ipRestrictions, updateAccessControl]);

  // Set expiration
  const setExpiration = useCallback(() => {
    if (!expirationDate) {
      updateAccessControl({ expiresAt: undefined });
      return;
    }
    
    const dateTime = expirationTime 
      ? `${expirationDate}T${expirationTime}`
      : `${expirationDate}T23:59:59`;
    
    const expiresAt = new Date(dateTime).getTime();
    updateAccessControl({ expiresAt });
    toast.success('Expiration date set');
  }, [expirationDate, expirationTime, updateAccessControl]);

  // Format expiration date
  const formatExpirationDate = useCallback((timestamp?: number) => {
    if (!timestamp) return 'Never';
    return new Date(timestamp).toLocaleString();
  }, []);

  // Check if permission is enabled
  const hasPermission = useCallback((permission: DocumentPermission) => {
    return currentAccessControl.permissions?.includes(permission) || false;
  }, [currentAccessControl.permissions]);

  // Initialize expiration form fields
  useEffect(() => {
    if (currentAccessControl.expiresAt) {
      const date = new Date(currentAccessControl.expiresAt);
      setExpirationDate(date.toISOString().split('T')[0]);
      setExpirationTime(date.toTimeString().slice(0, 5));
    }
  }, [currentAccessControl.expiresAt]);

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center gap-2 mb-6">
        <Shield className="h-6 w-6" />
        <h2 className="text-xl font-semibold">Access Control</h2>
        <Badge variant="outline" className="ml-auto">
          Document: {documentId.substring(0, 8)}...
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'permissions' | 'restrictions' | 'sharing')}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="permissions">Permissions</TabsTrigger>
          <TabsTrigger value="restrictions">Restrictions</TabsTrigger>
          <TabsTrigger value="sharing">Sharing</TabsTrigger>
        </TabsList>

        <TabsContent value="permissions" className="space-y-6">
          {/* Permission Templates */}
          <div>
            <h3 className="text-lg font-medium mb-3">Quick Templates</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {PERMISSION_TEMPLATES.map((template) => (
                <Card
                  key={template.name}
                  className="p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => applyTemplate(template)}
                >
                  <h4 className="font-medium mb-1">{template.name}</h4>
                  <p className="text-sm text-muted-foreground mb-2">{template.description}</p>
                  <div className="flex flex-wrap gap-1">
                    {template.permissions.map((permission) => (
                      <Badge key={permission} variant="secondary" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </Card>
              ))}
            </div>
          </div>

          <Separator />

          {/* Individual Permissions */}
          <div>
            <h3 className="text-lg font-medium mb-3">Individual Permissions</h3>
            <div className="space-y-4">
              {/* Core Permissions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    <Label>View Document</Label>
                  </div>
                  <Switch
                    checked={hasPermission('view')}
                    onCheckedChange={() => togglePermission('view')}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Printer className="h-4 w-4" />
                    <Label>Print</Label>
                  </div>
                  <Switch
                    checked={currentAccessControl.allowPrint}
                    onCheckedChange={(checked) => updateAccessControl({ allowPrint: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Copy className="h-4 w-4" />
                    <Label>Copy Text</Label>
                  </div>
                  <Switch
                    checked={currentAccessControl.allowCopy}
                    onCheckedChange={(checked) => updateAccessControl({ allowCopy: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    <Label>Download</Label>
                  </div>
                  <Switch
                    checked={currentAccessControl.allowDownload}
                    onCheckedChange={(checked) => updateAccessControl({ allowDownload: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    <Label>Annotate</Label>
                  </div>
                  <Switch
                    checked={currentAccessControl.allowAnnotate}
                    onCheckedChange={(checked) => updateAccessControl({ allowAnnotate: checked })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Share className="h-4 w-4" />
                    <Label>Share</Label>
                  </div>
                  <Switch
                    checked={hasPermission('share')}
                    onCheckedChange={() => togglePermission('share')}
                  />
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Usage Limits */}
          <div>
            <h3 className="text-lg font-medium mb-3">Usage Limits</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="maxViews">Maximum Views</Label>
                <Input
                  id="maxViews"
                  type="number"
                  placeholder="Unlimited"
                  value={currentAccessControl.maxViews || ''}
                  onChange={(e) => updateAccessControl({ 
                    maxViews: e.target.value ? parseInt(e.target.value) : undefined 
                  })}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Current views: {currentAccessControl.currentViews}
                </p>
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="restrictions" className="space-y-6">
          {/* Expiration */}
          <div>
            <h3 className="text-lg font-medium mb-3">Expiration</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="expirationDate">Date</Label>
                <Input
                  id="expirationDate"
                  type="date"
                  value={expirationDate}
                  onChange={(e) => setExpirationDate(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="expirationTime">Time</Label>
                <Input
                  id="expirationTime"
                  type="time"
                  value={expirationTime}
                  onChange={(e) => setExpirationTime(e.target.value)}
                />
              </div>
              <div className="flex items-end">
                <Button onClick={setExpiration} className="w-full">
                  <Calendar className="h-4 w-4 mr-2" />
                  Set Expiration
                </Button>
              </div>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Current expiration: {formatExpirationDate(currentAccessControl.expiresAt)}
            </p>
          </div>

          <Separator />

          {/* IP Restrictions */}
          <div>
            <h3 className="text-lg font-medium mb-3">IP Address Restrictions</h3>
            <div className="flex gap-2 mb-4">
              <Input
                placeholder="Enter IP address (e.g., ************* or 192.168.1.*)"
                value={newIpAddress}
                onChange={(e) => setNewIpAddress(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addIpRestriction()}
              />
              <Button onClick={addIpRestriction}>
                <Plus className="h-4 w-4 mr-2" />
                Add
              </Button>
            </div>
            
            {currentAccessControl.ipRestrictions && currentAccessControl.ipRestrictions.length > 0 ? (
              <div className="space-y-2">
                {currentAccessControl.ipRestrictions.map((ip, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-muted rounded">
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span className="font-mono">{ip}</span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeIpRestriction(ip)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No IP restrictions set. Access allowed from any IP.</p>
            )}
          </div>

          <Separator />

          {/* Time Restrictions */}
          <div>
            <h3 className="text-lg font-medium mb-3">Time Restrictions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="startTime">Start Time</Label>
                <Input
                  id="startTime"
                  type="time"
                  value={currentAccessControl.timeRestrictions?.startTime || ''}
                  onChange={(e) => updateAccessControl({
                    timeRestrictions: {
                      ...currentAccessControl.timeRestrictions,
                      startTime: e.target.value,
                      endTime: currentAccessControl.timeRestrictions?.endTime || '23:59',
                      timezone: currentAccessControl.timeRestrictions?.timezone || 'UTC',
                      daysOfWeek: currentAccessControl.timeRestrictions?.daysOfWeek || [0,1,2,3,4,5,6]
                    }
                  })}
                />
              </div>
              <div>
                <Label htmlFor="endTime">End Time</Label>
                <Input
                  id="endTime"
                  type="time"
                  value={currentAccessControl.timeRestrictions?.endTime || ''}
                  onChange={(e) => updateAccessControl({
                    timeRestrictions: {
                      ...currentAccessControl.timeRestrictions,
                      startTime: currentAccessControl.timeRestrictions?.startTime || '00:00',
                      endTime: e.target.value,
                      timezone: currentAccessControl.timeRestrictions?.timezone || 'UTC',
                      daysOfWeek: currentAccessControl.timeRestrictions?.daysOfWeek || [0,1,2,3,4,5,6]
                    }
                  })}
                />
              </div>
            </div>
            
            {/* Days of week */}
            <div className="mt-4">
              <Label>Allowed Days</Label>
              <div className="flex gap-2 mt-2">
                {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
                  <Button
                    key={day}
                    variant={currentAccessControl.timeRestrictions?.daysOfWeek?.includes(index) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      const currentDays = currentAccessControl.timeRestrictions?.daysOfWeek || [];
                      const newDays = currentDays.includes(index)
                        ? currentDays.filter(d => d !== index)
                        : [...currentDays, index];
                      
                      updateAccessControl({
                        timeRestrictions: {
                          ...currentAccessControl.timeRestrictions,
                          startTime: currentAccessControl.timeRestrictions?.startTime || '00:00',
                          endTime: currentAccessControl.timeRestrictions?.endTime || '23:59',
                          timezone: currentAccessControl.timeRestrictions?.timezone || 'UTC',
                          daysOfWeek: newDays
                        }
                      });
                    }}
                  >
                    {day}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="sharing" className="space-y-6">
          {/* User Access */}
          <div>
            <h3 className="text-lg font-medium mb-3">User Access</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="userId">User ID</Label>
                <Input
                  id="userId"
                  placeholder="Optional - restrict to specific user"
                  value={currentAccessControl.userId || ''}
                  onChange={(e) => updateAccessControl({ userId: e.target.value || undefined })}
                />
              </div>
              <div>
                <Label htmlFor="userEmail">User Email</Label>
                <Input
                  id="userEmail"
                  type="email"
                  placeholder="Optional - restrict to specific email"
                  value={currentAccessControl.userEmail || ''}
                  onChange={(e) => updateAccessControl({ userEmail: e.target.value || undefined })}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Access Summary */}
          <div>
            <h3 className="text-lg font-medium mb-3">Access Summary</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">
                  Permissions: {currentAccessControl.permissions?.join(', ') || 'None'}
                </span>
              </div>
              
              {currentAccessControl.expiresAt && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  <span className="text-sm">
                    Expires: {formatExpirationDate(currentAccessControl.expiresAt)}
                  </span>
                </div>
              )}
              
              {currentAccessControl.maxViews && (
                <div className="flex items-center gap-2">
                  <Eye className="h-4 w-4 text-blue-500" />
                  <span className="text-sm">
                    Views: {currentAccessControl.currentViews} / {currentAccessControl.maxViews}
                  </span>
                </div>
              )}
              
              {currentAccessControl.ipRestrictions && currentAccessControl.ipRestrictions.length > 0 && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-purple-500" />
                  <span className="text-sm">
                    IP Restrictions: {currentAccessControl.ipRestrictions.length} addresses
                  </span>
                </div>
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </Card>
  );
}
