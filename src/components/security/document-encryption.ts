import CryptoJS from 'crypto-js';

export interface EncryptionOptions {
  algorithm: 'AES' | 'DES' | 'TripleDES';
  keySize: 128 | 192 | 256;
  mode: 'CBC' | 'ECB' | 'CFB' | 'OFB' | 'CTR';
  padding: 'Pkcs7' | 'AnsiX923' | 'Iso10126' | 'Iso97971' | 'ZeroPadding' | 'NoPadding';
}

export interface EncryptedDocument {
  encryptedData: string;
  salt: string;
  iv: string;
  algorithm: string;
  keySize: number;
  mode: string;
  padding: string;
  timestamp: number;
  checksum: string;
}

export interface AccessControl {
  userId?: string;
  userEmail?: string;
  permissions: DocumentPermission[];
  expiresAt?: number;
  maxViews?: number;
  currentViews: number;
  allowPrint: boolean;
  allowCopy: boolean;
  allowDownload: boolean;
  allowAnnotate: boolean;
  ipRestrictions?: string[];
  timeRestrictions?: {
    startTime: string; // HH:MM format
    endTime: string;   // HH:MM format
    timezone: string;
    daysOfWeek: number[]; // 0-6, Sunday = 0
  };
}

export type DocumentPermission = 'view' | 'print' | 'copy' | 'download' | 'annotate' | 'share' | 'admin';

export class DocumentEncryption {
  private static readonly DEFAULT_OPTIONS: EncryptionOptions = {
    algorithm: 'AES',
    keySize: 256,
    mode: 'CBC',
    padding: 'Pkcs7'
  };

  /**
   * Generate a secure random password
   */
  static generatePassword(length: number = 16): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  /**
   * Generate a secure key from password using PBKDF2
   */
  static deriveKey(password: string, salt: string, keySize: number = 256): CryptoJS.lib.WordArray {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: keySize / 32,
      iterations: 10000
    });
  }

  /**
   * Encrypt document data
   */
  static async encryptDocument(
    data: ArrayBuffer | string,
    password: string,
    options: Partial<EncryptionOptions> = {}
  ): Promise<EncryptedDocument> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    
    // Convert data to string if it's ArrayBuffer
    const dataString = typeof data === 'string' ? data : this.arrayBufferToBase64(data);
    
    // Generate random salt and IV
    const salt = CryptoJS.lib.WordArray.random(128 / 8);
    const iv = CryptoJS.lib.WordArray.random(128 / 8);
    
    // Derive key from password
    const key = this.deriveKey(password, salt.toString(), opts.keySize);
    
    // Encrypt data
    const encrypted = CryptoJS.AES.encrypt(dataString, key, {
      iv: iv,
      mode: CryptoJS.mode[opts.mode],
      padding: CryptoJS.pad[opts.padding]
    });
    
    // Calculate checksum for integrity verification
    const checksum = CryptoJS.SHA256(dataString).toString();
    
    return {
      encryptedData: encrypted.toString(),
      salt: salt.toString(),
      iv: iv.toString(),
      algorithm: opts.algorithm,
      keySize: opts.keySize,
      mode: opts.mode,
      padding: opts.padding,
      timestamp: Date.now(),
      checksum
    };
  }

  /**
   * Decrypt document data
   */
  static async decryptDocument(
    encryptedDoc: EncryptedDocument,
    password: string
  ): Promise<string> {
    // Derive key from password using stored salt
    const key = this.deriveKey(password, encryptedDoc.salt, encryptedDoc.keySize);
    
    // Decrypt data
    const decrypted = CryptoJS.AES.decrypt(encryptedDoc.encryptedData, key, {
      iv: CryptoJS.enc.Hex.parse(encryptedDoc.iv),
      mode: CryptoJS.mode[encryptedDoc.mode as keyof typeof CryptoJS.mode],
      padding: CryptoJS.pad[encryptedDoc.padding as keyof typeof CryptoJS.pad]
    });
    
    const decryptedString = decrypted.toString(CryptoJS.enc.Utf8);
    
    if (!decryptedString) {
      throw new Error('Failed to decrypt document. Invalid password or corrupted data.');
    }
    
    // Verify integrity
    const checksum = CryptoJS.SHA256(decryptedString).toString();
    if (checksum !== encryptedDoc.checksum) {
      throw new Error('Document integrity check failed. Data may be corrupted.');
    }
    
    return decryptedString;
  }

  /**
   * Validate access control permissions
   */
  static validateAccess(
    accessControl: AccessControl,
    requestedPermission: DocumentPermission,
    userContext: {
      userId?: string;
      userEmail?: string;
      ipAddress?: string;
      currentTime?: Date;
    } = {}
  ): { allowed: boolean; reason?: string } {
    const now = userContext.currentTime || new Date();

    // Check if document has expired
    if (accessControl.expiresAt && now.getTime() > accessControl.expiresAt) {
      return { allowed: false, reason: 'Document access has expired' };
    }

    // Check view limit
    if (accessControl.maxViews && accessControl.currentViews >= accessControl.maxViews) {
      return { allowed: false, reason: 'Maximum view limit reached' };
    }

    // Check user permissions
    if (!accessControl.permissions.includes(requestedPermission) && !accessControl.permissions.includes('admin')) {
      return { allowed: false, reason: `Permission '${requestedPermission}' not granted` };
    }

    // Check user identity
    if (accessControl.userId && userContext.userId !== accessControl.userId) {
      return { allowed: false, reason: 'User not authorized' };
    }

    if (accessControl.userEmail && userContext.userEmail !== accessControl.userEmail) {
      return { allowed: false, reason: 'Email not authorized' };
    }

    // Check IP restrictions
    if (accessControl.ipRestrictions && userContext.ipAddress) {
      const isIpAllowed = accessControl.ipRestrictions.some(allowedIp => {
        return this.matchIpPattern(userContext.ipAddress!, allowedIp);
      });
      
      if (!isIpAllowed) {
        return { allowed: false, reason: 'IP address not authorized' };
      }
    }

    // Check time restrictions
    if (accessControl.timeRestrictions) {
      const timeAllowed = this.isTimeAllowed(now, accessControl.timeRestrictions);
      if (!timeAllowed) {
        return { allowed: false, reason: 'Access not allowed at this time' };
      }
    }

    // Check specific permission restrictions
    switch (requestedPermission) {
      case 'print':
        if (!accessControl.allowPrint) {
          return { allowed: false, reason: 'Printing not allowed' };
        }
        break;
      case 'copy':
        if (!accessControl.allowCopy) {
          return { allowed: false, reason: 'Copying not allowed' };
        }
        break;
      case 'download':
        if (!accessControl.allowDownload) {
          return { allowed: false, reason: 'Download not allowed' };
        }
        break;
      case 'annotate':
        if (!accessControl.allowAnnotate) {
          return { allowed: false, reason: 'Annotation not allowed' };
        }
        break;
    }

    return { allowed: true };
  }

  /**
   * Update access control after successful access
   */
  static updateAccessControl(accessControl: AccessControl, permission: DocumentPermission): AccessControl {
    const updated = { ...accessControl };
    
    if (permission === 'view') {
      updated.currentViews += 1;
    }
    
    return updated;
  }

  /**
   * Generate secure sharing token
   */
  static generateSharingToken(
    documentId: string,
    accessControl: AccessControl,
    expiresIn: number = 24 * 60 * 60 * 1000 // 24 hours
  ): string {
    const payload = {
      documentId,
      accessControl,
      expiresAt: Date.now() + expiresIn,
      tokenId: CryptoJS.lib.WordArray.random(128 / 8).toString()
    };
    
    const token = CryptoJS.AES.encrypt(JSON.stringify(payload), process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key').toString();
    return Buffer.from(token).toString('base64url');
  }

  /**
   * Validate sharing token
   */
  static validateSharingToken(token: string): { valid: boolean; payload?: any; reason?: string } {
    try {
      const encryptedPayload = Buffer.from(token, 'base64url').toString();
      const decrypted = CryptoJS.AES.decrypt(encryptedPayload, process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'default-key');
      const payload = JSON.parse(decrypted.toString(CryptoJS.enc.Utf8));
      
      if (Date.now() > payload.expiresAt) {
        return { valid: false, reason: 'Token has expired' };
      }
      
      return { valid: true, payload };
    } catch {
      return { valid: false, reason: 'Invalid token format' };
    }
  }

  /**
   * Helper: Convert ArrayBuffer to Base64
   */
  private static arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer);
    let binary = '';
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i]);
    }
    return btoa(binary);
  }

  /**
   * Helper: Match IP address against pattern (supports wildcards and CIDR)
   */
  private static matchIpPattern(ip: string, pattern: string): boolean {
    if (pattern === '*') return true;
    if (pattern === ip) return true;
    
    // Simple wildcard matching (e.g., 192.168.1.*)
    if (pattern.includes('*')) {
      const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$');
      return regex.test(ip);
    }
    
    // CIDR notation support would go here
    // For now, just exact match
    return false;
  }

  /**
   * Helper: Check if current time is within allowed time restrictions
   */
  private static isTimeAllowed(
    currentTime: Date,
    restrictions: AccessControl['timeRestrictions']
  ): boolean {
    if (!restrictions) return true;
    
    const dayOfWeek = currentTime.getDay();
    if (!restrictions.daysOfWeek.includes(dayOfWeek)) {
      return false;
    }
    
    const currentTimeStr = currentTime.toTimeString().substring(0, 5); // HH:MM
    
    return currentTimeStr >= restrictions.startTime && currentTimeStr <= restrictions.endTime;
  }
}
