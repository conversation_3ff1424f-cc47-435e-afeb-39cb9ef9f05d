"use client";

import Re<PERSON>, { useState, use<PERSON><PERSON>back, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

import { ScrollArea } from "@/components/ui/scroll-area";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import {
  FileText,
  Download,
  Play,
  Square,

  Eye,
  Zap,
  AlertCircle,
  Trash2,
  <PERSON>f<PERSON><PERSON><PERSON>,
  <PERSON>,
  Copy,
} from "lucide-react";
import { useOCR } from "@/hooks/use-ocr";
import type { OCRResult, ImagePreprocessingOptions } from "@/lib/ocr/tesseract-engine";
import type { OCRDocumentResult } from "@/lib/ocr/ocr-manager";
import { cn } from "@/lib/utils";

interface EnhancedOCREngineProps {
  pdfDocument: unknown;
  numPages: number;
  currentPage?: number;
  onTextExtracted?: (results: OCRResult[]) => void;
  onOCRResult?: (result: OCRDocumentResult) => void;
  className?: string;
  documentId?: string;
  enableAdvancedSettings?: boolean;
  enableBatchProcessing?: boolean;
  enablePreprocessing?: boolean;
}

const SUPPORTED_LANGUAGES = [
  { code: 'eng', name: 'English' },
  { code: 'spa', name: 'Spanish' },
  { code: 'fra', name: 'French' },
  { code: 'deu', name: 'German' },
  { code: 'ita', name: 'Italian' },
  { code: 'por', name: 'Portuguese' },
  { code: 'rus', name: 'Russian' },
  { code: 'jpn', name: 'Japanese' },
  { code: 'chi_sim', name: 'Chinese (Simplified)' },
  { code: 'chi_tra', name: 'Chinese (Traditional)' },
  { code: 'kor', name: 'Korean' },
  { code: 'ara', name: 'Arabic' },
  { code: 'hin', name: 'Hindi' },
];

export default function EnhancedOCREngine({
  pdfDocument,
  numPages,
  currentPage = 1,
  onTextExtracted,
  onOCRResult,
  className,
  documentId = 'unknown',
  enableAdvancedSettings = true,
  enableBatchProcessing = true,
  enablePreprocessing = true,
}: EnhancedOCREngineProps) {
  // OCR hook
  const {
    isInitialized,
    isInitializing,
    isProcessing,
    progress,
    results,
    error,
    stats,
    initialize,
    processDocument,
    processPage,
    getDocumentResult,
    getPageResult,
    searchOCRText,
    clearCache,
    cancelJob,
    isReady,
    hasError,
    clearError,
  } = useOCR({
    autoInitialize: true,
    enableCaching: true,
    maxWorkers: 2,
    defaultLanguage: 'eng',
    enableBackgroundProcessing: true,
  });

  // Component state
  const [selectedLanguage, setSelectedLanguage] = useState('eng');
  const [selectedPages, setSelectedPages] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(true);
  const [backgroundProcessing, setBackgroundProcessing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Array<{ pageNumber: number; matches: Array<{ text: string; confidence: number }> }>>([]);

  // Preprocessing options
  const [preprocessingOptions, setPreprocessingOptions] = useState<ImagePreprocessingOptions>({
    enhanceContrast: false,
    denoiseImage: false,
    sharpenImage: false,
    adjustBrightness: 0,
    adjustContrast: 0,
    scaleImage: 2.0, // Higher scale for better OCR
  });

  // Initialize selected pages
  useEffect(() => {
    if (selectAll) {
      setSelectedPages(Array.from({ length: numPages }, (_, i) => i + 1));
    }
  }, [numPages, selectAll]);

  // Handle page selection
  const togglePageSelection = useCallback((pageNum: number) => {
    setSelectedPages(prev => 
      prev.includes(pageNum)
        ? prev.filter(p => p !== pageNum)
        : [...prev, pageNum]
    );
    setSelectAll(false);
  }, []);

  // Handle select all toggle
  const handleSelectAllToggle = useCallback((checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedPages(Array.from({ length: numPages }, (_, i) => i + 1));
    } else {
      setSelectedPages([]);
    }
  }, [numPages]);

  // Start OCR processing
  const startOCRProcessing = useCallback(async () => {
    if (!isReady || !pdfDocument) {
      toast.error('OCR engine not ready');
      return;
    }

    const pagesToProcess = selectAll ? Array.from({ length: numPages }, (_, i) => i + 1) : selectedPages;
    
    if (pagesToProcess.length === 0) {
      toast.error('No pages selected for OCR processing');
      return;
    }

    try {
      const result = await processDocument(pdfDocument, {
        documentId,
        pages: pagesToProcess,
        language: selectedLanguage,
        preprocessingOptions: enablePreprocessing ? preprocessingOptions : undefined,
        priority: 'normal',
        backgroundProcessing,
      });

      if (result) {
        onOCRResult?.(result);
        
        // Extract results for callback
        const ocrResults = Array.from(result.results.values());
        onTextExtracted?.(ocrResults);

        toast.success(`OCR completed: ${result.processedPages}/${result.totalPages} pages processed`);
      }
    } catch (error) {
      toast.error(`OCR failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [
    isReady,
    pdfDocument,
    selectAll,
    numPages,
    selectedPages,
    documentId,
    selectedLanguage,
    enablePreprocessing,
    preprocessingOptions,
    backgroundProcessing,
    processDocument,
    onOCRResult,
    onTextExtracted,
  ]);

  // Process single page
  const processSinglePage = useCallback(async (pageNumber: number) => {
    if (!isReady || !pdfDocument) {
      toast.error('OCR engine not ready');
      return;
    }

    try {
      const result = await processPage(pdfDocument, pageNumber, {
        documentId,
        language: selectedLanguage,
        preprocessingOptions: enablePreprocessing ? preprocessingOptions : undefined,
      });

      if (result) {
        onTextExtracted?.([result]);
        toast.success(`Page ${pageNumber} processed successfully`);
      }
    } catch (error) {
      toast.error(`Failed to process page ${pageNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [
    isReady,
    pdfDocument,
    documentId,
    selectedLanguage,
    enablePreprocessing,
    preprocessingOptions,
    processPage,
    onTextExtracted,
  ]);

  // Search OCR text
  const handleSearch = useCallback(() => {
    if (!searchQuery.trim()) {
      setSearchResults([]);
      return;
    }

    const results = searchOCRText(documentId, searchQuery, {
      caseSensitive: false,
      wholeWords: false,
      useRegex: false,
    });

    setSearchResults(results);
    
    if (results.length === 0) {
      toast.info('No matches found');
    } else {
      toast.success(`Found ${results.reduce((sum, r) => sum + r.matches.length, 0)} matches across ${results.length} pages`);
    }
  }, [searchQuery, documentId, searchOCRText]);

  // Export results
  const exportResults = useCallback((format: 'txt' | 'json' | 'csv') => {
    const documentResult = getDocumentResult(documentId);
    if (!documentResult || documentResult.results.size === 0) {
      toast.error('No OCR results to export');
      return;
    }

    const results = Array.from(documentResult.results.values());
    let content = '';
    let filename = '';
    let mimeType = '';

    switch (format) {
      case 'txt':
        content = results
          .sort((a, b) => a.pageNumber - b.pageNumber)
          .map(result => `=== Page ${result.pageNumber} ===\n${result.text}\n\n`)
          .join('');
        filename = `ocr-results-${documentId}.txt`;
        mimeType = 'text/plain';
        break;

      case 'json':
        content = JSON.stringify(results, null, 2);
        filename = `ocr-results-${documentId}.json`;
        mimeType = 'application/json';
        break;

      case 'csv':
        const csvRows = [
          ['Page', 'Text', 'Confidence', 'Language', 'Processing Time'],
          ...results.map(result => [
            result.pageNumber.toString(),
            `"${result.text.replace(/"/g, '""')}"`,
            result.confidence.toFixed(2),
            result.language,
            result.processingTime.toString(),
          ]),
        ];
        content = csvRows.map(row => row.join(',')).join('\n');
        filename = `ocr-results-${documentId}.csv`;
        mimeType = 'text/csv';
        break;
    }

    // Download file
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    toast.success(`Results exported as ${format.toUpperCase()}`);
  }, [documentId, getDocumentResult]);

  // Copy text to clipboard
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Text copied to clipboard');
    } catch {
      toast.error('Failed to copy text');
    }
  }, []);

  const documentResult = getDocumentResult(documentId);
  const hasResults = documentResult && documentResult.results.size > 0;

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Enhanced OCR Engine
              </CardTitle>
              <CardDescription>
                Extract text from scanned PDFs and images using advanced OCR technology
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isReady ? "default" : "secondary"}>
                {isInitializing ? 'Initializing...' : isReady ? 'Ready' : 'Not Ready'}
              </Badge>
              {stats.activeJobs > 0 && (
                <Badge variant="outline">
                  {stats.activeJobs} active jobs
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Error Display */}
      {hasError && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                <RefreshCw className="h-3 w-3" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Display */}
      {(isInitializing || isProcessing) && progress && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>{progress.message}</span>
                <span>{Math.round(progress.progress)}%</span>
              </div>
              <Progress value={progress.progress} className="h-2" />
              {progress.pageNumber && progress.totalPages && (
                <div className="text-xs text-muted-foreground">
                  Page {progress.pageNumber} of {progress.totalPages}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Interface */}
      <Tabs defaultValue="process" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="process">Process</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
          <TabsTrigger value="search">Search</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="process" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">OCR Processing</CardTitle>
              <CardDescription>
                Configure and start OCR processing for selected pages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Language Selection */}
              <div className="space-y-2">
                <Label>Language</Label>
                <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {SUPPORTED_LANGUAGES.map(lang => (
                      <SelectItem key={lang.code} value={lang.code}>
                        {lang.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Page Selection */}
              {enableBatchProcessing && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Pages to Process</Label>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="select-all"
                        checked={selectAll}
                        onCheckedChange={handleSelectAllToggle}
                      />
                      <Label htmlFor="select-all" className="text-sm">
                        Select All ({numPages} pages)
                      </Label>
                    </div>
                  </div>

                  {!selectAll && (
                    <ScrollArea className="h-32 border rounded p-2">
                      <div className="grid grid-cols-8 gap-1">
                        {Array.from({ length: numPages }, (_, i) => i + 1).map(pageNum => (
                          <div key={pageNum} className="flex items-center space-x-1">
                            <Checkbox
                              id={`page-${pageNum}`}
                              checked={selectedPages.includes(pageNum)}
                              onCheckedChange={() => togglePageSelection(pageNum)}
                            />
                            <Label htmlFor={`page-${pageNum}`} className="text-xs">
                              {pageNum}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              )}

              {/* Processing Options */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="background-processing"
                    checked={backgroundProcessing}
                    onCheckedChange={setBackgroundProcessing}
                  />
                  <Label htmlFor="background-processing" className="text-sm">
                    Background Processing
                  </Label>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={startOCRProcessing}
                  disabled={!isReady || isProcessing || (enableBatchProcessing && selectedPages.length === 0)}
                  className="flex-1"
                >
                  <Play className="h-4 w-4 mr-2" />
                  {enableBatchProcessing ? `Process ${selectAll ? numPages : selectedPages.length} Pages` : 'Process Document'}
                </Button>

                {!enableBatchProcessing && (
                  <Button
                    onClick={() => processSinglePage(currentPage)}
                    disabled={!isReady || isProcessing}
                    variant="outline"
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Current Page
                  </Button>
                )}

                {isProcessing && (
                  <Button
                    onClick={() => cancelJob(documentId)}
                    variant="destructive"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {hasResults ? (
            <>
              {/* Results Summary */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">OCR Results</CardTitle>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportResults('txt')}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        TXT
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportResults('json')}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        JSON
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportResults('csv')}
                      >
                        <Download className="h-3 w-3 mr-1" />
                        CSV
                      </Button>
                    </div>
                  </div>
                  <CardDescription>
                    {documentResult?.processedPages} of {documentResult?.totalPages} pages processed
                    {documentResult?.metadata.averageConfidence && (
                      <> • Average confidence: {Math.round(documentResult.metadata.averageConfidence)}%</>
                    )}
                  </CardDescription>
                </CardHeader>
              </Card>

              {/* Results List */}
              <ScrollArea className="h-96">
                <div className="space-y-2">
                  {Array.from(documentResult?.results.values() || [])
                    .sort((a, b) => a.pageNumber - b.pageNumber)
                    .map(result => (
                      <Card key={result.id}>
                        <CardHeader className="pb-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline">Page {result.pageNumber}</Badge>
                              <Badge variant="secondary">
                                {Math.round(result.confidence)}% confidence
                              </Badge>
                              <Badge variant="outline">{result.language}</Badge>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(result.text)}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="text-sm text-muted-foreground line-clamp-3">
                            {result.text || 'No text extracted'}
                          </div>
                          <div className="mt-2 text-xs text-muted-foreground">
                            Processing time: {result.processingTime}ms • {result.words.length} words
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                </div>
              </ScrollArea>
            </>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">No OCR Results</h3>
                <p className="text-muted-foreground mb-4">
                  Process some pages to see OCR results here
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Search OCR Text</CardTitle>
              <CardDescription>
                Search through extracted text across all processed pages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  placeholder="Enter search query..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                />
                <Button onClick={handleSearch} disabled={!hasResults}>
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>

              {searchResults.length > 0 && (
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {searchResults.map(result => (
                      <Card key={result.pageNumber}>
                        <CardContent className="pt-4">
                          <div className="flex items-center justify-between mb-2">
                            <Badge variant="outline">Page {result.pageNumber}</Badge>
                            <Badge variant="secondary">{result.matches.length} matches</Badge>
                          </div>
                          <div className="space-y-1">
                            {result.matches.slice(0, 3).map((match, index) => (
                              <div key={index} className="text-sm">
                                <span className="font-medium">{match.text}</span>
                                <span className="text-muted-foreground ml-2">
                                  ({Math.round(match.confidence)}% confidence)
                                </span>
                              </div>
                            ))}
                            {result.matches.length > 3 && (
                              <div className="text-xs text-muted-foreground">
                                +{result.matches.length - 3} more matches
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          {enableAdvancedSettings && enablePreprocessing && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Image Preprocessing</CardTitle>
                <CardDescription>
                  Enhance image quality before OCR processing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="enhance-contrast"
                        checked={preprocessingOptions.enhanceContrast}
                        onCheckedChange={(checked) =>
                          setPreprocessingOptions(prev => ({ ...prev, enhanceContrast: checked }))
                        }
                      />
                      <Label htmlFor="enhance-contrast" className="text-sm">
                        Enhance Contrast
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="denoise-image"
                        checked={preprocessingOptions.denoiseImage}
                        onCheckedChange={(checked) =>
                          setPreprocessingOptions(prev => ({ ...prev, denoiseImage: checked }))
                        }
                      />
                      <Label htmlFor="denoise-image" className="text-sm">
                        Denoise Image
                      </Label>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Switch
                        id="sharpen-image"
                        checked={preprocessingOptions.sharpenImage}
                        onCheckedChange={(checked) =>
                          setPreprocessingOptions(prev => ({ ...prev, sharpenImage: checked }))
                        }
                      />
                      <Label htmlFor="sharpen-image" className="text-sm">
                        Sharpen Image
                      </Label>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-sm">
                        Brightness: {preprocessingOptions.adjustBrightness}
                      </Label>
                      <Slider
                        value={[preprocessingOptions.adjustBrightness || 0]}
                        onValueChange={([value]) =>
                          setPreprocessingOptions(prev => ({ ...prev, adjustBrightness: value }))
                        }
                        min={-100}
                        max={100}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">
                        Contrast: {preprocessingOptions.adjustContrast}
                      </Label>
                      <Slider
                        value={[preprocessingOptions.adjustContrast || 0]}
                        onValueChange={([value]) =>
                          setPreprocessingOptions(prev => ({ ...prev, adjustContrast: value }))
                        }
                        min={-100}
                        max={100}
                        step={5}
                        className="w-full"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm">
                        Scale: {preprocessingOptions.scaleImage}x
                      </Label>
                      <Slider
                        value={[preprocessingOptions.scaleImage || 1]}
                        onValueChange={([value]) =>
                          setPreprocessingOptions(prev => ({ ...prev, scaleImage: value }))
                        }
                        min={0.5}
                        max={4}
                        step={0.1}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Cache Management */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Cache Management</CardTitle>
              <CardDescription>
                Manage OCR result caching and storage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => clearCache(documentId)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear Document Cache
                </Button>
                <Button
                  variant="outline"
                  onClick={() => clearCache()}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Clear All Cache
                </Button>
              </div>

              <div className="text-sm text-muted-foreground">
                <p>Cache helps speed up repeated OCR operations by storing results locally.</p>
              </div>
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium">Active Jobs</div>
                  <div className="text-muted-foreground">{stats.activeJobs}</div>
                </div>
                <div>
                  <div className="font-medium">Queued Jobs</div>
                  <div className="text-muted-foreground">{stats.queuedJobs}</div>
                </div>
                <div>
                  <div className="font-medium">Total Processed</div>
                  <div className="text-muted-foreground">{stats.totalProcessed}</div>
                </div>
                <div>
                  <div className="font-medium">Avg Processing Time</div>
                  <div className="text-muted-foreground">{Math.round(stats.averageProcessingTime)}ms</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
