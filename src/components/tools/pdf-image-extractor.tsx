"use client";

import { useState, useCallback } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ImageIcon, Download, Eye, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { extractPDFDocument, type PDFDocument } from "@/lib/types/pdf";

export interface ExtractedImage {
  id: string;
  pageNumber: number;
  format: 'jpg' | 'png' | 'gif' | 'svg';
  width: number;
  height: number;
  size: number;
  dataUrl: string;
  boundingBox: { x: number; y: number; width: number; height: number };
  altText?: string;
  description?: string;
}

interface PDFImageExtractorProps {
  pdfDocument: PDFDocument;
  numPages: number;
  currentPage?: number;
  onImageExtracted?: (images: ExtractedImage[]) => void;
  className?: string;
  // Enhanced configuration options
  extractionMode?: 'all' | 'current' | 'range' | 'selection';
  imageFormats?: ('jpg' | 'png' | 'gif' | 'svg')[];
  minImageSize?: { width: number; height: number };
  maxImageSize?: { width: number; height: number };
  quality?: 'low' | 'medium' | 'high';
  enableMetadataExtraction?: boolean;
  batchSize?: number;
}

export default function PDFImageExtractor({
  pdfDocument,
  numPages,
  onImageExtracted,
}: PDFImageExtractorProps) {
  const [extractedImages, setExtractedImages] = useState<ExtractedImage[]>([]);
  const [isExtracting, setIsExtracting] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ExtractedImage | null>(
    null
  );

  const extractImages = useCallback(async () => {
    if (!pdfDocument) return;

    setIsExtracting(true);
    const images: ExtractedImage[] = [];

    try {
      // Extract the actual PDF document using the helper function
      const doc = extractPDFDocument(pdfDocument);

      if (!doc) {
        throw new Error("Invalid PDF document");
      }

      for (let pageNum = 1; pageNum <= numPages; pageNum++) {
        try {
          let page;
          if (typeof doc.getPage === "function") {
            page = await doc.getPage(pageNum);
          } else {
            // Skip if we can't access the page
            continue;
          }

          // Render the page to a canvas to capture images
          const viewport = page.getViewport({ scale: 1.0 });
          const canvas = document.createElement("canvas");
          const context = canvas.getContext("2d");

          if (context) {
            canvas.width = viewport.width;
            canvas.height = viewport.height;

            await page.render({
              canvasContext: context,
              viewport: viewport,
              canvas: canvas,
            }).promise;

            // Convert to image data
            const imageData = canvas.toDataURL("image/png");

            // Only add if the canvas actually contains image data
            if (imageData && imageData !== "data:,") {
              // Calculate approximate size from data URL
              const sizeInBytes = Math.round((imageData.length * 3) / 4);
              
              images.push({
                id: `page-${pageNum}-${Date.now()}`,
                pageNumber: pageNum,
                format: 'png',
                width: viewport.width,
                height: viewport.height,
                size: sizeInBytes,
                dataUrl: imageData,
                boundingBox: { x: 0, y: 0, width: viewport.width, height: viewport.height },
                altText: `Extracted image from page ${pageNum}`,
                description: `PDF page ${pageNum} rendered as image`
              });
            }
          }
        } catch (pageError) {
          console.warn(`Failed to process page ${pageNum}:`, pageError);
        }
      }

      setExtractedImages(images);
      onImageExtracted?.(images); // Enhanced callback for compatibility
      toast.success("Page rendering completed", {
        description: `Rendered ${images.length} pages as images.`,
      });
    } catch (error) {
      console.error("Image extraction error:", error);
      toast.error("Extraction failed. Failed to render pages from the PDF.");
    } finally {
      setIsExtracting(false);
    }
  }, [pdfDocument, numPages, onImageExtracted]);

  const downloadImage = (image: ExtractedImage) => {
    const link = document.createElement("a");
    link.href = image.dataUrl;
    link.download = `pdf-image-page-${image.pageNumber}-${image.id}.png`;
    link.click();
  };

  const downloadAllImages = () => {
    extractedImages.forEach((image, index) => {
      setTimeout(() => {
        downloadImage(image);
      }, index * 100); // Stagger downloads
    });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ImageIcon className="h-5 w-5" />
          PDF Images
        </CardTitle>
        <CardDescription>
          Extract and download images from the PDF document
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button
            onClick={extractImages}
            disabled={isExtracting || !pdfDocument}
            className="flex-1"
          >
            {isExtracting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Extracting...
              </>
            ) : (
              <>
                <ImageIcon className="h-4 w-4 mr-2" />
                Extract Images
              </>
            )}
          </Button>

          {extractedImages.length > 0 && (
            <Button variant="outline" onClick={downloadAllImages}>
              <Download className="h-4 w-4 mr-2" />
              Download All
            </Button>
          )}
        </div>

        {extractedImages.length > 0 && (
          <div>
            <p className="text-sm text-muted-foreground mb-3">
              Found {extractedImages.length} images
            </p>

            <ScrollArea className="h-64">
              <div className="grid grid-cols-2 gap-2">
                {extractedImages.map((image) => (
                  <div key={image.id} className="relative group">
                    <Image
                      src={image.dataUrl || "/placeholder.svg"}
                      alt={`Page ${image.pageNumber} Image`}
                      width={100}
                      height={80}
                      className="w-full h-20 object-cover rounded border cursor-pointer hover:opacity-80 transition-opacity"
                      onClick={() => setSelectedImage(image)}
                    />
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded flex items-center justify-center">
                      <div className="flex gap-1">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => setSelectedImage(image)}
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => downloadImage(image)}
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="absolute bottom-1 left-1 bg-black/70 text-white text-xs px-1 rounded">
                      Page {image.pageNumber}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Image Preview Dialog */}
        <Dialog
          open={!!selectedImage}
          onOpenChange={() => setSelectedImage(null)}
        >
          <DialogContent className="max-w-4xl max-h-[90vh]">
            <DialogHeader>
              <DialogTitle>
                Image from Page {selectedImage?.pageNumber}
              </DialogTitle>
            </DialogHeader>
            {selectedImage && (
              <div className="space-y-4">
                <div className="flex justify-center">
                  <Image
                    src={selectedImage.dataUrl || "/placeholder.svg"}
                    alt={`Page ${selectedImage.pageNumber} Image`}
                    width={600}
                    height={400}
                    className="max-w-full max-h-[60vh] object-contain"
                  />
                </div>
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>
                    {selectedImage.width} × {selectedImage.height} pixels
                  </span>
                  <Button
                    size="sm"
                    onClick={() => downloadImage(selectedImage)}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
