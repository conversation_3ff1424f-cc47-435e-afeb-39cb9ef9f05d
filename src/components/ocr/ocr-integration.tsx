"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Eye, 
  Zap, 
  FileText, 
  Search, 
  Settings, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Download,
  Copy,
} from 'lucide-react';
import { useOCR } from '@/hooks/use-ocr';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type { OCRResult } from '@/lib/ocr/tesseract-engine';

interface OCRIntegrationProps {
  pdfDocument: object;
  currentPage: number;
  numPages: number;
  documentId: string;
  onOCRTextExtracted?: (pageNumber: number, text: string, confidence: number) => void;

  enableAutoOCR?: boolean;
  enableSearchIntegration?: boolean;
  className?: string;
}

export default function OCRIntegration({
  pdfDocument,
  currentPage,
  numPages,
  documentId,
  onOCRTextExtracted,

  enableAutoOCR = false,
  enableSearchIntegration = true,
  className,
}: OCRIntegrationProps) {
  const [autoOCREnabled, setAutoOCREnabled] = useState(enableAutoOCR);
  const [processedPages, setProcessedPages] = useState<Set<number>>(new Set());
  const [ocrResults, setOCRResults] = useState<Map<number, OCRResult>>(new Map());
  const [showSettings, setShowSettings] = useState(false);

  const {
    isInitializing,
    isProcessing,
    progress,
    processPage,
    getPageResult,
    isReady,
    hasError,
    clearError,
  } = useOCR({
    autoInitialize: true,
    enableCaching: true,
    maxWorkers: 1, // Use single worker for integration
    defaultLanguage: 'eng',
  });

  // Process current page with OCR
  const processCurrentPage = useCallback(async () => {
    if (!isReady || !pdfDocument || processedPages.has(currentPage)) {
      return;
    }

    try {
      const result = await processPage(pdfDocument, currentPage, {
        documentId,
        language: 'eng',
      });

      if (result) {
        setProcessedPages(prev => new Set([...prev, currentPage]));
        setOCRResults(prev => new Map([...prev, [currentPage, result]]));
        
        // Notify parent components
        onOCRTextExtracted?.(currentPage, result.text, result.confidence);
        
        toast.success(`OCR completed for page ${currentPage}`);
      }
    } catch {
      toast.error(`OCR failed for page ${currentPage}`);
    }
  }, [isReady, pdfDocument, currentPage, processedPages, processPage, documentId, onOCRTextExtracted]);

  // Auto-process current page when enabled
  useEffect(() => {
    if (autoOCREnabled && isReady && !processedPages.has(currentPage)) {
      // Delay to avoid processing while user is rapidly navigating
      const timer = setTimeout(() => {
        processCurrentPage();
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [autoOCREnabled, isReady, currentPage, processedPages, processCurrentPage]);

  // Check for cached results on page change
  useEffect(() => {
    const checkCachedResult = async () => {
      if (!isReady) return;

      const cachedResult = getPageResult(documentId, currentPage);
      if (cachedResult && !processedPages.has(currentPage)) {
        setProcessedPages(prev => new Set([...prev, currentPage]));
        setOCRResults(prev => new Map([...prev, [currentPage, cachedResult]]));
        onOCRTextExtracted?.(currentPage, cachedResult.text, cachedResult.confidence);
      }
    };

    checkCachedResult();
  }, [currentPage, documentId, isReady, getPageResult, processedPages, onOCRTextExtracted]);

  // Copy OCR text to clipboard
  const copyOCRText = useCallback(async (pageNumber: number) => {
    const result = ocrResults.get(pageNumber);
    if (!result) return;

    try {
      await navigator.clipboard.writeText(result.text);
      toast.success('OCR text copied to clipboard');
    } catch {
      toast.error('Failed to copy text');
    }
  }, [ocrResults]);

  // Export OCR results
  const exportOCRResults = useCallback(() => {
    if (ocrResults.size === 0) {
      toast.error('No OCR results to export');
      return;
    }

    const results = Array.from(ocrResults.values()).sort((a, b) => a.pageNumber - b.pageNumber);
    const content = results
      .map(result => `=== Page ${result.pageNumber} ===\n${result.text}\n\n`)
      .join('');

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `ocr-results-${documentId}.txt`;
    link.click();
    URL.revokeObjectURL(url);

    toast.success('OCR results exported');
  }, [ocrResults, documentId]);

  const currentPageResult = ocrResults.get(currentPage);
  const hasCurrentPageResult = !!currentPageResult;
  const isCurrentPageProcessed = processedPages.has(currentPage);

  return (
    <div className={cn("space-y-3", className)}>
      {/* OCR Status Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4" />
              <CardTitle className="text-sm">OCR Integration</CardTitle>
              <Badge variant={isReady ? "default" : "secondary"} className="text-xs">
                {isInitializing ? 'Initializing...' : isReady ? 'Ready' : 'Not Ready'}
              </Badge>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>

        {showSettings && (
          <CardContent className="pt-0">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-ocr" className="text-sm">
                  Auto-process pages
                </Label>
                <Switch
                  id="auto-ocr"
                  checked={autoOCREnabled}
                  onCheckedChange={setAutoOCREnabled}
                  disabled={!isReady}
                />
              </div>
              <Separator />
              <div className="text-xs text-muted-foreground">
                <p>Auto-process: Automatically extract text when viewing new pages</p>
                <p>Manual: Click the OCR button to extract text from current page</p>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Error Display */}
      {hasError && (
        <Card className="border-destructive">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-3 w-3" />
              <span className="text-xs">{error}</span>
              <Button variant="ghost" size="sm" onClick={clearError}>
                Clear
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Progress Display */}
      {isProcessing && progress && (
        <Card>
          <CardContent className="pt-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs">
                <span>{progress.message}</span>
                <span>{Math.round(progress.progress)}%</span>
              </div>
              <Progress value={progress.progress} className="h-1" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current Page OCR */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Page {currentPage} OCR</CardTitle>
            <div className="flex items-center gap-1">
              {hasCurrentPageResult && (
                <Badge variant="outline" className="text-xs">
                  {Math.round(currentPageResult.confidence)}% confidence
                </Badge>
              )}
              {isCurrentPageProcessed ? (
                <CheckCircle className="h-3 w-3 text-green-500" />
              ) : (
                <div className="h-3 w-3 rounded-full bg-muted" />
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              size="sm"
              onClick={processCurrentPage}
              disabled={!isReady || isProcessing || isCurrentPageProcessed}
              className="flex-1"
            >
              {isProcessing ? (
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
              ) : (
                <Zap className="h-3 w-3 mr-1" />
              )}
              {isCurrentPageProcessed ? 'Processed' : 'Extract Text'}
            </Button>

            {hasCurrentPageResult && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => copyOCRText(currentPage)}
              >
                <Copy className="h-3 w-3" />
              </Button>
            )}
          </div>

          {/* OCR Result Display */}
          {hasCurrentPageResult && (
            <div className="space-y-2">
              <div className="text-xs text-muted-foreground">
                Extracted Text ({currentPageResult.words.length} words, {currentPageResult.processingTime}ms):
              </div>
              <div className="p-2 bg-muted rounded text-xs max-h-24 overflow-y-auto">
                {currentPageResult.text || 'No text found'}
              </div>
            </div>
          )}

          {/* No Result Message */}
          {!hasCurrentPageResult && !isProcessing && (
            <div className="text-center py-4">
              <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-xs text-muted-foreground">
                {autoOCREnabled 
                  ? 'OCR will run automatically when you navigate to new pages'
                  : 'Click "Extract Text" to process this page with OCR'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* OCR Summary */}
      {ocrResults.size > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">OCR Summary</CardTitle>
              <Button
                size="sm"
                variant="outline"
                onClick={exportOCRResults}
              >
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-medium">{ocrResults.size}</div>
                  <div className="text-muted-foreground">Pages Processed</div>
                </div>
                <div className="text-center">
                  <div className="font-medium">
                    {Math.round(
                      Array.from(ocrResults.values()).reduce((sum, r) => sum + r.confidence, 0) / ocrResults.size
                    )}%
                  </div>
                  <div className="text-muted-foreground">Avg Confidence</div>
                </div>
                <div className="text-center">
                  <div className="font-medium">
                    {Array.from(ocrResults.values()).reduce((sum, r) => sum + r.words.length, 0)}
                  </div>
                  <div className="text-muted-foreground">Total Words</div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span>Document Progress</span>
                  <span>{Math.round((ocrResults.size / numPages) * 100)}%</span>
                </div>
                <Progress value={(ocrResults.size / numPages) * 100} className="h-1" />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search Integration Hint */}
      {enableSearchIntegration && ocrResults.size > 0 && (
        <Card className="border-dashed">
          <CardContent className="pt-4">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Search className="h-3 w-3" />
              <span>
                OCR text is now searchable in the enhanced search interface
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
