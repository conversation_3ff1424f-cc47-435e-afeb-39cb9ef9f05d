"use client";

import React, {
  useState,
  use<PERSON><PERSON>back,
  useMemo,
  useRef,
  useEffect,
} from "react";
import {
  Card,
  CardContent,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import {
  Users,
  Share2,
  Clock,
  Send,
  UserPlus,
  MessageSquare,
  Check,
  Wifi,
  WifiOff,
  Star,
  Video,
  Settings,
  Bell,
  GitBranch,
} from "lucide-react";
import { toast } from "sonner";

export interface CollaborationUser {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: "owner" | "editor" | "viewer" | "reviewer";
  status: "online" | "offline" | "away";
  lastSeen?: Date;
  permissions: UserPermissions;
  presence?: UserPresence;
  preferences?: UserPreferences;
}

export interface UserPermissions {
  canView: boolean;
  canEdit: boolean;
  canComment: boolean;
  canShare: boolean;
  canDelete: boolean;
  canManageUsers: boolean;
  canExport: boolean;
  canPrint: boolean;
  canDownload?: boolean; // Legacy support
}

export interface UserPresence {
  pageNumber?: number;
  cursorPosition?: { x: number; y: number };
  selection?: { startPage: number; endPage: number; text?: string };
  isTyping?: boolean;
  lastActivity: Date;
  viewport?: { zoom: number; centerX: number; centerY: number };
}

export interface UserPreferences {
  notifications: NotificationSettings;
  displayName?: string;
  theme?: "light" | "dark" | "auto";
  language?: string;
  timezone?: string;
}

export interface NotificationSettings {
  comments: boolean;
  mentions: boolean;
  document_changes: boolean;
  user_joins: boolean;
  user_leaves: boolean;
  real_time: boolean;
  email_digest: boolean;
}

export interface CollaborationMessage {
  id: string;
  type: "comment" | "system" | "mention" | "reaction";
  content: string;
  author: CollaborationUser;
  timestamp: Date;
  threadId?: string;
  replyTo?: string;
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
  mentions?: string[];
  isEdited?: boolean;
  editedAt?: Date;
  isPinned?: boolean;
  isResolved?: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
}

export interface MessageReaction {
  emoji: string;
  users: string[];
  count: number;
}

export interface MessageAttachment {
  id: string;
  type: "image" | "file" | "link" | "annotation";
  url: string;
  filename?: string;
  size?: number;
  thumbnail?: string;
}

export interface CollaborationActivity {
  id: string;
  type: "join" | "leave" | "edit" | "comment" | "share" | "approve" | "reject";
  user: CollaborationUser;
  timestamp: Date;
  description: string;
  metadata?: unknown;
  pageNumber?: number;
  relatedId?: string;
}

export interface DocumentVersion {
  id: string;
  version: string;
  timestamp: Date;
  author: CollaborationUser;
  description: string;
  changes: VersionChange[];
  size: number;
  checksum: string;
  isCurrent: boolean;
  branch?: string;
}

export interface VersionChange {
  type: "add" | "modify" | "delete";
  target: "annotation" | "form_field" | "bookmark" | "metadata";
  targetId: string;
  description: string;
  before?: unknown;
  after?: unknown;
}

interface Comment {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
  pageNumber: number;
  position?: { x: number; y: number };
  replies: ReplyType[];
  isResolved: boolean;
  mentions: string[];
}

interface ReplyType {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
}

interface ShareSettings {
  visibility: "private" | "team" | "public";
  allowDownload: boolean;
  allowPrint: boolean;
  allowCopy: boolean;
  expiresAt?: Date;
  password?: string;
}

interface PDFCollaborationProps {
  pdfDocument: unknown;
  currentUser: CollaborationUser;
  collaborators?: CollaborationUser[];
  messages?: CollaborationMessage[];
  activities?: CollaborationActivity[];
  documentVersions?: DocumentVersion[];

  // Connection state
  isConnected?: boolean;
  onReconnect?: () => void;

  // User management
  onUserInvite?: (email: string, role: CollaborationUser["role"]) => void;
  onUserRemove?: (userId: string) => void;
  onUserRoleChange?: (userId: string, role: CollaborationUser["role"]) => void;

  // Comments (legacy support)
  onCommentAdd?: (comment: Omit<Comment, "id" | "timestamp">) => void;

  // Enhanced messaging
  onMessageSend?: (
    content: string,
    type: CollaborationMessage["type"],
    metadata?: unknown
  ) => void;
  onMessageReact?: (messageId: string, emoji: string) => void;
  onMessagePin?: (messageId: string) => void;
  onMessageResolve?: (messageId: string) => void;
  onMessageEdit?: (messageId: string, content: string) => void;
  onMessageDelete?: (messageId: string) => void;

  // Presence
  onPresenceUpdate?: (presence: UserPresence) => void;
  onCursorMove?: (position: { x: number; y: number }) => void;

  // Document management
  onVersionCreate?: (description: string) => void;
  onVersionRestore?: (versionId: string) => void;
  onConflictResolve?: (
    conflictId: string,
    resolution: "accept" | "reject" | "merge"
  ) => void;

  // Share settings
  onShareSettingsChange?: (settings: ShareSettings) => void;
}

// Real-time presence indicators
const PresenceIndicators: React.FC<{
  collaborators: CollaborationUser[];
  currentUser: CollaborationUser;
  onUserClick: (user: CollaborationUser) => void;
}> = ({ collaborators, currentUser, onUserClick }) => {
  const activeUsers = collaborators.filter(
    (user) => user.status === "online" && user.id !== currentUser.id
  );

  return (
    <div className="flex items-center gap-1 overflow-x-auto">
      {activeUsers.map((user) => (
        <button
          key={user.id}
          onClick={() => onUserClick(user)}
          className="relative group"
          title={`${user.name} - ${user.status}`}
        >
          <Avatar className="h-8 w-8 border-2 border-background">
            <AvatarImage src={user.avatar} alt={user.name} />
            <AvatarFallback className="text-xs">
              {user.name
                .split(" ")
                .map((n) => n[0])
                .join("")
                .toUpperCase()}
            </AvatarFallback>
          </Avatar>

          {/* Status indicator */}
          <div
            className={cn(
              "absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-background",
              user.status === "online"
                ? "bg-green-500"
                : user.status === "away"
                ? "bg-yellow-500"
                : "bg-gray-400"
            )}
          />

          {/* Cursor indicator */}
          {user.presence?.cursorPosition && (
            <div
              className="absolute w-2 h-2 bg-blue-500 rounded-full animate-pulse"
              style={{
                left: user.presence.cursorPosition.x,
                top: user.presence.cursorPosition.y,
                transform: "translate(-50%, -50%)",
              }}
            />
          )}
        </button>
      ))}

      {activeUsers.length === 0 && (
        <span className="text-sm text-muted-foreground">
          No other users online
        </span>
      )}
    </div>
  );
};

// Enhanced collaboration chat
const CollaborationChat: React.FC<{
  messages: CollaborationMessage[];
  currentUser: CollaborationUser;
  onMessageSend: (content: string, type: CollaborationMessage["type"]) => void;
  onMessageReact: (messageId: string, emoji: string) => void;
  onMessagePin: (messageId: string) => void;
  onMessageResolve: (messageId: string) => void;
  className?: string;
}> = ({
  messages,
  currentUser,
  onMessageSend,
  onMessageReact,
  onMessagePin,
  onMessageResolve,
  className,
}) => {
  const [newMessage, setNewMessage] = useState("");
  const [filter, setFilter] = useState<
    "all" | "comments" | "mentions" | "pinned"
  >("all");
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const filteredMessages = useMemo(() => {
    switch (filter) {
      case "comments":
        return messages.filter((m) => m.type === "comment");
      case "mentions":
        return messages.filter((m) => m.mentions?.includes(currentUser.id));
      case "pinned":
        return messages.filter((m) => m.isPinned);
      default:
        return messages;
    }
  }, [messages, filter, currentUser.id]);

  const handleSend = useCallback(() => {
    if (newMessage.trim()) {
      onMessageSend(newMessage.trim(), "comment");
      setNewMessage("");
    }
  }, [newMessage, onMessageSend]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        handleSend();
      }
    },
    [handleSend]
  );

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const getMessageIcon = (type: CollaborationMessage["type"]) => {
    switch (type) {
      case "comment":
        return MessageSquare;
      case "mention":
        return Bell;
      case "system":
        return Settings;
      case "reaction":
        return Star;
      default:
        return MessageSquare;
    }
  };

  return (
    <Card className={cn("flex flex-col h-96", className)}>
      {/* Header */}
      <div className="p-3 border-b">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium">Discussion</h4>
          <Badge variant="secondary">{filteredMessages.length}</Badge>
        </div>

        {/* Filters */}
        <div className="flex gap-1">
          {["all", "comments", "mentions", "pinned"].map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter(filterType as "all" | "comments" | "mentions" | "pinned")}
              className="text-xs capitalize"
            >
              {filterType}
            </Button>
          ))}
        </div>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-3">
        <div className="space-y-3">
          {filteredMessages.map((message) => {
            const Icon = getMessageIcon(message.type);
            const isOwn = message.author.id === currentUser.id;

            return (
              <div
                key={message.id}
                className={cn("flex gap-3 group", isOwn && "flex-row-reverse")}
              >
                <Avatar className="h-8 w-8 flex-shrink-0">
                  <AvatarImage
                    src={message.author.avatar}
                    alt={message.author.name}
                  />
                  <AvatarFallback className="text-xs">
                    {message.author.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")
                      .toUpperCase()}
                  </AvatarFallback>
                </Avatar>

                <div className={cn("flex-1 min-w-0", isOwn && "text-right")}>
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium">
                      {message.author.name}
                    </span>
                    <Icon className="size-3 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                    {message.isPinned && (
                      <Star className="size-3 text-yellow-500" />
                    )}
                  </div>

                  <div
                    className={cn(
                      "inline-block max-w-xs p-2 rounded-lg text-sm",
                      isOwn
                        ? "bg-primary text-primary-foreground ml-auto"
                        : "bg-muted"
                    )}
                  >
                    {message.content}
                    {message.isEdited && (
                      <span className="text-xs opacity-75 ml-2">(edited)</span>
                    )}
                  </div>

                  {/* Reactions */}
                  {message.reactions && message.reactions.length > 0 && (
                    <div className="flex gap-1 mt-1">
                      {message.reactions.map((reaction) => (
                        <button
                          key={reaction.emoji}
                          onClick={() =>
                            onMessageReact(message.id, reaction.emoji)
                          }
                          className="inline-flex items-center gap-1 px-1.5 py-0.5 rounded text-xs bg-muted hover:bg-muted/80"
                        >
                          <span>{reaction.emoji}</span>
                          <span>{reaction.count}</span>
                        </button>
                      ))}
                    </div>
                  )}

                  {/* Actions */}
                  <div className="flex gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onMessageReact(message.id, "👍")}
                      className="h-6 w-6 p-0"
                    >
                      👍
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onMessagePin(message.id)}
                      className="h-6 w-6 p-0"
                    >
                      <Star className="size-3" />
                    </Button>
                    {message.type === "comment" && !message.isResolved && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onMessageResolve(message.id)}
                        className="h-6 w-6 p-0"
                      >
                        <Check className="size-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-3 border-t">
        <div className="flex gap-2">
          <Input
            placeholder="Type a message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            className="flex-1"
          />
          <Button size="sm" onClick={handleSend} disabled={!newMessage.trim()}>
            <Send className="size-4" />
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default function PDFCollaboration({
  currentUser,
  collaborators = [],
  messages = [],
  activities = [],
  documentVersions = [],
  isConnected = true,
  onReconnect,

  onMessageSend,
  onMessageReact,
  onMessagePin,
  onMessageResolve,
  onVersionCreate,
  onVersionRestore,
}: PDFCollaborationProps) {
  const [activeTab, setActiveTab] = useState<
    "collaboration" | "chat" | "activity" | "versions"
  >("collaboration");

  const [activeUsers, setActiveUsers] = useState<CollaborationUser[]>(
    collaborators.length > 0
      ? collaborators
      : [
          {
            id: "user-1",
            name: "John Doe",
            email: "<EMAIL>",
            role: "owner",
            status: "online",
            lastSeen: new Date(),
            permissions: {
              canView: true,
              canEdit: true,
              canComment: true,
              canShare: true,
              canDelete: true,
              canManageUsers: true,
              canExport: true,
              canPrint: true,
              canDownload: true,
            },
          },
          {
            id: "user-2",
            name: "Jane Smith",
            email: "<EMAIL>",
            role: "editor",
            status: "online",
            lastSeen: new Date(Date.now() - 5 * 60 * 1000),
            permissions: {
              canView: true,
              canEdit: true,
              canComment: true,
              canShare: false,
              canDelete: false,
              canManageUsers: false,
              canExport: true,
              canPrint: true,
              canDownload: true,
            },
          },
          {
            id: "user-3",
            name: "Bob Wilson",
            email: "<EMAIL>",
            role: "viewer",
            status: "away",
            lastSeen: new Date(Date.now() - 30 * 60 * 1000),
            permissions: {
              canView: true,
              canEdit: false,
              canComment: true,
              canShare: false,
              canDelete: false,
              canManageUsers: false,
              canExport: false,
              canPrint: false,
              canDownload: false,
            },
          },
        ]
  );

  // Convert legacy comments to messages for backward compatibility
  const [enhancedMessages, setEnhancedMessages] = useState<
    CollaborationMessage[]
  >(
    messages.length > 0
      ? messages
      : [
          {
            id: "msg-1",
            type: "comment",
            content:
              "This section needs revision. The data doesn't match our latest findings.",
            author: activeUsers[1] || currentUser,
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            reactions: [{ emoji: "👍", users: ["user-1"], count: 1 }],
            mentions: [],
            isPinned: false,
            isResolved: false,
          },
        ]
  );





  // Enhanced collaboration handlers
  const handleMessageSend = useCallback(
    (content: string, type: CollaborationMessage["type"]) => {
      const message: CollaborationMessage = {
        id: Date.now().toString(),
        type,
        content,
        author: currentUser,
        timestamp: new Date(),
        reactions: [],
        mentions: [],
        isPinned: false,
        isResolved: false,
      };

      setEnhancedMessages((prev) => [...prev, message]);
      onMessageSend?.(content, type);

      toast("Message sent", {
        description: "Your message has been posted",
      });
    },
    [currentUser, onMessageSend]
  );

  const handleMessageReact = useCallback(
    (messageId: string, emoji: string) => {
      setEnhancedMessages((prev) =>
        prev.map((msg) => {
          if (msg.id === messageId) {
            const existingReaction = msg.reactions?.find(
              (r) => r.emoji === emoji
            );
            if (existingReaction) {
              const hasUserReacted = existingReaction.users.includes(
                currentUser.id
              );
              return {
                ...msg,
                reactions:
                  msg.reactions?.map((r) =>
                    r.emoji === emoji
                      ? {
                          ...r,
                          users: hasUserReacted
                            ? r.users.filter((id) => id !== currentUser.id)
                            : [...r.users, currentUser.id],
                          count: hasUserReacted ? r.count - 1 : r.count + 1,
                        }
                      : r
                  ) || [],
              };
            } else {
              return {
                ...msg,
                reactions: [
                  ...(msg.reactions || []),
                  {
                    emoji,
                    users: [currentUser.id],
                    count: 1,
                  },
                ],
              };
            }
          }
          return msg;
        })
      );
      onMessageReact?.(messageId, emoji);
    },
    [currentUser.id, onMessageReact]
  );

  const handleMessagePin = useCallback(
    (messageId: string) => {
      setEnhancedMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId ? { ...msg, isPinned: !msg.isPinned } : msg
        )
      );
      onMessagePin?.(messageId);
    },
    [onMessagePin]
  );

  const handleMessageResolve = useCallback(
    (messageId: string) => {
      setEnhancedMessages((prev) =>
        prev.map((msg) =>
          msg.id === messageId
            ? {
                ...msg,
                isResolved: !msg.isResolved,
                resolvedBy: currentUser.id,
                resolvedAt: new Date(),
              }
            : msg
        )
      );
      onMessageResolve?.(messageId);
    },
    [currentUser.id, onMessageResolve]
  );

  // Legacy comment support
  const addComment = useCallback(() => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      userId: currentUser.id,
      content: newComment,
      timestamp: new Date(),
      pageNumber: 1,
      replies: [],
      isResolved: false,
      mentions: [],
    };

    onCommentAdd?.(comment);

    // Also add as enhanced message
    handleMessageSend(newComment, "comment");
    setNewComment("");
  }, [newComment, currentUser.id, onCommentAdd, handleMessageSend]);

  const inviteUser = useCallback(() => {
    if (!inviteEmail.trim()) return;

    const newUser: CollaborationUser = {
      id: Date.now().toString(),
      name: inviteEmail.split("@")[0],
      email: inviteEmail,
      role: inviteRole,
      status: "offline",
      lastSeen: new Date(),
      permissions: {
        canView: true,
        canEdit: inviteRole === "owner" || inviteRole === "editor",
        canComment: true,
        canShare: inviteRole === "owner",
        canDelete: inviteRole === "owner",
        canManageUsers: inviteRole === "owner",
        canExport: inviteRole !== "viewer",
        canPrint: inviteRole !== "viewer",
        canDownload: inviteRole !== "viewer",
      },
    };

    setActiveUsers((prev) => [...prev, newUser]);
    onUserInvite?.(inviteEmail, inviteRole);
    setInviteEmail("");
    setInviteRole("viewer");
    setShowInvite(false);

    toast("User invited", {
      description: `Invitation sent to ${inviteEmail}`,
    });
  }, [inviteEmail, inviteRole, onUserInvite]);





  const getStatusColor = (status: CollaborationUser["status"]) => {
    switch (status) {
      case "online":
        return "bg-green-500";
      case "away":
        return "bg-yellow-500";
      case "offline":
        return "bg-gray-400";
    }
  };

  const getRoleColor = (role: CollaborationUser["role"]) => {
    switch (role) {
      case "owner":
        return "bg-purple-100 text-purple-800";
      case "editor":
        return "bg-blue-100 text-blue-800";
      case "reviewer":
        return "bg-orange-100 text-orange-800";
      case "viewer":
        return "bg-gray-100 text-gray-800";
    }
  };

  const onlineUsers = activeUsers.filter((user) => user.status === "online");
  const onlineCount = onlineUsers.length + 1; // +1 for current user
  const unresolvedComments = enhancedMessages.filter(
    (m) => m.type === "comment" && !m.isResolved
  );

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* Enhanced Header */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Collaboration
            </CardTitle>
            <div className="flex items-center gap-2">
              {isConnected ? (
                <div className="flex items-center gap-1 text-green-600">
                  <Wifi className="size-4" />
                  <span className="text-sm">Connected</span>
                </div>
              ) : (
                <button
                  onClick={onReconnect}
                  className="flex items-center gap-1 text-red-600 hover:text-red-700"
                >
                  <WifiOff className="size-4" />
                  <span className="text-sm">Reconnect</span>
                </button>
              )}
              <Badge variant="secondary">{onlineCount} online</Badge>
            </div>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowInvite(!showInvite)}
          >
            <UserPlus className="size-4 mr-1" />
            Invite
          </Button>
        </div>

        {/* Presence indicators */}
        <PresenceIndicators
          collaborators={activeUsers}
          currentUser={currentUser}
          onUserClick={(user) => console.log("User clicked:", user)}
        />

        {/* Invite panel */}
        {showInvite && (
          <Card className="p-3 mt-4 space-y-3">
            <div className="flex gap-2">
              <Input
                placeholder="Email address"
                type="email"
                value={inviteEmail}
                onChange={(e) => setInviteEmail(e.target.value)}
                className="flex-1"
              />
              <Select value={inviteRole} onValueChange={(value: string) => setInviteRole(value as CollaborationUser["role"])}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="viewer">Viewer</SelectItem>
                  <SelectItem value="reviewer">Reviewer</SelectItem>
                  <SelectItem value="editor">Editor</SelectItem>
                  <SelectItem value="owner">Owner</SelectItem>
                </SelectContent>
              </Select>
              <Button
                size="sm"
                onClick={inviteUser}
                disabled={!inviteEmail.trim()}
              >
                Send
              </Button>
            </div>
          </Card>
        )}
      </Card>

      {/* Enhanced Tabs */}
      <div className="flex gap-1">
        {["collaboration", "chat", "activity", "versions"].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? "default" : "ghost"}
            size="sm"
            onClick={() => setActiveTab(tab as "collaboration" | "chat" | "activity" | "versions")}
            className="capitalize"
          >
            {tab}
            {tab === "chat" && enhancedMessages.length > 0 && (
              <Badge variant="secondary" className="ml-2 h-4 w-4 p-0 text-xs">
                {enhancedMessages.length}
              </Badge>
            )}
          </Button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === "collaboration" && (
          <Card className="h-full p-4">
            <CardContent className="space-y-4 p-0">
              {/* Active Users */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">
                    Active Users ({onlineUsers.length + 1})
                  </h3>
                  <Button size="sm" variant="outline">
                    <Video className="h-4 w-4 mr-2" />
                    Start Call
                  </Button>
                </div>

                <div className="grid grid-cols-1 gap-2">
                  {/* Current user */}
                  <div className="flex items-center gap-3 p-3 rounded-lg border bg-primary/5">
                    <div className="relative">
                      <Avatar className="h-8 w-8">
                        <AvatarImage
                          src={currentUser.avatar || "/placeholder.svg"}
                        />
                        <AvatarFallback>
                          {currentUser.name
                            .split(" ")
                            .map((n) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white bg-green-500" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium">
                        {currentUser.name} (You)
                      </div>
                      <Badge
                        variant="outline"
                        className={getRoleColor(currentUser.role)}
                      >
                        {currentUser.role}
                      </Badge>
                    </div>
                  </div>

                  {/* Other users */}
                  {activeUsers.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center gap-3 p-3 rounded-lg border"
                    >
                      <div className="relative">
                        <Avatar className="h-8 w-8">
                          <AvatarImage
                            src={user.avatar || "/placeholder.svg"}
                          />
                          <AvatarFallback>
                            {user.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div
                          className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(
                            user.status
                          )}`}
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium truncate">
                          {user.name}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className={getRoleColor(user.role)}
                          >
                            {user.role}
                          </Badge>
                          {user.status === "away" && user.lastSeen && (
                            <span className="text-xs text-muted-foreground">
                              Away{" "}
                              {Math.round(
                                (Date.now() - user.lastSeen.getTime()) /
                                  (1000 * 60)
                              )}
                              m
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Link
                </Button>
                <Button variant="outline" size="sm">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </div>

              {/* Legacy Comments Section */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">Quick Comments</h3>
                  <Badge variant="outline">
                    {unresolvedComments.length} unresolved
                  </Badge>
                </div>

                <div className="space-y-2">
                  <Textarea
                    placeholder="Add a comment..."
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    className="min-h-[80px]"
                  />
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      onClick={addComment}
                      disabled={!newComment.trim()}
                    >
                      <Send className="h-4 w-4 mr-2" />
                      Comment
                    </Button>
                    <Button size="sm" variant="outline">
                      <Bell className="h-4 w-4 mr-2" />
                      Mention
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === "chat" && (
          <CollaborationChat
            messages={enhancedMessages}
            currentUser={currentUser}
            onMessageSend={handleMessageSend}
            onMessageReact={handleMessageReact}
            onMessagePin={handleMessagePin}
            onMessageResolve={handleMessageResolve}
            className="h-full"
          />
        )}

        {activeTab === "activity" && (
          <Card className="h-full p-4">
            <h4 className="font-medium mb-4">Recent Activity</h4>
            <div className="space-y-3">
              {activities.length > 0 ? (
                activities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center gap-3 p-3 rounded border"
                  >
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div className="flex-1">
                      <p className="text-sm">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.timestamp.toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <div className="space-y-2">
                    <Clock className="h-3 w-3 mx-auto" />
                    <p className="text-sm">Jane Smith commented on page 1</p>
                    <p className="text-xs">2 hours ago</p>
                  </div>
                  <div className="space-y-2 mt-4">
                    <Clock className="h-3 w-3 mx-auto" />
                    <p className="text-sm">John Doe shared the document</p>
                    <p className="text-xs">1 day ago</p>
                  </div>
                </div>
              )}
            </div>
          </Card>
        )}

        {activeTab === "versions" && (
          <Card className="h-full p-4">
            <h4 className="font-medium mb-4">Version History</h4>
            <div className="space-y-3">
              {documentVersions.length > 0 ? (
                documentVersions.map((version) => (
                  <div
                    key={version.id}
                    className="flex items-center justify-between p-3 rounded border"
                  >
                    <div>
                      <div className="font-medium">{version.version}</div>
                      <div className="text-sm text-muted-foreground">
                        {version.description}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {version.timestamp.toLocaleString()}
                      </div>
                    </div>
                    {!version.isCurrent && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onVersionRestore?.(version.id)}
                      >
                        Restore
                      </Button>
                    )}
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <GitBranch className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p>No versions saved</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => onVersionCreate?.("Initial version")}
                  >
                    Create First Version
                  </Button>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
