"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Memory, 
  HardDrive, 
  Zap, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Trash2,
  Activity,
  BarChart3,
  Cpu,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { MemoryManager, type MemoryMetrics, type MemoryPressureEvent } from '@/lib/pdf/memory-manager';

interface MemoryMonitorProps {
  memoryManager: MemoryManager;
  className?: string;
}

export default function MemoryMonitor({ memoryManager, className }: MemoryMonitorProps) {
  const [metrics, setMetrics] = useState<MemoryMetrics>(memoryManager.getMetrics());
  const [memoryHistory, setMemoryHistory] = useState<number[]>([]);
  const [pressureEvents, setPressureEvents] = useState<MemoryPressureEvent[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Update metrics periodically
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(() => {
      const currentMetrics = memoryManager.getMetrics();
      setMetrics(currentMetrics);
      
      // Update memory history
      setMemoryHistory(prev => {
        const newHistory = [...prev, currentMetrics.totalMemoryUsed];
        return newHistory.slice(-50); // Keep last 50 data points
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isMonitoring, memoryManager]);

  // Listen for memory pressure events
  useEffect(() => {
    const handleMemoryPressure = (event: MemoryPressureEvent) => {
      setPressureEvents(prev => [event, ...prev.slice(0, 9)]); // Keep last 10 events
    };

    memoryManager.addEventListener('memory-pressure', handleMemoryPressure);

    return () => {
      memoryManager.removeEventListener('memory-pressure', handleMemoryPressure);
    };
  }, [memoryManager]);

  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    setMemoryHistory([]);
    setPressureEvents([]);
  }, []);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  const clearCache = useCallback(() => {
    memoryManager.clear();
    setMetrics(memoryManager.getMetrics());
  }, [memoryManager]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (timestamp: number): string => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getPressureColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-500';
      case 'medium': return 'text-yellow-500';
      case 'high': return 'text-orange-500';
      case 'critical': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getPressureBadgeVariant = (level: string) => {
    switch (level) {
      case 'low': return 'default';
      case 'medium': return 'secondary';
      case 'high': return 'destructive';
      case 'critical': return 'destructive';
      default: return 'outline';
    }
  };

  const getMemoryUsagePercentage = (): number => {
    // Estimate based on browser memory if available
    if ('memory' in performance) {
      const memInfo = (performance as unknown as { memory: { usedJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
      return (memInfo.usedJSHeapSize / memInfo.jsHeapSizeLimit) * 100;
    }
    return 0;
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Memory className="h-5 w-5" />
            Memory Monitor
          </CardTitle>
          <CardDescription>
            Real-time memory usage and cache performance monitoring
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Badge variant={isMonitoring ? "default" : "secondary"}>
                {isMonitoring ? "Monitoring" : "Stopped"}
              </Badge>
              <Badge variant={getPressureBadgeVariant(metrics.memoryPressure)}>
                {metrics.memoryPressure} pressure
              </Badge>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                onClick={isMonitoring ? stopMonitoring : startMonitoring}
                variant={isMonitoring ? "destructive" : "default"}
                size="sm"
              >
                {isMonitoring ? "Stop" : "Start"} Monitoring
              </Button>
              <Button variant="outline" size="sm" onClick={clearCache}>
                <Trash2 className="h-4 w-4 mr-2" />
                Clear Cache
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Memory Pressure Alerts */}
      {pressureEvents.length > 0 && pressureEvents[0].level !== 'medium' && (
        <Alert variant={pressureEvents[0].level === 'critical' ? "destructive" : "default"}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Memory Pressure: {pressureEvents[0].level}</strong>
            <br />
            Current usage: {formatBytes(pressureEvents[0].currentUsage)} / {formatBytes(pressureEvents[0].maxMemory)}
            <br />
            Suggested actions: {pressureEvents[0].suggestedActions.join(', ')}
          </AlertDescription>
        </Alert>
      )}

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <HardDrive className="h-4 w-4 text-blue-500" />
              Memory Used
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(metrics.totalMemoryUsed)}</div>
            <div className="text-xs text-muted-foreground">
              {metrics.itemCount} items cached
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <TrendingUp className="h-4 w-4 text-green-500" />
              Cache Hit Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.cacheHitRate * 100).toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Efficiency metric
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Zap className="h-4 w-4 text-purple-500" />
              Compression
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{(metrics.compressionRatio * 100).toFixed(1)}%</div>
            <div className="text-xs text-muted-foreground">
              Space saved
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="h-4 w-4 text-orange-500" />
              GC Count
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.gcCount}</div>
            <div className="text-xs text-muted-foreground">
              Cleanup cycles
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Memory Usage Chart */}
      {memoryHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Memory Usage History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Browser Memory Usage */}
              {'memory' in performance && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Browser Memory Usage</span>
                    <span>{getMemoryUsagePercentage().toFixed(1)}%</span>
                  </div>
                  <Progress value={getMemoryUsagePercentage()} className="h-2" />
                </div>
              )}

              {/* Cache Memory Usage */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Cache Memory Usage</span>
                  <span>{formatBytes(metrics.totalMemoryUsed)}</span>
                </div>
                <Progress 
                  value={(metrics.totalMemoryUsed / (512 * 1024 * 1024)) * 100} 
                  className="h-2" 
                />
              </div>

              {/* Memory History Chart */}
              <div className="mt-4">
                <div className="text-sm font-medium mb-2">Usage Over Time</div>
                <div className="h-32 flex items-end space-x-1">
                  {memoryHistory.map((usage, index) => {
                    const maxUsage = Math.max(...memoryHistory);
                    const height = maxUsage > 0 ? (usage / maxUsage) * 100 : 0;
                    
                    return (
                      <div
                        key={index}
                        className="bg-primary flex-1 min-w-[2px] rounded-t"
                        style={{ height: `${Math.max(height, 2)}%` }}
                        title={formatBytes(usage)}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Cache Statistics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm">Total Items</span>
              <span className="font-bold">{metrics.itemCount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Average Item Size</span>
              <span className="font-bold">{formatBytes(metrics.averageItemSize)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Cache Hit Rate</span>
              <span className="font-bold">{(metrics.cacheHitRate * 100).toFixed(2)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Compression Ratio</span>
              <span className="font-bold">{(metrics.compressionRatio * 100).toFixed(2)}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm">Last Cleanup</span>
              <span className="font-bold">{formatTime(metrics.lastCleanup)}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Memory Pressure Events</CardTitle>
          </CardHeader>
          <CardContent>
            {pressureEvents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <CheckCircle className="h-8 w-8 mx-auto mb-2" />
                <p>No memory pressure events</p>
              </div>
            ) : (
              <div className="space-y-3 max-h-48 overflow-y-auto">
                {pressureEvents.map((event, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 bg-muted rounded-lg">
                    <AlertTriangle className={cn("h-4 w-4 mt-0.5", getPressureColor(event.level))} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <Badge variant={getPressureBadgeVariant(event.level)} className="text-xs">
                          {event.level}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {formatTime(Date.now())}
                        </span>
                      </div>
                      <p className="text-sm mt-1">
                        {formatBytes(event.currentUsage)} / {formatBytes(event.maxMemory)}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {event.suggestedActions.slice(0, 2).join(', ')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Browser Memory Info */}
      {'memory' in performance && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              Browser Memory Information
            </CardTitle>
            <CardDescription>
              Native browser memory statistics (Chrome/Edge only)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {(() => {
                const memInfo = (performance as unknown as { memory: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
                return (
                  <>
                    <div className="text-center">
                      <div className="text-lg font-bold">{formatBytes(memInfo.usedJSHeapSize)}</div>
                      <div className="text-xs text-muted-foreground">Used Heap Size</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold">{formatBytes(memInfo.totalJSHeapSize)}</div>
                      <div className="text-xs text-muted-foreground">Total Heap Size</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold">{formatBytes(memInfo.jsHeapSizeLimit)}</div>
                      <div className="text-xs text-muted-foreground">Heap Size Limit</div>
                    </div>
                  </>
                );
              })()}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
