"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Cpu, 
  Memory, 
  Activity, 
  Settings, 
  Play, 
  Pause, 
  RotateCcw,
  TrendingUp,
  Users,
  Clock,

} from 'lucide-react';
import { cn } from '@/lib/utils';
import { getWorkerManager, type WorkerStats, type WorkerConfig } from '@/lib/pdf/worker-manager';

interface WorkerManagerDashboardProps {
  className?: string;
}

export default function WorkerManagerDashboard({ className }: WorkerManagerDashboardProps) {
  const [stats, setStats] = useState<WorkerStats>({
    activeWorkers: 0,
    totalWorkers: 0,
    memoryUsage: 0,
    tasksQueued: 0,
    tasksCompleted: 0,
    averageTaskTime: 0,
    workerUtilization: 0,
  });

  const [config, setConfig] = useState<WorkerConfig>({
    maxWorkers: 4,
    workerSrc: '/pdf-worker-enhanced.js',
    enableSharedArrayBuffer: false,
    memoryLimit: 512,
    idleTimeout: 30000,
    enableParallelProcessing: true,
    workerPoolSize: 4,
    enableMemoryOptimization: true,
    enableProgressiveRendering: true,
  });

  const [isMonitoring, setIsMonitoring] = useState(false);
  const [performanceHistory, setPerformanceHistory] = useState<number[]>([]);

  const workerManager = getWorkerManager(config);

  // Update stats periodically
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(() => {
      const currentStats = workerManager.getStats();
      setStats(currentStats);
      
      // Update performance history
      setPerformanceHistory(prev => {
        const newHistory = [...prev, currentStats.workerUtilization * 100];
        return newHistory.slice(-20); // Keep last 20 data points
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [isMonitoring, workerManager]);

  const startMonitoring = useCallback(() => {
    setIsMonitoring(true);
    workerManager.initialize();
  }, [workerManager]);

  const stopMonitoring = useCallback(() => {
    setIsMonitoring(false);
  }, []);

  const resetStats = useCallback(() => {
    setPerformanceHistory([]);
    setStats({
      activeWorkers: 0,
      totalWorkers: 0,
      memoryUsage: 0,
      tasksQueued: 0,
      tasksCompleted: 0,
      averageTaskTime: 0,
      workerUtilization: 0,
    });
  }, []);

  const updateWorkerConfig = useCallback((newConfig: Partial<WorkerConfig>) => {
    const updatedConfig = { ...config, ...newConfig };
    setConfig(updatedConfig);
    workerManager.updateConfig(updatedConfig);
  }, [config, workerManager]);

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getUtilizationColor = (utilization: number): string => {
    if (utilization < 0.3) return 'text-green-500';
    if (utilization < 0.7) return 'text-yellow-500';
    return 'text-red-500';
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            PDF Worker Manager Dashboard
          </CardTitle>
          <CardDescription>
            Monitor and manage PDF.js worker performance and configuration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3">
            <Button
              onClick={isMonitoring ? stopMonitoring : startMonitoring}
              variant={isMonitoring ? "destructive" : "default"}
            >
              {isMonitoring ? (
                <>
                  <Pause className="h-4 w-4 mr-2" />
                  Stop Monitoring
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Monitoring
                </>
              )}
            </Button>
            
            <Button variant="outline" onClick={resetStats}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset Stats
            </Button>

            <Badge variant={isMonitoring ? "default" : "secondary"}>
              {isMonitoring ? "Active" : "Inactive"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Users className="h-4 w-4 text-blue-500" />
                  Active Workers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.activeWorkers}</div>
                <div className="text-xs text-muted-foreground">
                  of {stats.totalWorkers} total
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Memory className="h-4 w-4 text-green-500" />
                  Memory Usage
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatBytes(stats.memoryUsage)}</div>
                <div className="text-xs text-muted-foreground">
                  Limit: {formatBytes(config.memoryLimit * 1024 * 1024)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Activity className="h-4 w-4 text-purple-500" />
                  Task Queue
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.tasksQueued}</div>
                <div className="text-xs text-muted-foreground">
                  {stats.tasksCompleted} completed
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Clock className="h-4 w-4 text-orange-500" />
                  Avg Task Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatTime(stats.averageTaskTime)}</div>
                <div className="text-xs text-muted-foreground">
                  Per task average
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Worker Utilization */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Worker Utilization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Current Utilization</span>
                  <span className={cn("font-bold", getUtilizationColor(stats.workerUtilization))}>
                    {(stats.workerUtilization * 100).toFixed(1)}%
                  </span>
                </div>
                <Progress 
                  value={stats.workerUtilization * 100} 
                  className="h-2"
                />
                
                {/* Performance History Chart */}
                {performanceHistory.length > 0 && (
                  <div className="mt-4">
                    <div className="text-sm font-medium mb-2">Performance History</div>
                    <div className="h-20 flex items-end space-x-1">
                      {performanceHistory.map((value, index) => (
                        <div
                          key={index}
                          className="bg-primary flex-1 min-w-[2px] rounded-t"
                          style={{ height: `${Math.max(value, 2)}%` }}
                          title={`${value.toFixed(1)}%`}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-6">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Task Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Tasks Completed</span>
                  <span className="font-bold">{stats.tasksCompleted}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Tasks Queued</span>
                  <span className="font-bold">{stats.tasksQueued}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Average Task Time</span>
                  <span className="font-bold">{formatTime(stats.averageTaskTime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Worker Efficiency</span>
                  <span className={cn("font-bold", getUtilizationColor(stats.workerUtilization))}>
                    {(stats.workerUtilization * 100).toFixed(1)}%
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Resource Usage</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm">Memory Usage</span>
                  <span className="font-bold">{formatBytes(stats.memoryUsage)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Memory Limit</span>
                  <span className="font-bold">{formatBytes(config.memoryLimit * 1024 * 1024)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Active Workers</span>
                  <span className="font-bold">{stats.activeWorkers} / {stats.totalWorkers}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Max Workers</span>
                  <span className="font-bold">{config.maxWorkers}</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="configuration" className="space-y-6">
          {/* Configuration Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Worker Configuration
              </CardTitle>
              <CardDescription>
                Adjust worker settings for optimal performance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Max Workers</label>
                    <div className="mt-1">
                      <input
                        type="number"
                        min="1"
                        max="16"
                        value={config.maxWorkers}
                        onChange={(e) => updateWorkerConfig({ maxWorkers: parseInt(e.target.value) })}
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Memory Limit (MB)</label>
                    <div className="mt-1">
                      <input
                        type="number"
                        min="128"
                        max="2048"
                        value={config.memoryLimit}
                        onChange={(e) => updateWorkerConfig({ memoryLimit: parseInt(e.target.value) })}
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Worker Pool Size</label>
                    <div className="mt-1">
                      <input
                        type="number"
                        min="1"
                        max="8"
                        value={config.workerPoolSize}
                        onChange={(e) => updateWorkerConfig({ workerPoolSize: parseInt(e.target.value) })}
                        className="w-full px-3 py-2 border rounded-md"
                      />
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="parallel-processing"
                      checked={config.enableParallelProcessing}
                      onChange={(e) => updateWorkerConfig({ enableParallelProcessing: e.target.checked })}
                    />
                    <label htmlFor="parallel-processing" className="text-sm font-medium">
                      Enable Parallel Processing
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="memory-optimization"
                      checked={config.enableMemoryOptimization}
                      onChange={(e) => updateWorkerConfig({ enableMemoryOptimization: e.target.checked })}
                    />
                    <label htmlFor="memory-optimization" className="text-sm font-medium">
                      Enable Memory Optimization
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="progressive-rendering"
                      checked={config.enableProgressiveRendering}
                      onChange={(e) => updateWorkerConfig({ enableProgressiveRendering: e.target.checked })}
                    />
                    <label htmlFor="progressive-rendering" className="text-sm font-medium">
                      Enable Progressive Rendering
                    </label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="shared-array-buffer"
                      checked={config.enableSharedArrayBuffer}
                      onChange={(e) => updateWorkerConfig({ enableSharedArrayBuffer: e.target.checked })}
                    />
                    <label htmlFor="shared-array-buffer" className="text-sm font-medium">
                      Enable SharedArrayBuffer
                    </label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
