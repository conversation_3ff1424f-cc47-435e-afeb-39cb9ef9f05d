import React from "react";
import { PDFSidebar, PDFFloatingToolbar } from "../navigation";
import { useEnhancedViewerState } from "../core/hooks";
import { Document, Page } from "react-pdf";
import { toast } from "sonner";

export interface OptimizedPDFViewerProps {
  file: string | File;
  className?: string;
}

export const OptimizedPDFViewerExample: React.FC<OptimizedPDFViewerProps> = ({
  file,
  className,
}) => {
  const {
    state,
    navigation,
    view,
    ui,
    search,
    document: docActions,
    computed,
    undo,
    redo,
  } = useEnhancedViewerState();

  // Document load handlers
  const handleDocumentLoadSuccess = (pdf: { numPages: number }) => {
    docActions.setNumPages(pdf.numPages);
    docActions.setLoading(false);
    toast.success(`PDF loaded successfully - ${pdf.numPages} pages`);
  };

  const handleDocumentLoadError = (error: Error) => {
    docActions.setLoading(false);
    toast.error("Failed to load PDF", {
      description: error.message,
    });
  };

  // Action handlers for the layout
  const handleDownload = () => {
    if (typeof file === "string") {
      const link = document.createElement("a");
      link.href = file;
      link.download = "document.pdf";
      link.click();
    } else {
      const url = URL.createObjectURL(file);
      const link = document.createElement("a");
      link.href = url;
      link.download = file.name;
      link.click();
      URL.revokeObjectURL(url);
    }
  };

  const handleAddBookmark = () => {
    const title = prompt(
      `Bookmark page ${state.pageNumber}:`,
      `Page ${state.pageNumber}`
    );
    if (title) {
      toast.success("Bookmark added", {
        description: `Page ${state.pageNumber} bookmarked as "${title}"`,
      });
    }
  };

  const handleToggleSearch = () => {
    ui.setActiveTab("search");
    if (!state.sidebarOpen) {
      ui.toggleSidebar();
    }
  };






  return (
    <div className={`flex h-screen bg-background ${className}`}>
      {/* Sidebar with consolidated features */}
      <PDFSidebar
        isOpen={state.sidebarOpen}
        onClose={ui.toggleSidebar}
        activeTab={state.activeTab as "search" | "bookmarks" | "outline" | "thumbnails" | "annotations" | "forms" | "settings"}
        onTabChange={(tab: string) => ui.setActiveTab(tab)}
        currentMode={state.currentMode}
        onModeChange={ui.setMode}
        adaptiveLayout={true}
        performanceMode={state.compactMode}
        pdfDocument={null}
        numPages={state.numPages}
        currentPage={state.pageNumber}
        outline={[]}
        currentScale={state.scale}
        searchText={state.searchText}
        onSearchChange={search.setSearchText}
        onPageSelect={navigation.goToPage}
        bookmarks={[]}
        onAddBookmark={handleAddBookmark}
        onRemoveBookmark={() => {}}
        onUpdateBookmark={() => {}}
        annotations={[]}
        selectedTool={state.selectedTool as "select" | "highlight" | "rectangle" | "circle" | "arrow" | "text" | "note" | "link" | null}
        onToolSelect={(tool: string | null) => ui.setTool(tool)}
        onAnnotationAdd={() => {}}
        onAnnotationUpdate={() => {}}
        onAnnotationDelete={() => {}}
        formFields={[]}
        formData={{}}
        onFormFieldsChange={() => {}}
        onFormDataChange={() => {}}
      />

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Floating toolbar with consolidated features */}
        <PDFFloatingToolbar
          selectedTool={state.selectedTool as "select" | "highlight" | "rectangle" | "circle" | "arrow" | "text" | "note" | "link" | null}
          onToolSelect={(tool: string | null) => ui.setTool(tool)}
          selectedColor="#FFEB3B"
          onColorChange={() => {}}
          isVisible={true}
          onToggleVisibility={() => {}}
          position={{ x: 20, y: 20 }}
          onPositionChange={() => {}}
          annotationCount={0}
          onUndo={undo}
          onRedo={redo}
          onClearAll={() => {}}
          canUndo={computed.canUndo}
          canRedo={computed.canRedo}
          // Enhanced navigation features
          onPrevPage={navigation.prevPage}
          onNextPage={navigation.nextPage}
          canGoToPrev={computed.canGoToPrev}
          canGoToNext={computed.canGoToNext}
          pageNumber={state.pageNumber}
          numPages={state.numPages}
          onPageChange={navigation.goToPage}
          scale={state.scale}
          onScaleChange={view.setScale}
          rotation={state.rotation}
          onRotationChange={(rotation: number) => {
            // Use rotate method or implement setRotation
            console.log("Rotation changed to:", rotation);
          }}
          onZoomIn={view.zoomIn}
          onZoomOut={view.zoomOut}
          onResetZoom={view.resetZoom}
          onRotate={view.rotate}
          onToggleFullscreen={view.toggleFullscreen}
          onDownload={handleDownload}
          onToggleSearch={handleToggleSearch}
          onAddBookmark={handleAddBookmark}
          onOpenSidebar={ui.toggleSidebar}
          adaptiveLayout={true}
          performanceMode={state.compactMode}
          isCompact={state.compactMode}
        />

        {/* PDF Document */}
        <div className="flex-1 flex items-center justify-center p-4">
          <Document
            file={file}
            onLoadSuccess={handleDocumentLoadSuccess}
            onLoadError={handleDocumentLoadError}
            loading={
              <div className="flex items-center justify-center p-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            }
            className="max-w-full"
          >
            <Page
              pageNumber={state.pageNumber}
              scale={state.scale}
              rotate={state.rotation}
              loading={
                <div className="flex items-center justify-center p-8 bg-muted rounded">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                </div>
              }
              className="shadow-lg rounded border"
            />
          </Document>
        </div>
      </div>
    </div>
  );
};

export default OptimizedPDFViewerExample;
