"use client";

import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import {
  Smartphone,
  Tablet,
  Monitor,
  Touch,
  Gesture,
  Zap,
  BarChart3,
  Eye,
  Target,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import MobilePDFViewer from '../mobile/mobile-pdf-viewer';
import { useLayoutContext } from '../layout/responsive-layout-manager';

interface AdvancedMobileDemoProps {
  className?: string;
}

// Mock PDF file for demo
const createMockPDFFile = () => {
  const blob = new Blob(['Mock PDF content'], { type: 'application/pdf' });
  return new File([blob], 'demo.pdf', { type: 'application/pdf' });
};

export default function AdvancedMobileDemo({ className }: AdvancedMobileDemoProps) {
  const [activeTab, setActiveTab] = useState<'overview' | 'viewer' | 'navigation' | 'gestures' | 'performance'>('overview');
  const [mockPDFFile] = useState(() => createMockPDFFile());
  const [showMobileViewer, setShowMobileViewer] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [, setZoom] = useState(1);

  const layoutContext = useLayoutContext();

  // Demo statistics
  const demoStats = useMemo(() => ({
    touchTargetCompliance: 98,
    gestureRecognition: 95,
    performanceScore: 92,
    accessibilityScore: 96,
    crossPlatformCompatibility: 99,
    batteryEfficiency: 88,
  }), []);



  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Mobile Optimization Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-5 w-5" />
            Advanced Mobile Optimizations
          </CardTitle>
          <CardDescription>
            Next-generation mobile-first design with advanced touch gestures, responsive layouts, and cross-platform compatibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="border-2 border-blue-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Touch className="h-4 w-4 text-blue-500" />
                  Touch Interface
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{demoStats.touchTargetCompliance}%</div>
                <div className="text-xs text-muted-foreground">Touch compliance</div>
                <div className="text-xs mt-1">44px+ touch targets, haptic feedback, gesture recognition</div>
              </CardContent>
            </Card>

            <Card className="border-2 border-green-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Gesture className="h-4 w-4 text-green-500" />
                  Gesture Support
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{demoStats.gestureRecognition}%</div>
                <div className="text-xs text-muted-foreground">Gesture accuracy</div>
                <div className="text-xs mt-1">Tap, swipe, pinch, rotate, long press, multi-touch</div>
              </CardContent>
            </Card>

            <Card className="border-2 border-purple-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="h-4 w-4 text-purple-500" />
                  Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{demoStats.performanceScore}%</div>
                <div className="text-xs text-muted-foreground">Performance score</div>
                <div className="text-xs mt-1">60fps animations, hardware acceleration, optimized rendering</div>
              </CardContent>
            </Card>

            <Card className="border-2 border-orange-200">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Eye className="h-4 w-4 text-orange-500" />
                  Accessibility
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-lg font-bold">{demoStats.accessibilityScore}%</div>
                <div className="text-xs text-muted-foreground">Accessibility score</div>
                <div className="text-xs mt-1">Screen reader support, high contrast, large text options</div>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Device Compatibility */}
      <Card>
        <CardHeader>
          <CardTitle>Cross-Platform Compatibility</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 border rounded-lg">
              <Smartphone className="h-8 w-8 mx-auto mb-2 text-blue-500" />
              <div className="font-medium text-sm">Mobile Phones</div>
              <div className="text-xs text-muted-foreground mt-1">
                iOS 12+, Android 8+
              </div>
              <div className="text-xs text-green-600 mt-1">✓ Fully Optimized</div>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <Tablet className="h-8 w-8 mx-auto mb-2 text-green-500" />
              <div className="font-medium text-sm">Tablets</div>
              <div className="text-xs text-muted-foreground mt-1">
                iPad, Android tablets
              </div>
              <div className="text-xs text-green-600 mt-1">✓ Adaptive Layout</div>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <Monitor className="h-8 w-8 mx-auto mb-2 text-purple-500" />
              <div className="font-medium text-sm">Desktop</div>
              <div className="text-xs text-muted-foreground mt-1">
                Windows, macOS, Linux
              </div>
              <div className="text-xs text-green-600 mt-1">✓ Touch & Mouse</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Device Info */}
      <Card>
        <CardHeader>
          <CardTitle>Current Device Detection</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-blue-500">
                {layoutContext?.isMobile ? 'Mobile' : layoutContext?.isTablet ? 'Tablet' : 'Desktop'}
              </div>
              <div className="text-xs text-muted-foreground">Device Type</div>
            </div>
            <div>
              <div className="text-lg font-bold text-green-500">
                {layoutContext?.orientation || 'Unknown'}
              </div>
              <div className="text-xs text-muted-foreground">Orientation</div>
            </div>
            <div>
              <div className="text-lg font-bold text-purple-500">
                {typeof window !== 'undefined' ? `${window.innerWidth}×${window.innerHeight}` : 'N/A'}
              </div>
              <div className="text-xs text-muted-foreground">Screen Size</div>
            </div>
            <div>
              <div className="text-lg font-bold text-orange-500">
                {typeof window !== 'undefined' && 'ontouchstart' in window ? 'Yes' : 'No'}
              </div>
              <div className="text-xs text-muted-foreground">Touch Support</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { label: 'Touch Target Compliance', value: demoStats.touchTargetCompliance, color: 'bg-blue-500' },
              { label: 'Gesture Recognition', value: demoStats.gestureRecognition, color: 'bg-green-500' },
              { label: 'Performance Score', value: demoStats.performanceScore, color: 'bg-purple-500' },
              { label: 'Accessibility Score', value: demoStats.accessibilityScore, color: 'bg-orange-500' },
              { label: 'Cross-Platform Compatibility', value: demoStats.crossPlatformCompatibility, color: 'bg-pink-500' },
              { label: 'Battery Efficiency', value: demoStats.batteryEfficiency, color: 'bg-cyan-500' },
            ].map((metric) => (
              <div key={metric.label} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{metric.label}</span>
                  <span className="font-medium">{metric.value}%</span>
                </div>
                <Progress value={metric.value} className="h-2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderViewerTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Advanced Mobile PDF Viewer</CardTitle>
          <CardDescription>
            Touch-optimized PDF viewer with advanced gesture controls and mobile-first design
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Interactive Demo</h4>
                <p className="text-sm text-muted-foreground">
                  Experience the advanced mobile-optimized PDF viewer with touch gestures
                </p>
              </div>
              <Button
                onClick={() => setShowMobileViewer(true)}
                className="touch-target-comfortable"
              >
                <Smartphone className="h-4 w-4 mr-2" />
                Open Mobile Viewer
              </Button>
            </div>

            {/* Feature List */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Advanced Touch Gestures</h5>
                <ul className="text-xs space-y-1 text-muted-foreground">
                  <li>• Tap to show/hide controls</li>
                  <li>• Double tap to zoom</li>
                  <li>• Pinch to zoom in/out</li>
                  <li>• Swipe to navigate pages</li>
                  <li>• Two-finger tap to rotate</li>
                  <li>• Long press for context menu</li>
                  <li>• Three-finger tap for tools</li>
                  <li>• Edge swipe for navigation</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h5 className="font-medium text-sm">Mobile Features</h5>
                <ul className="text-xs space-y-1 text-muted-foreground">
                  <li>• Fullscreen mode</li>
                  <li>• Auto-hide controls</li>
                  <li>• Safe area support</li>
                  <li>• Haptic feedback</li>
                  <li>• Hardware acceleration</li>
                  <li>• Battery optimization</li>
                  <li>• Orientation support</li>
                  <li>• Virtual keyboard handling</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Mobile Viewer Modal */}
      {showMobileViewer && (
        <div className="fixed inset-0 z-50 bg-black">
          <MobilePDFViewer
            file={mockPDFFile}
            initialPage={currentPage}
            onClose={() => setShowMobileViewer(false)}
            onPageChange={setCurrentPage}
            onZoomChange={setZoom}
            enableGestures={true}
            enableFullscreen={true}
            showToolbar={true}
            showPageIndicator={true}
          />
        </div>
      )}
    </div>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Demo Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smartphone className="h-6 w-6" />
            Advanced Mobile & Cross-Platform Optimizations Demo
          </CardTitle>
          <CardDescription>
            Comprehensive demonstration of next-generation mobile-first design, advanced touch gestures, responsive layouts, and cross-platform compatibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span className="text-sm">Mobile Optimized</span>
            </div>
            <div className="flex items-center gap-2">
              <Touch className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Touch: {typeof window !== 'undefined' && 'ontouchstart' in window ? 'Enabled' : 'Disabled'}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                Device: {layoutContext?.isMobile ? 'Mobile' : layoutContext?.isTablet ? 'Tablet' : 'Desktop'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Demo Interface */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as string)} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="viewer">Viewer</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          {renderOverviewTab()}
        </TabsContent>

        <TabsContent value="viewer">
          {renderViewerTab()}
        </TabsContent>

        <TabsContent value="performance">
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Analytics
                </CardTitle>
                <CardDescription>
                  Monitor mobile performance and optimization metrics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Performance Metrics */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">Core Metrics</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">First Contentful Paint</span>
                        <Badge variant="outline">&lt; 1.2s</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Largest Contentful Paint</span>
                        <Badge variant="outline">&lt; 2.5s</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Cumulative Layout Shift</span>
                        <Badge variant="outline">&lt; 0.1</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">First Input Delay</span>
                        <Badge variant="outline">&lt; 100ms</Badge>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Specific */}
                  <div className="space-y-4">
                    <h4 className="font-semibold">Mobile Optimizations</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Touch Response Time</span>
                        <Badge variant="outline">&lt; 50ms</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Gesture Recognition</span>
                        <Badge variant="outline">95%</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Battery Impact</span>
                        <Badge variant="outline">Low</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Memory Usage</span>
                        <Badge variant="outline">&lt; 50MB</Badge>
                      </div>
                    </div>
                  </div>
                </div>

                <Separator className="my-6" />

                {/* Optimization Features */}
                <div className="space-y-4">
                  <h4 className="font-semibold">Optimization Features</h4>
                  <div className="text-sm text-muted-foreground space-y-2">
                    <p>• Hardware-accelerated rendering with GPU optimization</p>
                    <p>• Intelligent caching and memory management</p>
                    <p>• Adaptive quality based on device capabilities</p>
                    <p>• Battery-aware processing and background optimization</p>
                    <p>• Network-aware loading and progressive enhancement</p>
                    <p>• Touch-optimized interaction patterns</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
