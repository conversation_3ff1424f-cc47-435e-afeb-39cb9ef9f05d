import React, { useState } from "react";
import {
  PDFSimplePage,
  PDFSearch,
  PDFFormManager,
  PDFFloatingToolbar,
  PDFSidebar,
  PDFOCREngine,
  PDFDigitalSignature,
  PDFImageExtractor,
} from "@/components";
import type { OCRResult } from "@/components/tools/pdf-ocr-engine";
import type { ExtractedImage } from "@/components/tools/pdf-image-extractor";

/**
 * Examples demonstrating how to use consolidated components with enhanced features
 */

// Example 1: Enhanced PDF Page with Feature Toggles
export const EnhancedPageExample: React.FC = () => {
  const [enableAnnotations, setEnableAnnotations] = useState(false);
  const [enableForms, setEnableForms] = useState(false);
  const [enableTextSelection, setEnableTextSelection] = useState(true);
  const [searchText, setSearchText] = useState("");

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">
        Enhanced PDF Page with Feature Toggles
      </h3>

      {/* Feature toggle controls */}
      <div className="flex gap-4 p-4 bg-muted rounded">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={enableAnnotations}
            onChange={(e) => setEnableAnnotations(e.target.checked)}
          />
          Enable Annotations
        </label>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={enableForms}
            onChange={(e) => setEnableForms(e.target.checked)}
          />
          Enable Forms
        </label>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={enableTextSelection}
            onChange={(e) => setEnableTextSelection(e.target.checked)}
          />
          Enable Text Selection
        </label>
      </div>

      {/* Consolidated PDF Page Component */}
      <PDFSimplePage
        pageNumber={1}
        scale={1.0}
        rotation={0}
        // Enhanced features via props
        enableAnnotations={enableAnnotations}
        enableForms={enableForms}
        enableTextSelection={enableTextSelection}
        enableSearch={true}
        searchText={searchText}
        onTextSelected={(selection) => {
          console.log("Text selected:", selection);
        }}
        onSearch={(text) => {
          setSearchText(text);
          console.log("Searching for:", text);
        }}
        onBookmark={() => {
          console.log("Bookmark added for page 1");
        }}
      />
    </div>
  );
};

// Example 2: Consolidated Search with Different Modes
export const ConsolidatedSearchExample: React.FC = () => {
  const [searchText, setSearchText] = useState("");
  const [searchMode, setSearchMode] = useState<
    "simple" | "enhanced" | "unified"
  >("simple");
  const [searchOptions, setSearchOptions] = useState({
    caseSensitive: false,
    wholeWords: false,
  });

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Consolidated Search Component</h3>

      {/* Search mode selector */}
      <div className="flex gap-2 p-4 bg-muted rounded">
        <label>Search Mode:</label>
        <select
          value={searchMode}
          onChange={(e) => setSearchMode(e.target.value as "simple" | "enhanced" | "unified")}
          className="px-2 py-1 border rounded"
        >
          <option value="simple">Simple</option>
          <option value="enhanced">Enhanced</option>
          <option value="unified">Unified</option>
        </select>
      </div>

      {/* Single search component with configurable mode */}
      <PDFSearch
        searchText={searchText}
        onSearchChange={setSearchText}
        onClose={() => console.log("Search closed")}
        // Mode configuration
        variant={searchMode}
        searchOptions={searchOptions}
        onSearchOptionsChange={setSearchOptions}
        // Enhanced features
        onSearchResults={(results) => {
          console.log(`Found ${results.length} search results`);
        }}
        onPageSelect={(page) => {
          console.log(`Navigate to page ${page}`);
        }}
        onCurrentSearchIndex={(index) => {
          console.log(`Current search result: ${index}`);
        }}
      />
    </div>
  );
};

// Example 3: Enhanced Form Manager with Advanced Features
export const EnhancedFormExample: React.FC = () => {
  const [formMode, setFormMode] = useState<"view" | "edit" | "design">("edit");
  const [enableValidation, setEnableValidation] = useState(true);
  const [enableTemplates, setEnableTemplates] = useState(true);

  const enhancedFormFields = [
    {
      id: "email",
      type: "email" as const,
      name: "userEmail",
      label: "Email Address",
      value: "",
      readonly: false,
      pageNumber: 1,
      position: { pageNumber: 1, x: 100, y: 200, width: 200, height: 30 },
      appearance: {
        fontSize: 12,
        fontColor: "#000000",
        backgroundColor: "#ffffff",
        borderColor: "#cccccc",
        borderWidth: 1,
      },
      metadata: {
        placeholder: "Enter your email",
        created: Date.now(),
        modified: Date.now(),
      },
      required: true,
    },
    {
      id: "phone",
      type: "phone" as const,
      name: "userPhone",
      label: "Phone Number",
      value: "",
      readonly: false,
      pageNumber: 1,
      position: { pageNumber: 1, x: 100, y: 250, width: 200, height: 30 },
      appearance: {
        fontSize: 12,
        fontColor: "#000000",
        backgroundColor: "#ffffff",
        borderColor: "#cccccc",
        borderWidth: 1,
      },
      metadata: {
        placeholder: "(###) ###-####",
        created: Date.now(),
        modified: Date.now(),
      },
      required: false,
    },
    {
      id: "signature",
      type: "signature" as const,
      name: "userSignature",
      label: "Digital Signature",
      value: "",
      readonly: false,
      pageNumber: 1,
      position: { pageNumber: 1, x: 100, y: 300, width: 300, height: 80 },
      appearance: {
        fontSize: 12,
        fontColor: "#000000",
        backgroundColor: "#ffffff",
        borderColor: "#cccccc",
        borderWidth: 1,
      },
      metadata: {
        created: Date.now(),
        modified: Date.now(),
      },
      required: true,
    },
  ];



  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Enhanced Form Manager</h3>

      {/* Form configuration controls */}
      <div className="flex gap-4 p-4 bg-muted rounded">
        <label>
          Mode:
          <select
            value={formMode}
            onChange={(e) => setFormMode(e.target.value as "view" | "edit" | "design")}
            className="ml-2 px-2 py-1 border rounded"
          >
            <option value="view">View</option>
            <option value="edit">Edit</option>
            <option value="design">Design</option>
          </select>
        </label>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={enableValidation}
            onChange={(e) => setEnableValidation(e.target.checked)}
          />
          Enable Validation
        </label>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={enableTemplates}
            onChange={(e) => setEnableTemplates(e.target.checked)}
          />
          Enable Templates
        </label>
      </div>

      {/* Enhanced Form Manager */}
      <PDFFormManager
        pdfDocument={null} // Would be actual PDF document
        mode={formMode}
        enableValidation={enableValidation}
        enableTemplates={enableTemplates}
        formFields={enhancedFormFields}
        // validationRules={validationRules} // Commented out due to interface mismatch
        onFormFieldsChange={(fields) => {
          console.log("Form fields changed:", fields);
        }}
        onFormDataChange={(data) => {
          console.log("Form data changed:", data);
        }}
        onFieldAdd={(field) => {
          console.log("Field added:", field);
        }}
        onFieldUpdate={(id, field) => {
          console.log("Field updated:", id, field);
        }}
        onFormSave={(data) => {
          console.log("Form saved:", data);
        }}
      />
    </div>
  );
};

// Example 4: Optimized Navigation Components
export const OptimizedNavigationExample: React.FC = () => {
  const [scale, setScale] = useState(1.0);
  const [page, setPage] = useState(1);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [adaptiveLayout, setAdaptiveLayout] = useState(true);
  const [performanceMode, setPerformanceMode] = useState(false);

  const toolbarGroups = [
    {
      id: "navigation",
      priority: "primary" as const,
      items: [
        {
          id: "prev",
          icon: () => <span>←</span>,
          label: "Previous Page",
          action: () => setPage(Math.max(1, page - 1)),
          disabled: page <= 1,
        },
        {
          id: "next",
          icon: () => <span>→</span>,
          label: "Next Page",
          action: () => setPage(page + 1),
          disabled: false,
        },
      ],
    },
  ];

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Optimized Navigation Components</h3>

      {/* Navigation configuration */}
      <div className="flex gap-4 p-4 bg-muted rounded">
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={adaptiveLayout}
            onChange={(e) => setAdaptiveLayout(e.target.checked)}
          />
          Adaptive Layout
        </label>
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={performanceMode}
            onChange={(e) => setPerformanceMode(e.target.checked)}
          />
          Performance Mode
        </label>
      </div>

      {/* Optimized Floating Toolbar */}
      <PDFFloatingToolbar
        selectedTool={null}
        onToolSelect={(tool) => console.log("Tool selected:", tool)}
        selectedColor="#FFEB3B"
        onColorChange={(color) => console.log("Color changed:", color)}
        onPrevPage={() => setPage(Math.max(1, page - 1))}
        onNextPage={() => setPage(page + 1)}
        canGoToPrev={page > 1}
        canGoToNext={page < 10} // Assuming 10 pages
        pageNumber={page}
        numPages={10}
        onPageChange={setPage}
        scale={scale}
        onScaleChange={setScale}
        rotation={0}
        onRotationChange={() => {}}
        // Enhanced features
        toolbarGroups={toolbarGroups}
        adaptiveLayout={adaptiveLayout}
        performanceMode={performanceMode}
        onAddBookmark={() => console.log("Bookmark added")}
        onDownload={() => console.log("Download started")}
      />

      {/* Enhanced Sidebar */}
      <PDFSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        activeTab="outline"
        onTabChange={(tab) => console.log("Tab changed:", tab)}
        currentMode="reading"
        onModeChange={(mode) => console.log("Mode changed:", mode)}
        // Enhanced features
        adaptiveLayout={adaptiveLayout}
        performanceMode={performanceMode}
        pdfDocument={null}
        numPages={10}
        currentPage={page}
        outline={[]}
        currentScale={scale}
        searchText=""
        onSearchChange={() => {}}
        onPageSelect={setPage}
        bookmarks={[]}
        onAddBookmark={() => {}}
        onRemoveBookmark={() => {}}
        onUpdateBookmark={() => {}}
        annotations={[]}
        selectedTool="select"
        onToolSelect={() => {}}
        onAnnotationAdd={() => {}}
        onAnnotationUpdate={() => {}}
        onAnnotationDelete={() => {}}
        formFields={[]}
        formData={{}}
        onFormFieldsChange={() => {}}
        onFormDataChange={() => {}}
      />
    </div>
  );
};

// Example 5: Enhanced Tools Usage
export const EnhancedToolsExample: React.FC = () => {
  const [ocrResults, setOcrResults] = useState<OCRResult[] | null>(null);
  const [extractedImages, setExtractedImages] = useState<ExtractedImage[]>([]);
  const [signatures, setSignatures] = useState<unknown[]>([]);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Enhanced Tools</h3>

      {/* Enhanced OCR Engine */}
      <div className="p-4 border rounded">
        <h4 className="font-medium mb-2">Enhanced OCR Engine</h4>
        <PDFOCREngine
          pdfDocument={null} // Would be actual PDF document
          numPages={10}
          onTextExtracted={(result: OCRResult[]) => {
            setOcrResults(result);
            console.log("OCR completed:", result);
          }}
          // Enhanced features
          defaultLanguage="eng"
          enableAutoDetect={true}
        />
        {ocrResults && ocrResults.length > 0 ? (
          <div className="mt-2 p-2 bg-muted rounded text-sm">
            OCR Results: {ocrResults[0].confidence}% confidence
          </div>
        ) : null}
      </div>

      {/* Enhanced Image Extractor */}
      <div className="p-4 border rounded">
        <h4 className="font-medium mb-2">Enhanced Image Extractor</h4>
        <PDFImageExtractor
          pdfDocument={null} // Would be actual PDF document
          numPages={10}
          onImageExtracted={(images: ExtractedImage[]) => {
            setExtractedImages(images);
            console.log("Images extracted:", images);
          }}
        />
        {extractedImages.length > 0 && (
          <div className="mt-2 p-2 bg-muted rounded text-sm">
            Extracted {extractedImages.length} images
          </div>
        )}
      </div>

      {/* Enhanced Digital Signature */}
      <div className="p-4 border rounded">
        <h4 className="font-medium mb-2">Enhanced Digital Signature</h4>
        <PDFDigitalSignature
          pdfDocument={null} // Would be actual PDF document
          numPages={10}
          currentPage={1}
          onSignatureAdd={(signature) => {
            setSignatures([...signatures, signature]);
            console.log("Signature added:", signature);
          }}
        />
        {signatures.length > 0 && (
          <div className="mt-2 p-2 bg-muted rounded text-sm">
            {signatures.length} signature(s) added
          </div>
        )}
      </div>
    </div>
  );
};

// Example 6: Migration Pattern Example
export const MigrationPatternExample: React.FC = () => {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold">Migration Patterns</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Before: Multiple Components */}
        <div className="p-4 border rounded">
          <h4 className="font-medium mb-2 text-red-600">
            ❌ Before (Multiple Components)
          </h4>
          <pre className="text-sm bg-muted p-2 rounded overflow-x-auto">
            {`// Multiple imports needed
import { PDFEnhancedPageWithAnnotations } from '@/components/core'
import { PDFSearchEnhanced } from '@/components/search'
import { EnhancedFormManager } from '@/components/forms'
import { OptimizedToolbar } from '@/components/navigation'

// Multiple components to manage
<PDFEnhancedPageWithAnnotations />
<PDFSearchEnhanced />
<EnhancedFormManager />
<OptimizedToolbar />`}
          </pre>
        </div>

        {/* After: Consolidated Components */}
        <div className="p-4 border rounded">
          <h4 className="font-medium mb-2 text-green-600">
            ✅ After (Consolidated)
          </h4>
          <pre className="text-sm bg-muted p-2 rounded overflow-x-auto">
            {`// Single imports with feature toggles
import { 
  PDFSimplePage, 
  PDFSearch, 
  PDFFormManager, 
  PDFFloatingToolbar 
} from '@/components'

// Single components with enhanced features
<PDFSimplePage enableAnnotations={true} />
<PDFSearch variant="enhanced" />
<PDFFormManager enableValidation={true} />
<PDFFloatingToolbar adaptiveLayout={true} />`}
          </pre>
        </div>
      </div>

      <div className="p-4 bg-blue-50 border border-blue-200 rounded">
        <h4 className="font-medium mb-2">💡 Migration Benefits</h4>
        <ul className="text-sm space-y-1">
          <li>• Reduced bundle size - no duplicate component code</li>
          <li>• Simplified imports - single component per feature</li>
          <li>• Feature toggles - enable/disable functionality as needed</li>
          <li>• Backward compatibility - existing code continues to work</li>
          <li>• Enhanced performance - optimized consolidated components</li>
        </ul>
      </div>
    </div>
  );
};

// Main example component that showcases all patterns
export const ConsolidatedComponentShowcase: React.FC = () => {
  const [activeExample, setActiveExample] = useState("page");

  const examples = [
    { id: "page", label: "Enhanced Page", component: EnhancedPageExample },
    {
      id: "search",
      label: "Consolidated Search",
      component: ConsolidatedSearchExample,
    },
    { id: "forms", label: "Enhanced Forms", component: EnhancedFormExample },
    {
      id: "navigation",
      label: "Optimized Navigation",
      component: OptimizedNavigationExample,
    },
    { id: "tools", label: "Enhanced Tools", component: EnhancedToolsExample },
    {
      id: "migration",
      label: "Migration Patterns",
      component: MigrationPatternExample,
    },
  ];

  const ActiveComponent =
    examples.find((ex) => ex.id === activeExample)?.component ||
    EnhancedPageExample;

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-2xl font-bold mb-6">
        Consolidated Component Examples
      </h2>

      {/* Example selector */}
      <div className="flex flex-wrap gap-2 mb-6">
        {examples.map((example) => (
          <button
            key={example.id}
            onClick={() => setActiveExample(example.id)}
            className={`px-4 py-2 rounded transition-colors ${
              activeExample === example.id
                ? "bg-primary text-primary-foreground"
                : "bg-muted hover:bg-muted/80"
            }`}
          >
            {example.label}
          </button>
        ))}
      </div>

      {/* Active example */}
      <div className="border rounded-lg p-6">
        <ActiveComponent />
      </div>
    </div>
  );
};

export default ConsolidatedComponentShowcase;
