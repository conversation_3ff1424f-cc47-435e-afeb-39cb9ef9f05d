// Component examples and usage patterns
export { default as OptimizedPDFViewerExample } from "./optimized-pdf-viewer";
export { default as VirtualPDFDemo } from "./virtual-pdf-demo";
export { default as AdvancedCachingDemo } from "./advanced-caching-demo";
export { default as EnhancedSearchDemo } from "./enhanced-search-demo";
export { default as OCRIntegrationDemo } from "./ocr-integration-demo";
export { default as AdvancedAnnotationDemo } from "./advanced-annotation-demo";
export { default as LayoutOptimizationDemo } from "./layout-optimization-demo";
export { default as MobileResponsiveDemo } from "./mobile-responsive-demo";
export {
  default as ConsolidatedComponentShowcase,
  EnhancedPageExample,
  ConsolidatedSearchExample,
  EnhancedFormExample,
  OptimizedNavigationExample,
  EnhancedToolsExample,
  MigrationPatternExample,
} from "./consolidated-component-examples";

// Export types for examples
export type { OptimizedPDFViewerProps } from "./optimized-pdf-viewer";
