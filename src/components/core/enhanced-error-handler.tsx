"use client";

import React from 'react';
import { toast } from 'sonner';
import {
  AlertCircle,
  Wifi,
  FileX,
  Shield,
  HardDrive,
  ExternalLink,
  Info
} from 'lucide-react';

interface ErrorHandlerOptions {
  showToast?: boolean;
  showRetryButton?: boolean;
  showAlternativeActions?: boolean;
  onRetry?: () => void;
  onSelectFile?: () => void;
  onSelectUrl?: () => void;
}

// Enhanced error messages with detailed descriptions and solutions
const ERROR_DETAILS: Record<DocumentErrorType, {
  title: string;
  description: string;
  icon: React.ReactNode;
  severity: 'error' | 'warning' | 'info';
  solutions: string[];
  technicalInfo?: string;
}> = {
  NETWORK_ERROR: {
    title: 'Network Connection Error',
    description: 'Unable to download the PDF file due to network issues.',
    icon: <Wifi className="h-5 w-5" />,
    severity: 'warning',
    solutions: [
      'Check your internet connection',
      'Try again in a few moments',
      'Verify the URL is accessible',
      'Check if the server is responding'
    ],
    technicalInfo: 'This usually occurs when the network request fails or times out.'
  },
  FILE_NOT_FOUND: {
    title: 'File Not Found',
    description: 'The PDF file could not be located at the specified path or URL.',
    icon: <FileX className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Verify the file path or URL is correct',
      'Check if the file has been moved or deleted',
      'Ensure you have the correct permissions',
      'Try browsing for the file manually'
    ],
    technicalInfo: 'HTTP 404 error or file system path not found.'
  },
  CORRUPTED_FILE: {
    title: 'Corrupted or Invalid PDF',
    description: 'The file appears to be damaged or is not a valid PDF document.',
    icon: <AlertCircle className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Try opening the file in another PDF viewer',
      'Re-download the file if it came from the internet',
      'Check if the file was completely uploaded',
      'Contact the file provider for a new copy'
    ],
    technicalInfo: 'PDF structure is malformed or file headers are invalid.'
  },
  PERMISSION_DENIED: {
    title: 'Access Denied',
    description: 'You do not have permission to access this PDF file.',
    icon: <Shield className="h-5 w-5" />,
    severity: 'warning',
    solutions: [
      'Check if you need to log in or authenticate',
      'Verify you have read permissions for the file',
      'Contact the file owner for access',
      'Try accessing from a different account'
    ],
    technicalInfo: 'HTTP 403 error or file system permission denied.'
  },
  MEMORY_ERROR: {
    title: 'Insufficient Memory',
    description: 'Not enough memory available to load this PDF document.',
    icon: <HardDrive className="h-5 w-5" />,
    severity: 'warning',
    solutions: [
      'Close other open documents or browser tabs',
      'Restart your browser',
      'Try opening a smaller PDF file first',
      'Free up system memory by closing other applications'
    ],
    technicalInfo: 'Browser memory limit exceeded or system resources exhausted.'
  },
  UNSUPPORTED_FORMAT: {
    title: 'Unsupported File Format',
    description: 'This file format is not supported by the PDF viewer.',
    icon: <FileX className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Ensure the file is a valid PDF document',
      'Convert the file to PDF format',
      'Check the file extension is .pdf',
      'Try opening with a different application'
    ],
    technicalInfo: 'File MIME type or format not recognized as PDF.'
  },
  WORKER_ERROR: {
    title: 'PDF Worker Error',
    description: 'The PDF processing worker failed to load or execute.',
    icon: <AlertCircle className="h-5 w-5" />,
    severity: 'warning',
    solutions: [
      'Refresh the page to reload the worker',
      'Check your internet connection',
      'Clear browser cache and try again',
      'Try using a different browser'
    ],
    technicalInfo: 'PDF.js worker script failed to load or initialize.'
  },
  CORS_ERROR: {
    title: 'Cross-Origin Request Blocked',
    description: 'The PDF file cannot be loaded due to cross-origin restrictions.',
    icon: <ExternalLink className="h-5 w-5" />,
    severity: 'warning',
    solutions: [
      'Upload the PDF file directly instead of using a URL',
      'Use a PDF from the same domain',
      'Contact the website administrator',
      'Try downloading the file first, then upload it'
    ],
    technicalInfo: 'CORS policy prevents loading the resource from external domain.'
  },
  FORMAT_ERROR: {
    title: 'Invalid PDF Format',
    description: 'The file appears to be corrupted or not a valid PDF.',
    icon: <FileX className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Verify the file is a valid PDF document',
      'Try opening with another PDF viewer',
      'Re-download or re-create the PDF file',
      'Check if the file was corrupted during transfer'
    ],
    technicalInfo: 'PDF file structure is invalid or corrupted.'
  },
  UNKNOWN_ERROR: {
    title: 'Unknown Error',
    description: 'An unexpected error occurred while processing the PDF.',
    icon: <AlertCircle className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Try refreshing the page',
      'Check your internet connection',
      'Try a different PDF file',
      'Contact support if the problem persists'
    ],
    technicalInfo: 'Unhandled error occurred during PDF processing.'
  },
  LOAD_FAILED: {
    title: 'Failed to Load Document',
    description: 'An unexpected error occurred while loading the PDF.',
    icon: <AlertCircle className="h-5 w-5" />,
    severity: 'error',
    solutions: [
      'Try refreshing the page',
      'Clear your browser cache',
      'Try a different browser',
      'Contact support if the problem persists'
    ],
    technicalInfo: 'Generic loading error - check browser console for details.'
  }
};

export class EnhancedErrorHandler {
  static categorizeAndHandle(
    error: Error, 
    context: string = 'document',
    options: ErrorHandlerOptions = {}
  ): DocumentError {
    const categorizedError = categorizeError(error);
    const errorDetails = ERROR_DETAILS[categorizedError.type];
    
    if (options.showToast !== false) {
      this.showErrorToast(categorizedError, errorDetails, options);
    }
    
    // Log detailed error information for debugging
    console.group(`🔴 PDF Error: ${errorDetails.title}`);
    console.error('Original error:', error);
    console.info('Error type:', categorizedError.type);
    console.info('Context:', context);
    console.info('Can retry:', categorizedError.canRetry);
    console.info('Suggested action:', categorizedError.suggestedAction);
    if (errorDetails.technicalInfo) {
      console.info('Technical info:', errorDetails.technicalInfo);
    }
    console.groupEnd();
    
    return categorizedError;
  }
  
  private static showErrorToast(
    error: DocumentError, 
    details: typeof ERROR_DETAILS[DocumentErrorType],
    options: ErrorHandlerOptions
  ) {
    const toastOptions = {
      description: error.message,
      duration: error.canRetry ? 8000 : 12000,
      action: error.canRetry && options.onRetry ? {
        label: 'Retry',
        onClick: options.onRetry
      } : undefined
    };
    
    switch (details.severity) {
      case 'error':
        toast.error(details.title, toastOptions);
        break;
      case 'warning':
        toast.warning(details.title, toastOptions);
        break;
      case 'info':
        toast.info(details.title, toastOptions);
        break;
    }
  }
  
  static getErrorDetails(errorType: DocumentErrorType) {
    return ERROR_DETAILS[errorType];
  }
  
  static createUserFriendlyMessage(error: DocumentError): string {
    const details = ERROR_DETAILS[error.type];
    return `${details.title}: ${details.description}`;
  }
  
  static getSolutions(errorType: DocumentErrorType): string[] {
    return ERROR_DETAILS[errorType].solutions;
  }
  
  static canRecover(errorType: DocumentErrorType): boolean {
    return ERROR_DETAILS[errorType].severity !== 'error' || 
           errorType === DocumentErrorType.NETWORK_ERROR ||
           errorType === DocumentErrorType.MEMORY_ERROR;
  }
  
  // Utility method to show a comprehensive error dialog
  static showDetailedError(
    error: DocumentError,
    documentTitle: string,
    onAction?: (action: 'retry' | 'file' | 'url' | 'close') => void
  ) {
    const details = ERROR_DETAILS[error.type];
    
    // Create a detailed toast with multiple action buttons
    toast.error(details.title, {
      description: (
        <div className="space-y-2">
          <p className="text-sm">{error.message}</p>
          <p className="text-xs text-muted-foreground">
            Document: {documentTitle}
          </p>
          {details.solutions.length > 0 && (
            <div className="text-xs">
              <p className="font-medium">Suggestions:</p>
              <ul className="list-disc list-inside space-y-1">
                {details.solutions.slice(0, 2).map((solution, index) => (
                  <li key={index}>{solution}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      ),
      duration: 15000,
      action: onAction ? {
        label: error.canRetry ? 'Retry' : 'Options',
        onClick: () => onAction(error.canRetry ? 'retry' : 'close')
      } : undefined
    });
  }
  
  // Method to handle progressive error recovery
  static handleProgressiveRecovery(
    error: DocumentError,
    attemptCount: number,
    maxAttempts: number = 3
  ): 'retry' | 'fallback' | 'abort' {
    if (!error.canRetry || attemptCount >= maxAttempts) {
      return 'abort';
    }
    
    // For network errors, try a few times before giving up
    if (error.type === DocumentErrorType.NETWORK_ERROR && attemptCount < 2) {
      return 'retry';
    }
    
    // For memory errors, suggest fallback after first attempt
    if (error.type === DocumentErrorType.MEMORY_ERROR && attemptCount >= 1) {
      return 'fallback';
    }
    
    return attemptCount < 2 ? 'retry' : 'fallback';
  }
}

// React component for displaying error information
interface ErrorDisplayProps {
  error: DocumentError;
  documentTitle?: string;
  showSolutions?: boolean;
  compact?: boolean;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  documentTitle,
  showSolutions = true,
  compact = false
}) => {
  const details = ERROR_DETAILS[error.type];
  
  if (compact) {
    return (
      <div className="flex items-center gap-2 text-sm text-destructive">
        {details.icon}
        <span>{details.title}</span>
      </div>
    );
  }
  
  return (
    <div className="space-y-3 p-4 border border-destructive/20 rounded-lg bg-destructive/5">
      <div className="flex items-start gap-3">
        <div className="text-destructive mt-0.5">
          {details.icon}
        </div>
        <div className="flex-1 space-y-1">
          <h4 className="font-medium text-destructive">{details.title}</h4>
          <p className="text-sm text-muted-foreground">{error.message}</p>
          {documentTitle && (
            <p className="text-xs text-muted-foreground">
              Document: {documentTitle}
            </p>
          )}
        </div>
      </div>
      
      {showSolutions && details.solutions.length > 0 && (
        <div className="space-y-2">
          <h5 className="text-sm font-medium flex items-center gap-1">
            <Info className="h-3 w-3" />
            Suggested Solutions:
          </h5>
          <ul className="text-xs space-y-1 text-muted-foreground">
            {details.solutions.map((solution, index) => (
              <li key={index} className="flex items-start gap-1">
                <span className="text-primary">•</span>
                <span>{solution}</span>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default EnhancedErrorHandler;
