"use client";

import type React from "react";
import PDFSimplePage from "./pdf-simple-page";

interface PDFPageWrapperProps {
  pageNumber: number;
  scale: number;
  rotation: number;
  pdfDocument: unknown;
  searchText: string;
  searchOptions: { caseSensitive: boolean; wholeWords: boolean };
  currentSearchPageIndex: number;
  searchResults: Array<{ pageIndex: number; textItems: unknown[] }>;
  className?: string;
}

export default function PDFPageWrapper({
  pageNumber,
  scale,
  rotation,
  pdfDocument,
  searchText,
  searchOptions,
  currentSearchPageIndex,
  searchResults,
  className,
}: PDFPageWrapperProps) {
  return (
    <PDFSimplePage
      pageNumber={pageNumber}
      scale={scale}
      rotation={rotation}
      pdfDocument={pdfDocument}
      className={className}
      searchText={searchText}
      searchOptions={searchOptions}
      currentSearchPageIndex={currentSearchPageIndex}
      searchResults={searchResults}
      enableAnnotations={false}
      enableForms={false}
      enableTextSelection={true}
      enableSearch={true}
      enableContextMenu={false}
    />
  );
}
