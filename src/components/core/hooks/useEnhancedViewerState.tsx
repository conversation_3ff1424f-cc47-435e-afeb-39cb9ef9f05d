import { useState, useC<PERSON>back, useReducer, useEffect, useMemo } from 'react'
import { ViewerMode } from '../../navigation/pdf-sidebar'

// Enhanced state interfaces
interface ViewerState {
  // Document state
  pageNumber: number
  numPages: number
  scale: number
  rotation: number
  isLoading: boolean
  
  // UI state
  sidebarOpen: boolean
  activeTab: string
  isFullscreen: boolean
  currentMode: ViewerMode
  selectedTool: string | null
  
  // Search state
  searchText: string
  searchResults: unknown[]
  currentSearchIndex: number
  
  // Tool states
  annotationMode: boolean
  formMode: boolean
  selectionMode: boolean
  
  // User preferences
  autoHide: boolean
  gesturesEnabled: boolean
  keyboardShortcuts: boolean
  compactMode: boolean
}

interface ViewerPreferences {
  defaultMode: ViewerMode
  defaultZoom: number
  autoSaveDrafts: boolean
  showThumbnails: boolean
  enableAnimations: boolean
  theme: 'light' | 'dark' | 'auto'
}

// Action types for state management
type ViewerAction = 
  | { type: 'SET_PAGE'; payload: number }
  | { type: 'SET_NUM_PAGES'; payload: number }
  | { type: 'SET_SCALE'; payload: number }
  | { type: 'SET_ROTATION'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_ACTIVE_TAB'; payload: string }
  | { type: 'TOGGLE_FULLSCREEN' }
  | { type: 'SET_MODE'; payload: ViewerMode }
  | { type: 'SET_TOOL'; payload: string | null }
  | { type: 'SET_SEARCH'; payload: string }
  | { type: 'SET_SEARCH_RESULTS'; payload: unknown[] }
  | { type: 'SET_SEARCH_INDEX'; payload: number }
  | { type: 'TOGGLE_ANNOTATION_MODE' }
  | { type: 'TOGGLE_FORM_MODE' }
  | { type: 'TOGGLE_SELECTION_MODE' }
  | { type: 'UPDATE_PREFERENCES'; payload: Partial<ViewerPreferences> }
  | { type: 'RESET_TO_DEFAULTS' }

// Interaction history for undo/redo
interface InteractionHistory {
  past: Partial<ViewerState>[]
  present: Partial<ViewerState>
  future: Partial<ViewerState>[]
}

const initialViewerState: ViewerState = {
  pageNumber: 1,
  numPages: 0,
  scale: 1.0,
  rotation: 0,
  isLoading: false,
  sidebarOpen: false,
  activeTab: 'outline',
  isFullscreen: false,
  currentMode: 'reading',
  selectedTool: null,
  searchText: '',
  searchResults: [],
  currentSearchIndex: -1,
  annotationMode: false,
  formMode: false,
  selectionMode: false,
  autoHide: true,
  gesturesEnabled: true,
  keyboardShortcuts: true,
  compactMode: false
}

const initialPreferences: ViewerPreferences = {
  defaultMode: 'reading',
  defaultZoom: 1.0,
  autoSaveDrafts: true,
  showThumbnails: true,
  enableAnimations: true,
  theme: 'auto'
}

// State reducer for complex state management
function viewerReducer(state: ViewerState, action: ViewerAction): ViewerState {
  switch (action.type) {
    case 'SET_PAGE':
      return { ...state, pageNumber: Math.max(1, Math.min(action.payload, state.numPages)) }
    
    case 'SET_NUM_PAGES':
      return { ...state, numPages: action.payload }
    
    case 'SET_SCALE':
      return { ...state, scale: Math.max(0.1, Math.min(5.0, action.payload)) }
    
    case 'SET_ROTATION':
      return { ...state, rotation: action.payload % 360 }
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen }
    
    case 'SET_ACTIVE_TAB':
      return { ...state, activeTab: action.payload, sidebarOpen: true }
    
    case 'TOGGLE_FULLSCREEN':
      return { ...state, isFullscreen: !state.isFullscreen }
    
    case 'SET_MODE':
      // Auto-switch tools and tabs when mode changes
      const newState = { ...state, currentMode: action.payload }
      
      switch (action.payload) {
        case 'annotating':
          newState.selectedTool = 'highlight'
          newState.activeTab = 'annotations'
          newState.annotationMode = true
          break
        case 'form-filling':
          newState.selectedTool = 'form-fill'
          newState.activeTab = 'forms'
          newState.formMode = true
          break
        case 'reviewing':
          newState.activeTab = 'annotations'
          break
        default:
          newState.selectedTool = null
          newState.activeTab = 'outline'
          newState.annotationMode = false
          newState.formMode = false
      }
      
      return newState
    
    case 'SET_TOOL':
      return { ...state, selectedTool: action.payload }
    
    case 'SET_SEARCH':
      return { 
        ...state, 
        searchText: action.payload,
        currentSearchIndex: action.payload ? 0 : -1 
      }
    
    case 'SET_SEARCH_RESULTS':
      return { 
        ...state, 
        searchResults: action.payload,
        currentSearchIndex: action.payload.length > 0 ? 0 : -1 
      }
    
    case 'SET_SEARCH_INDEX':
      return { 
        ...state, 
        currentSearchIndex: Math.max(-1, Math.min(action.payload, state.searchResults.length - 1))
      }
    
    case 'TOGGLE_ANNOTATION_MODE':
      return { 
        ...state, 
        annotationMode: !state.annotationMode,
        currentMode: !state.annotationMode ? 'annotating' : 'reading'
      }
    
    case 'TOGGLE_FORM_MODE':
      return { 
        ...state, 
        formMode: !state.formMode,
        currentMode: !state.formMode ? 'form-filling' : 'reading'
      }
    
    case 'TOGGLE_SELECTION_MODE':
      return { ...state, selectionMode: !state.selectionMode }
    
    case 'RESET_TO_DEFAULTS':
      return { ...initialViewerState, numPages: state.numPages }
    
    default:
      return state
  }
}

// Enhanced viewer state management hook
export const useEnhancedViewerState = (initialPage = 1) => {
  const [state, dispatch] = useReducer(viewerReducer, {
    ...initialViewerState,
    pageNumber: initialPage
  })
  
  const [preferences, setPreferences] = useState<ViewerPreferences>(initialPreferences)
  const [history, setHistory] = useState<InteractionHistory>({
    past: [],
    present: {},
    future: []
  })

  // Load preferences from localStorage
  useEffect(() => {
    try {
      const savedPreferences = localStorage.getItem('pdf-viewer-preferences')
      if (savedPreferences) {
        setPreferences({ ...initialPreferences, ...JSON.parse(savedPreferences) })
      }
    } catch (error) {
      console.warn('Failed to load preferences:', error)
    }
  }, [])

  // Save preferences to localStorage
  const updatePreferences = useCallback((newPreferences: Partial<ViewerPreferences>) => {
    const updated = { ...preferences, ...newPreferences }
    setPreferences(updated)
    
    try {
      localStorage.setItem('pdf-viewer-preferences', JSON.stringify(updated))
    } catch (error) {
      console.warn('Failed to save preferences:', error)
    }
  }, [preferences])

  // History management for undo/redo
  const addToHistory = useCallback((change: Partial<ViewerState>) => {
    setHistory(prev => ({
      past: [...prev.past, prev.present],
      present: change,
      future: []
    }))
  }, [])

  const undo = useCallback(() => {
    setHistory(prev => {
      if (prev.past.length === 0) return prev
      
      const previous = prev.past[prev.past.length - 1]
      const newPast = prev.past.slice(0, prev.past.length - 1)
      
      return {
        past: newPast,
        present: previous,
        future: [prev.present, ...prev.future]
      }
    })
  }, [])

  const redo = useCallback(() => {
    setHistory(prev => {
      if (prev.future.length === 0) return prev
      
      const next = prev.future[0]
      const newFuture = prev.future.slice(1)
      
      return {
        past: [...prev.past, prev.present],
        present: next,
        future: newFuture
      }
    })
  }, [])

  // Navigation actions
  const navigation = useMemo(() => ({
    goToPage: (page: number) => {
      addToHistory({ pageNumber: state.pageNumber })
      dispatch({ type: 'SET_PAGE', payload: page })
    },
    nextPage: () => {
      if (state.pageNumber < state.numPages) {
        addToHistory({ pageNumber: state.pageNumber })
        dispatch({ type: 'SET_PAGE', payload: state.pageNumber + 1 })
      }
    },
    prevPage: () => {
      if (state.pageNumber > 1) {
        addToHistory({ pageNumber: state.pageNumber })
        dispatch({ type: 'SET_PAGE', payload: state.pageNumber - 1 })
      }
    },
    firstPage: () => {
      addToHistory({ pageNumber: state.pageNumber })
      dispatch({ type: 'SET_PAGE', payload: 1 })
    },
    lastPage: () => {
      addToHistory({ pageNumber: state.pageNumber })
      dispatch({ type: 'SET_PAGE', payload: state.numPages })
    }
  }), [state.pageNumber, state.numPages, addToHistory])

  // View actions
  const view = useMemo(() => ({
    setScale: (scale: number) => {
      addToHistory({ scale: state.scale })
      dispatch({ type: 'SET_SCALE', payload: scale })
    },
    zoomIn: () => {
      addToHistory({ scale: state.scale })
      dispatch({ type: 'SET_SCALE', payload: state.scale + 0.25 })
    },
    zoomOut: () => {
      addToHistory({ scale: state.scale })
      dispatch({ type: 'SET_SCALE', payload: state.scale - 0.25 })
    },
    resetZoom: () => {
      addToHistory({ scale: state.scale })
      dispatch({ type: 'SET_SCALE', payload: preferences.defaultZoom })
    },
    rotate: () => {
      addToHistory({ rotation: state.rotation })
      dispatch({ type: 'SET_ROTATION', payload: state.rotation + 90 })
    },
    toggleFullscreen: () => dispatch({ type: 'TOGGLE_FULLSCREEN' })
  }), [state.scale, state.rotation, preferences.defaultZoom, addToHistory])

  // UI actions
  const ui = useMemo(() => ({
    toggleSidebar: () => dispatch({ type: 'TOGGLE_SIDEBAR' }),
    setActiveTab: (tab: string) => dispatch({ type: 'SET_ACTIVE_TAB', payload: tab }),
    setMode: (mode: ViewerMode) => {
      addToHistory({ currentMode: state.currentMode })
      dispatch({ type: 'SET_MODE', payload: mode })
    },
    setTool: (tool: string | null) => dispatch({ type: 'SET_TOOL', payload: tool }),
    toggleAnnotationMode: () => dispatch({ type: 'TOGGLE_ANNOTATION_MODE' }),
    toggleFormMode: () => dispatch({ type: 'TOGGLE_FORM_MODE' }),
    toggleSelectionMode: () => dispatch({ type: 'TOGGLE_SELECTION_MODE' })
  }), [state.currentMode, addToHistory])

  // Search actions
  const search = useMemo(() => ({
    setSearchText: (text: string) => dispatch({ type: 'SET_SEARCH', payload: text }),
    setResults: (results: unknown[]) => dispatch({ type: 'SET_SEARCH_RESULTS', payload: results }),
    nextResult: () => {
      if (state.currentSearchIndex < state.searchResults.length - 1) {
        dispatch({ type: 'SET_SEARCH_INDEX', payload: state.currentSearchIndex + 1 })
      }
    },
    prevResult: () => {
      if (state.currentSearchIndex > 0) {
        dispatch({ type: 'SET_SEARCH_INDEX', payload: state.currentSearchIndex - 1 })
      }
    },
    clearSearch: () => {
      dispatch({ type: 'SET_SEARCH', payload: '' })
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] })
    }
  }), [state.currentSearchIndex, state.searchResults.length])

  // Document actions
  const document = useMemo(() => ({
    setNumPages: (numPages: number) => dispatch({ type: 'SET_NUM_PAGES', payload: numPages }),
    setLoading: (loading: boolean) => dispatch({ type: 'SET_LOADING', payload: loading }),
    reset: () => dispatch({ type: 'RESET_TO_DEFAULTS' })
  }), [])

  // Computed values
  const computed = useMemo(() => ({
    canGoToPrev: state.pageNumber > 1,
    canGoToNext: state.pageNumber < state.numPages,
    canUndo: history.past.length > 0,
    canRedo: history.future.length > 0,
    hasSearch: state.searchText.length > 0,
    hasSearchResults: state.searchResults.length > 0,
    isFirstPage: state.pageNumber === 1,
    isLastPage: state.pageNumber === state.numPages,
    zoomPercentage: Math.round(state.scale * 100),
    isMinZoom: state.scale <= 0.1,
    isMaxZoom: state.scale >= 5.0
  }), [state, history])

  return {
    // State
    state,
    preferences,
    computed,
    
    // Actions
    navigation,
    view,
    ui,
    search,
    document,
    
    // History
    undo,
    redo,
    
    // Preferences
    updatePreferences,
    
    // Raw dispatch for custom actions
    dispatch
  }
}

export type EnhancedViewerState = ReturnType<typeof useEnhancedViewerState>

export default useEnhancedViewerState