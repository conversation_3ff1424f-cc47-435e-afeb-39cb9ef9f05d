import { useEffect } from 'react'

interface KeyboardShortcutHandlers {
  onPrevPage: () => void
  onNextPage: () => void
  onOpenSearch: () => void
  onOpenBookmarks: () => void
  onAddBookmark: () => void
  onOpenAnnotations: () => void
  onOpenPrint: () => void
  onOpenThumbnails: () => void
  onOpenForms: () => void
  onOpenWorkflows: () => void
  onOpenOutline: () => void
  onCloseSidebar: () => void
  onClearSelection: () => void
}

export const usePDFKeyboardShortcuts = (handlers: KeyboardShortcutHandlers) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case "f":
            e.preventDefault()
            handlers.onOpenSearch()
            break
          case "b":
            e.preventDefault()
            handlers.onOpenBookmarks()
            break
          case "d":
            e.preventDefault()
            handlers.onAddBookmark()
            break
          case "a":
            e.preventDefault()
            handlers.onOpenAnnotations()
            break
          case "p":
            e.preventDefault()
            handlers.onOpenPrint()
            break
          case "t":
            e.preventDefault()
            handlers.onOpenThumbnails()
            break
          case "r":
            e.preventDefault()
            handlers.onOpenForms()
            break
          case "w":
            e.preventDefault()
            handlers.onOpenWorkflows()
            break
          case "1":
            e.preventDefault()
            handlers.onOpenOutline()
            break
          case "2":
            e.preventDefault()
            handlers.onOpenSearch()
            break
          case "3":
            e.preventDefault()
            handlers.onOpenAnnotations()
            break
          case "4":
            e.preventDefault()
            handlers.onOpenForms()
            break
        }
      }

      switch (e.key) {
        case "ArrowLeft":
          if (!e.ctrlKey && !e.metaKey) {
            e.preventDefault()
            handlers.onPrevPage()
          }
          break
        case "ArrowRight":
          if (!e.ctrlKey && !e.metaKey) {
            e.preventDefault()
            handlers.onNextPage()
          }
          break
        case "Escape":
          handlers.onCloseSidebar()
          handlers.onClearSelection()
          break
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [handlers])
}