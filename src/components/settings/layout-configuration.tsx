"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  Layout,
  Sidebar,
  Grid3X3,

  Monitor,
  Smartphone,
  Tablet,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface LayoutPreferences {
  // Layout modes
  defaultLayoutMode: 'single' | 'side-by-side' | 'grid' | 'overlay';
  enableMultiDocument: boolean;
  maxDocuments: number;
  
  // Sidebar preferences
  sidebarDefaultOpen: boolean;
  sidebarDefaultMode: 'full' | 'compact' | 'mini';
  sidebarAutoHide: boolean;
  sidebarAutoHideDelay: number;
  sidebarPosition: 'left' | 'right';
  
  // Header preferences
  headerMode: 'full' | 'compact' | 'minimal';
  showAdvancedTools: boolean;
  toolbarAutoCollapse: boolean;
  
  // Tab preferences
  tabDisplayMode: 'full' | 'compact' | 'minimal' | 'icons-only';
  maxVisibleTabs: number;
  enableTabReordering: boolean;
  showPinnedTabs: boolean;
  
  // Display preferences
  showBorders: boolean;
  compactSpacing: boolean;
  enableAnimations: boolean;
  darkMode: boolean;
  
  // Responsive behavior
  enableResponsiveLayout: boolean;
  enableAutoLayout: boolean;
  enableDensityControl: boolean;
  
  // Performance
  enableVirtualization: boolean;
  enableLazyLoading: boolean;
  maxRenderDistance: number;
  
  // Accessibility
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  fontSize: number;
  enableKeyboardNavigation: boolean;
}

interface LayoutConfigurationProps {
  preferences: LayoutPreferences;
  onPreferencesChange: (preferences: LayoutPreferences) => void;
  onReset?: () => void;
  className?: string;
}

const DEFAULT_PREFERENCES: LayoutPreferences = {
  defaultLayoutMode: 'single',
  enableMultiDocument: true,
  maxDocuments: 4,
  sidebarDefaultOpen: false,
  sidebarDefaultMode: 'compact',
  sidebarAutoHide: true,
  sidebarAutoHideDelay: 3000,
  sidebarPosition: 'left',
  headerMode: 'compact',
  showAdvancedTools: false,
  toolbarAutoCollapse: true,
  tabDisplayMode: 'compact',
  maxVisibleTabs: 6,
  enableTabReordering: true,
  showPinnedTabs: true,
  showBorders: true,
  compactSpacing: false,
  enableAnimations: true,
  darkMode: false,
  enableResponsiveLayout: true,
  enableAutoLayout: true,
  enableDensityControl: true,
  enableVirtualization: true,
  enableLazyLoading: true,
  maxRenderDistance: 5,
  enableHighContrast: false,
  enableReducedMotion: false,
  fontSize: 14,
  enableKeyboardNavigation: true,
};

export default function LayoutConfiguration({
  preferences,
  onPreferencesChange,
  onReset,
  className
}: LayoutConfigurationProps) {
  const [previewMode, setPreviewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop');

  const updatePreference = useCallback(<K extends keyof LayoutPreferences>(
    key: K,
    value: LayoutPreferences[K]
  ) => {
    onPreferencesChange({
      ...preferences,
      [key]: value
    });
  }, [preferences, onPreferencesChange]);

  const handleReset = useCallback(() => {
    onPreferencesChange(DEFAULT_PREFERENCES);
    onReset?.();
  }, [onPreferencesChange, onReset]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Layout Configuration</h2>
          <p className="text-muted-foreground">
            Customize the PDF viewer layout and behavior
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Preview mode selector */}
          <div className="flex items-center gap-1 border rounded-lg p-1">
            <Button
              variant={previewMode === 'desktop' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setPreviewMode('desktop')}
              className="h-8 w-8 p-0"
            >
              <Monitor className="h-4 w-4" />
            </Button>
            <Button
              variant={previewMode === 'tablet' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setPreviewMode('tablet')}
              className="h-8 w-8 p-0"
            >
              <Tablet className="h-4 w-4" />
            </Button>
            <Button
              variant={previewMode === 'mobile' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setPreviewMode('mobile')}
              className="h-8 w-8 p-0"
            >
              <Smartphone className="h-4 w-4" />
            </Button>
          </div>
          
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </Button>
        </div>
      </div>

      <Tabs defaultValue="layout" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="layout">Layout</TabsTrigger>
          <TabsTrigger value="interface">Interface</TabsTrigger>
          <TabsTrigger value="behavior">Behavior</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="accessibility">Accessibility</TabsTrigger>
        </TabsList>

        {/* Layout Tab */}
        <TabsContent value="layout" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Multi-Document Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Grid3X3 className="h-5 w-5" />
                  Multi-Document Layout
                </CardTitle>
                <CardDescription>
                  Configure how multiple PDFs are displayed
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="enable-multi">Enable Multi-Document View</Label>
                  <Switch
                    id="enable-multi"
                    checked={preferences.enableMultiDocument}
                    onCheckedChange={(checked) => updatePreference('enableMultiDocument', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Default Layout Mode</Label>
                  <Select
                    value={preferences.defaultLayoutMode}
                    onValueChange={(value: string) => updatePreference('defaultLayoutMode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="single">Single Document</SelectItem>
                      <SelectItem value="side-by-side">Side by Side</SelectItem>
                      <SelectItem value="grid">Grid Layout</SelectItem>
                      <SelectItem value="overlay">Overlay Compare</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Maximum Documents: {preferences.maxDocuments}</Label>
                  <Slider
                    value={[preferences.maxDocuments]}
                    onValueChange={([value]) => updatePreference('maxDocuments', value)}
                    min={1}
                    max={9}
                    step={1}
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Sidebar Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sidebar className="h-5 w-5" />
                  Sidebar Configuration
                </CardTitle>
                <CardDescription>
                  Customize sidebar behavior and appearance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="sidebar-open">Open by Default</Label>
                  <Switch
                    id="sidebar-open"
                    checked={preferences.sidebarDefaultOpen}
                    onCheckedChange={(checked) => updatePreference('sidebarDefaultOpen', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>Default Mode</Label>
                  <Select
                    value={preferences.sidebarDefaultMode}
                    onValueChange={(value: string) => updatePreference('sidebarDefaultMode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                      <SelectItem value="mini">Mini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Position</Label>
                  <Select
                    value={preferences.sidebarPosition}
                    onValueChange={(value: string) => updatePreference('sidebarPosition', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Left</SelectItem>
                      <SelectItem value="right">Right</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="sidebar-autohide">Auto-hide</Label>
                  <Switch
                    id="sidebar-autohide"
                    checked={preferences.sidebarAutoHide}
                    onCheckedChange={(checked) => updatePreference('sidebarAutoHide', checked)}
                  />
                </div>

                {preferences.sidebarAutoHide && (
                  <div className="space-y-2">
                    <Label>Auto-hide Delay: {preferences.sidebarAutoHideDelay}ms</Label>
                    <Slider
                      value={[preferences.sidebarAutoHideDelay]}
                      onValueChange={([value]) => updatePreference('sidebarAutoHideDelay', value)}
                      min={1000}
                      max={10000}
                      step={500}
                      className="w-full"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Interface Tab */}
        <TabsContent value="interface" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Header Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="h-5 w-5" />
                  Header & Toolbar
                </CardTitle>
                <CardDescription>
                  Configure header and toolbar appearance
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Header Mode</Label>
                  <Select
                    value={preferences.headerMode}
                    onValueChange={(value: string) => updatePreference('headerMode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="advanced-tools">Show Advanced Tools</Label>
                  <Switch
                    id="advanced-tools"
                    checked={preferences.showAdvancedTools}
                    onCheckedChange={(checked) => updatePreference('showAdvancedTools', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="toolbar-collapse">Auto-collapse Toolbar</Label>
                  <Switch
                    id="toolbar-collapse"
                    checked={preferences.toolbarAutoCollapse}
                    onCheckedChange={(checked) => updatePreference('toolbarAutoCollapse', checked)}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Tab Settings */}
            <Card>
              <CardHeader>
                <CardTitle>Document Tabs</CardTitle>
                <CardDescription>
                  Customize document tab behavior
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>Tab Display Mode</Label>
                  <Select
                    value={preferences.tabDisplayMode}
                    onValueChange={(value: string) => updatePreference('tabDisplayMode', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full</SelectItem>
                      <SelectItem value="compact">Compact</SelectItem>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="icons-only">Icons Only</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Max Visible Tabs: {preferences.maxVisibleTabs}</Label>
                  <Slider
                    value={[preferences.maxVisibleTabs]}
                    onValueChange={([value]) => updatePreference('maxVisibleTabs', value)}
                    min={3}
                    max={12}
                    step={1}
                    className="w-full"
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="tab-reorder">Enable Tab Reordering</Label>
                  <Switch
                    id="tab-reorder"
                    checked={preferences.enableTabReordering}
                    onCheckedChange={(checked) => updatePreference('enableTabReordering', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="pinned-tabs">Show Pinned Tabs</Label>
                  <Switch
                    id="pinned-tabs"
                    checked={preferences.showPinnedTabs}
                    onCheckedChange={(checked) => updatePreference('showPinnedTabs', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Behavior Tab */}
        <TabsContent value="behavior" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Display & Behavior</CardTitle>
              <CardDescription>
                Configure visual appearance and interaction behavior
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="show-borders">Show Borders</Label>
                    <Switch
                      id="show-borders"
                      checked={preferences.showBorders}
                      onCheckedChange={(checked) => updatePreference('showBorders', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="compact-spacing">Compact Spacing</Label>
                    <Switch
                      id="compact-spacing"
                      checked={preferences.compactSpacing}
                      onCheckedChange={(checked) => updatePreference('compactSpacing', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="enable-animations">Enable Animations</Label>
                    <Switch
                      id="enable-animations"
                      checked={preferences.enableAnimations}
                      onCheckedChange={(checked) => updatePreference('enableAnimations', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="responsive-layout">Responsive Layout</Label>
                    <Switch
                      id="responsive-layout"
                      checked={preferences.enableResponsiveLayout}
                      onCheckedChange={(checked) => updatePreference('enableResponsiveLayout', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="auto-layout">Auto Layout</Label>
                    <Switch
                      id="auto-layout"
                      checked={preferences.enableAutoLayout}
                      onCheckedChange={(checked) => updatePreference('enableAutoLayout', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="density-control">Density Control</Label>
                    <Switch
                      id="density-control"
                      checked={preferences.enableDensityControl}
                      onCheckedChange={(checked) => updatePreference('enableDensityControl', checked)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Optimization</CardTitle>
              <CardDescription>
                Configure performance-related settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="virtualization">Enable Virtualization</Label>
                    <Switch
                      id="virtualization"
                      checked={preferences.enableVirtualization}
                      onCheckedChange={(checked) => updatePreference('enableVirtualization', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="lazy-loading">Enable Lazy Loading</Label>
                    <Switch
                      id="lazy-loading"
                      checked={preferences.enableLazyLoading}
                      onCheckedChange={(checked) => updatePreference('enableLazyLoading', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Max Render Distance: {preferences.maxRenderDistance}</Label>
                    <Slider
                      value={[preferences.maxRenderDistance]}
                      onValueChange={([value]) => updatePreference('maxRenderDistance', value)}
                      min={1}
                      max={10}
                      step={1}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Accessibility Tab */}
        <TabsContent value="accessibility" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Accessibility Options</CardTitle>
              <CardDescription>
                Configure accessibility and usability features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="high-contrast">High Contrast Mode</Label>
                    <Switch
                      id="high-contrast"
                      checked={preferences.enableHighContrast}
                      onCheckedChange={(checked) => updatePreference('enableHighContrast', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="reduced-motion">Reduced Motion</Label>
                    <Switch
                      id="reduced-motion"
                      checked={preferences.enableReducedMotion}
                      onCheckedChange={(checked) => updatePreference('enableReducedMotion', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <Label htmlFor="keyboard-nav">Keyboard Navigation</Label>
                    <Switch
                      id="keyboard-nav"
                      checked={preferences.enableKeyboardNavigation}
                      onCheckedChange={(checked) => updatePreference('enableKeyboardNavigation', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Font Size: {preferences.fontSize}px</Label>
                    <Slider
                      value={[preferences.fontSize]}
                      onValueChange={([value]) => updatePreference('fontSize', value)}
                      min={10}
                      max={24}
                      step={1}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
