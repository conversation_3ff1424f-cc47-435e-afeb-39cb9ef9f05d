"use client";

import { useState, useCallback, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  X,
  ChevronUp,
  ChevronDown,
  Loader2,
  Settings,
} from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { toast } from "sonner";
import { extractPDFDocument, type PDFDocument } from "@/lib/types/pdf";

interface SearchResult {
  pageIndex: number;
  textItems: Array<{
    str: string;
    transform: number[];
    width: number;
    height: number;
    itemIndex: number;
    matches: RegExpMatchArray[];
    text?: string;
  }>;
}

interface SearchOptions {
  caseSensitive: boolean;
  wholeWords: boolean;
  useRegex?: boolean;
}

interface PDFSearchProps {
  // Core props
  searchText: string;
  onSearchChange: (text: string) => void;
  onClose: () => void;

  // Legacy props for backward compatibility
  onSearch?: (searchTerm: string) => void;
  onNavigateResults?: (direction: "next" | "prev") => void;
  onClearSearch?: () => void;
  searchResults?: Array<{ pageIndex: number; textItems: unknown[] }>;
  currentSearchIndex?: number;
  isSearching?: boolean;

  // Enhanced props
  pdfDocument?: PDFDocument;
  numPages?: number;
  onPageSelect?: (page: number) => void;
  onSearchResults?: (results: SearchResult[]) => void;
  onCurrentSearchIndex?: (index: number) => void;
  searchOptions?: SearchOptions;
  onSearchOptionsChange?: (options: SearchOptions) => void;

  // Variant selection
  variant?: "simple" | "enhanced" | "unified";
  className?: string;
}

export default function PDFSearch({
  searchText,
  onSearchChange,
  onClose,
  onSearch,
  onNavigateResults,
  onClearSearch,
  searchResults: legacySearchResults = [],
  currentSearchIndex: legacyCurrentSearchIndex = -1,
  isSearching: legacyIsSearching = false,
  pdfDocument,
  numPages = 0,
  onPageSelect,
  onSearchResults,
  onCurrentSearchIndex,
  searchOptions: initialSearchOptions,
  onSearchOptionsChange,
  variant = "enhanced",
  className,
}: PDFSearchProps): React.ReactElement {
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [isSearching, setIsSearching] = useState(false);
  const [searchOptions, setSearchOptions] = useState<SearchOptions>({
    caseSensitive: false,
    wholeWords: false,
    useRegex: false,
    ...initialSearchOptions,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);

  // Determine if we should use enhanced mode
  const useEnhancedMode: boolean =
    Boolean(pdfDocument && numPages && numPages > 0 && onPageSelect && variant !== "simple");

  // Perform search operation for enhanced mode
  const performSearch = useCallback(
    async (searchTerm: string) => {
      if (!useEnhancedMode || !searchTerm.trim()) {
        setSearchResults([]);
        setCurrentSearchIndex(-1);
        onSearchResults?.([]);
        onCurrentSearchIndex?.(-1);
        return;
      }

      setIsSearching(true);
      const results: SearchResult[] = [];

      try {
        let searchRegex: RegExp;

        if (searchOptions.useRegex) {
          try {
            searchRegex = new RegExp(
              searchTerm,
              searchOptions.caseSensitive ? "g" : "gi"
            );
          } catch {
            toast.error(
              "Invalid Regular Expression. Please check your regex pattern and try again."
            );
            setIsSearching(false);
            return;
          }
        } else if (searchOptions.wholeWords) {
          const pattern = `\\b${searchTerm.replace(
            /[.*+?^${}()|[\]\\]/g,
            "\\$&"
          )}\\b`;
          searchRegex = new RegExp(
            pattern,
            searchOptions.caseSensitive ? "g" : "gi"
          );
        } else {
          searchRegex = new RegExp(
            searchTerm.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
            searchOptions.caseSensitive ? "g" : "gi"
          );
        }

        for (let pageIndex = 1; pageIndex <= numPages; pageIndex++) {
          try {
            const doc = extractPDFDocument(pdfDocument);
            if (!doc) {
              continue;
            }

            const page = await doc.getPage(pageIndex);
            const textContent = await page.getTextContent();
            const pageResults: Array<{
              str: string;
              transform: number[];
              width: number;
              height: number;
              itemIndex: number;
              matches: RegExpMatchArray[];
              text?: string;
            }> = [];

            textContent.items.forEach((item, index: number) => {
              // Only process TextItem objects, not TextMarkedContent
              if ('str' in item && item.str && searchRegex.test(item.str)) {
                const matches = [...item.str.matchAll(searchRegex)];
                pageResults.push({
                  str: item.str,
                  transform: item.transform,
                  width: item.width,
                  height: item.height,
                  itemIndex: index,
                  text: item.str,
                  matches,
                });
              }
            });

            if (pageResults.length > 0) {
              results.push({
                pageIndex: pageIndex - 1, // Convert to 0-based indexing
                textItems: pageResults,
              });
            }
          } catch (pageError) {
            console.warn(`Error processing page ${pageIndex}:`, pageError);
          }
        }

        setSearchResults(results);
        setCurrentSearchIndex(results.length > 0 ? 0 : -1);
        onSearchResults?.(results);
        onCurrentSearchIndex?.(results.length > 0 ? 0 : -1);

        if (results.length === 0) {
          toast(`"${searchTerm}" was not found in the document.`);
        } else {
          const totalMatches = results.reduce(
            (sum, result) => sum + result.textItems.length,
            0
          );
          toast(
            `Found ${totalMatches} matches across ${results.length} pages.`
          );
        }
      } catch (error) {
        console.error("Search error:", error);
        toast.error("An error occurred while searching the document.");
      } finally {
        setIsSearching(false);
      }
    },
    [
      pdfDocument,
      numPages,
      searchOptions,
      onSearchResults,
      onCurrentSearchIndex,
      useEnhancedMode,
    ]
  );

  // Navigate search results for enhanced mode
  const goToPrevious = useCallback(() => {
    if (useEnhancedMode) {
      if (searchResults.length === 0) return;
      const newIndex =
        currentSearchIndex > 0
          ? currentSearchIndex - 1
          : searchResults.length - 1;
      setCurrentSearchIndex(newIndex);
      onCurrentSearchIndex?.(newIndex);
      onPageSelect?.(searchResults[newIndex].pageIndex + 1); // Convert back to 1-based
    } else {
      onNavigateResults?.("prev");
    }
  }, [
    searchResults,
    currentSearchIndex,
    onCurrentSearchIndex,
    onPageSelect,
    onNavigateResults,
    useEnhancedMode,
  ]);

  const goToNext = useCallback(() => {
    if (useEnhancedMode) {
      if (searchResults.length === 0) return;
      const newIndex =
        currentSearchIndex < searchResults.length - 1
          ? currentSearchIndex + 1
          : 0;
      setCurrentSearchIndex(newIndex);
      onCurrentSearchIndex?.(newIndex);
      onPageSelect?.(searchResults[newIndex].pageIndex + 1); // Convert back to 1-based
    } else {
      onNavigateResults?.("next");
    }
  }, [
    searchResults,
    currentSearchIndex,
    onCurrentSearchIndex,
    onPageSelect,
    onNavigateResults,
    useEnhancedMode,
  ]);

  // Handle search options change
  const updateSearchOptions = useCallback(
    (newOptions: Partial<SearchOptions>) => {
      const updatedOptions = { ...searchOptions, ...newOptions };
      setSearchOptions(updatedOptions);
      onSearchOptionsChange?.(updatedOptions);

      // Re-search if there's a search term and we're in enhanced mode
      if (searchText.trim() && useEnhancedMode) {
        performSearch(searchText);
      }
    },
    [
      searchOptions,
      onSearchOptionsChange,
      searchText,
      performSearch,
      useEnhancedMode,
    ]
  );

  // Handle search text change
  const handleSearchChange = useCallback(
    (text: string) => {
      onSearchChange(text);

      // Legacy callback support
      if (onSearch && text.trim()) {
        onSearch(text);
      } else if (onClearSearch && !text.trim()) {
        onClearSearch();
      }
    },
    [onSearchChange, onSearch, onClearSearch]
  );

  // Trigger search when search text changes (enhanced mode only)
  useEffect(() => {
    if (!useEnhancedMode) return;

    const debounceTimer = setTimeout(() => {
      if (searchText.trim()) {
        performSearch(searchText);
      } else {
        setSearchResults([]);
        setCurrentSearchIndex(-1);
        onSearchResults?.([]);
        onCurrentSearchIndex?.(-1);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [
    searchText,
    performSearch,
    onSearchResults,
    onCurrentSearchIndex,
    useEnhancedMode,
  ]);

  // Use enhanced results or legacy results
  const displayResults = useEnhancedMode ? searchResults : legacySearchResults;
  const displayCurrentIndex = useEnhancedMode
    ? currentSearchIndex
    : legacyCurrentSearchIndex;
  const displayIsSearching = useEnhancedMode ? isSearching : legacyIsSearching;

  const totalMatches = displayResults.reduce(
    (sum, result) => sum + result.textItems.length,
    0
  );

  // Legacy mode calculation for current match
  const currentMatch: number =
    !useEnhancedMode && displayResults.length > 0 && displayCurrentIndex >= 0
      ? displayResults
          .slice(0, displayCurrentIndex)
          .reduce((acc, result) => acc + result.textItems.length, 0) + 1
      : 0;

  return (
    <div className={`flex flex-col gap-3 p-4 ${className || ""}`} data-testid="pdf-search">
      {/* Search Header */}
      <div className="flex items-center justify-between">
        <h3 className="font-medium">Search in PDF</h3>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </div>


      <div className="relative">
        {useEnhancedMode ? (
          <>
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search text..."
              value={searchText}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-8"
              disabled={displayIsSearching}
            />
          </>
        ) : (
          <input
            type="text"
            placeholder="Search text..."
            value={searchText}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="w-full px-3 py-2 border rounded-md pl-8"
            onKeyDown={(e) => {
              if (e.key === "Enter" && onSearch && searchText.trim()) {
                onSearch(searchText);
              }
              if (e.key === "Escape") {
                onClose();
              }
            }}
          />
        )}
        {displayIsSearching && (
          <Loader2 className="absolute right-2 top-2.5 h-4 w-4 animate-spin text-muted-foreground" />
        )}
      </div>

      {/* Search Options (Enhanced mode only) */}
      {useEnhancedMode && variant === "enhanced" && (
        <div className="space-y-2">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-between"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <span className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Search Options
            </span>
            <ChevronDown
              className={`h-4 w-4 transition-transform ${
                showAdvanced ? "rotate-180" : ""
              }`}
            />
          </Button>
          {showAdvanced && (
            <div className="space-y-2 pt-2 border-t">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="case-sensitive"
                  checked={searchOptions.caseSensitive}
                  onCheckedChange={(checked) =>
                    updateSearchOptions({ caseSensitive: !!checked })
                  }
                />
                <label htmlFor="case-sensitive" className="text-sm">
                  Case sensitive
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="whole-words"
                  checked={searchOptions.wholeWords}
                  onCheckedChange={(checked) =>
                    updateSearchOptions({ wholeWords: !!checked })
                  }
                />
                <label htmlFor="whole-words" className="text-sm">
                  Whole words only
                </label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="use-regex"
                  checked={searchOptions.useRegex}
                  onCheckedChange={(checked) =>
                    updateSearchOptions({ useRegex: !!checked })
                  }
                />
                <label htmlFor="use-regex" className="text-sm">
                  Use regular expression
                </label>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Navigation Controls */}
      {displayResults.length > 0 && (
        <>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {useEnhancedMode ? (
                <>
                  <Badge variant="secondary">
                    {displayCurrentIndex + 1} of {displayResults.length}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    {totalMatches} total matches
                  </span>
                </>
              ) : (
                <span className="text-sm text-muted-foreground">
                  {currentMatch} of {totalMatches}
                </span>
              )}
            </div>
            <div className="flex gap-1">
              {useEnhancedMode ? (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToPrevious}
                    disabled={displayResults.length === 0}
                  >
                    <ChevronUp className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={goToNext}
                    disabled={displayResults.length === 0}
                  >
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </>
              ) : (
                <>
                  <button
                    onClick={goToPrevious}
                    disabled={displayResults.length === 0}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50"
                  >
                    ↑
                  </button>
                  <button
                    onClick={goToNext}
                    disabled={displayResults.length === 0}
                    className="px-2 py-1 text-sm border rounded hover:bg-gray-50 disabled:opacity-50"
                  >
                    ↓
                  </button>
                </>
              )}
            </div>
          </div>
        </>
      )}

      {/* Search Results List (Enhanced variant only) */}
      {useEnhancedMode &&
        variant === "enhanced" &&
        displayResults.length > 0 && (
          <>
            <Separator />
            <ScrollArea className="max-h-64">
              <div className="space-y-2">
                {displayResults.map((result, index) => (
                  <div
                    key={result.pageIndex}
                    className={`p-2 rounded-md cursor-pointer transition-colors ${
                      index === displayCurrentIndex
                        ? "bg-primary/10 border border-primary/20"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => {
                      setCurrentSearchIndex(index);
                      onCurrentSearchIndex?.(index);
                      onPageSelect?.(result.pageIndex + 1);
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">
                        Page {result.pageIndex + 1}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {result.textItems.length} matches
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {result.textItems[0] && typeof result.textItems[0] === 'object' && 'text' in result.textItems[0]
                        ? String(result.textItems[0].text).substring(0, 100) + '...'
                        : result.textItems[0] && typeof result.textItems[0] === 'object' && 'str' in result.textItems[0]
                        ? String(result.textItems[0].str).substring(0, 100) + '...'
                        : 'No preview available'}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </>
        )}
    </div>
  );
}
