"use client";

import React, { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

import { ScrollArea } from '@/components/ui/scroll-area';
import { Checkbox } from '@/components/ui/checkbox';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Search,
  Filter,
  SortAsc,
  SortDesc,
  X,
  ChevronUp,
  Bookmark,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Enhanced search interfaces
export interface AdvancedSearchQuery {
  text: string;
  filters: SearchFilters;
  options: SearchOptions;
  facets: SearchFacets;
}

export interface SearchFilters {
  contentTypes: string[];
  dateRange: { start: Date | null; end: Date | null };
  pageRange: { start: number | null; end: number | null };
  authors: string[];
  tags: string[];
  hasAnnotations: boolean | null;
  hasBookmarks: boolean | null;
  hasForms: boolean | null;
  fileSize: { min: number | null; max: number | null };
  language: string | null;
  rating: number | null;
}

export interface SearchOptions {
  caseSensitive: boolean;
  wholeWords: boolean;
  useRegex: boolean;
  fuzzySearch: boolean;
  semanticSearch: boolean;
  includeOCR: boolean;
  searchScope: 'current' | 'all' | 'selection' | 'range';
  maxResults: number;
  sortBy: 'relevance' | 'date' | 'title' | 'author' | 'size' | 'pages';
  sortOrder: 'asc' | 'desc';
  groupBy: 'none' | 'document' | 'page' | 'type' | 'author' | 'date';
}

export interface SearchFacets {
  contentTypes: Array<{ name: string; count: number; selected: boolean }>;
  authors: Array<{ name: string; count: number; selected: boolean }>;
  tags: Array<{ name: string; count: number; selected: boolean }>;
  dateRanges: Array<{ name: string; range: { start: Date; end: Date }; count: number; selected: boolean }>;
  languages: Array<{ name: string; count: number; selected: boolean }>;
}

export interface SearchResult {
  id: string;
  documentId: string;
  title: string;
  pageNumber: number;
  content: string;
  highlightedContent: string;
  context: string;
  type: 'text' | 'annotation' | 'bookmark' | 'form' | 'metadata' | 'ocr';
  relevanceScore: number;
  position: { x: number; y: number; width: number; height: number };
  author?: string;
  createdDate?: Date;
  modifiedDate?: Date;
  tags: string[];
  language?: string;
  thumbnail?: string;
}

export interface SearchSuggestion {
  type: 'recent' | 'popular' | 'smart' | 'autocomplete' | 'semantic';
  text: string;
  description?: string;
  category?: string;
  frequency?: number;
  icon?: React.ComponentType<{ className?: string }>;
}

interface AdvancedSearchInterfaceProps {
  onSearch: (query: AdvancedSearchQuery) => Promise<SearchResult[]>;
  onResultSelect: (result: SearchResult) => void;
  onSaveSearch: (query: AdvancedSearchQuery, name: string) => void;
  initialQuery?: Partial<AdvancedSearchQuery>;
  suggestions?: SearchSuggestion[];
  facets?: SearchFacets;
  recentSearches?: string[];
  savedSearches?: Array<{ name: string; query: AdvancedSearchQuery }>;
  className?: string;
}

const DEFAULT_FILTERS: SearchFilters = {
  contentTypes: [],
  dateRange: { start: null, end: null },
  pageRange: { start: null, end: null },
  authors: [],
  tags: [],
  hasAnnotations: null,
  hasBookmarks: null,
  hasForms: null,
  fileSize: { min: null, max: null },
  language: null,
  rating: null,
};

const DEFAULT_OPTIONS: SearchOptions = {
  caseSensitive: false,
  wholeWords: false,
  useRegex: false,
  fuzzySearch: false,
  semanticSearch: false,
  includeOCR: false,
  searchScope: 'all',
  maxResults: 100,
  sortBy: 'relevance',
  sortOrder: 'desc',
  groupBy: 'none',
};

export default function AdvancedSearchInterface({
  onSearch,
  onResultSelect,

  initialQuery,
  suggestions = [],
  recentSearches = [],
  className,
}: AdvancedSearchInterfaceProps) {
  const [searchText, setSearchText] = useState(initialQuery?.text || '');
  const [filters, setFilters] = useState<SearchFilters>({ ...DEFAULT_FILTERS, ...initialQuery?.filters });
  const [options, setOptions] = useState<SearchOptions>({ ...DEFAULT_OPTIONS, ...initialQuery?.options });
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showOptions, setShowOptions] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeTab, setActiveTab] = useState<'search' | 'filters' | 'results'>('search');
  const [searchHistory, setSearchHistory] = useState<string[]>(recentSearches);

  const searchInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout>();

  // Memoized search query
  const currentQuery = useMemo((): AdvancedSearchQuery => ({
    text: searchText,
    filters,
    options,
    facets: facets || {
      contentTypes: [],
      authors: [],
      tags: [],
      dateRanges: [],
      languages: [],
    },
  }), [searchText, filters, options, facets]);

  // Filtered suggestions based on search text
  const filteredSuggestions = useMemo(() => {
    if (!searchText.trim()) return suggestions.slice(0, 5);
    
    return suggestions
      .filter(s => s.text.toLowerCase().includes(searchText.toLowerCase()))
      .sort((a, b) => {
        // Prioritize by type and frequency
        const typeOrder = { recent: 0, popular: 1, smart: 2, autocomplete: 3, semantic: 4 };
        const aOrder = typeOrder[a.type] || 5;
        const bOrder = typeOrder[b.type] || 5;
        
        if (aOrder !== bOrder) return aOrder - bOrder;
        return (b.frequency || 0) - (a.frequency || 0);
      })
      .slice(0, 8);
  }, [searchText, suggestions]);

  // Perform search with debouncing
  const performSearch = useCallback(async (query: AdvancedSearchQuery) => {
    if (!query.text.trim()) {
      setResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const searchResults = await onSearch(query);
      setResults(searchResults);
      
      // Add to search history
      if (query.text && !searchHistory.includes(query.text)) {
        setSearchHistory(prev => [query.text, ...prev.slice(0, 9)]);
      }
    } catch (error) {
      console.error('Search failed:', error);
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, [onSearch, searchHistory]);

  // Debounced search
  const debouncedSearch = useCallback((query: AdvancedSearchQuery) => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      performSearch(query);
    }, 300);
  }, [performSearch]);

  // Handle search text change
  const handleSearchTextChange = useCallback((text: string) => {
    setSearchText(text);
    setShowSuggestions(text.length > 0);
    setSelectedSuggestion(-1);
    
    if (text.trim()) {
      debouncedSearch({ ...currentQuery, text });
    } else {
      setResults([]);
    }
  }, [currentQuery, debouncedSearch]);

  // Handle filter changes
  const updateFilters = useCallback((updates: Partial<SearchFilters>) => {
    const newFilters = { ...filters, ...updates };
    setFilters(newFilters);
    
    if (searchText.trim()) {
      debouncedSearch({ ...currentQuery, filters: newFilters });
    }
  }, [filters, searchText, currentQuery, debouncedSearch]);

  // Handle option changes
  const updateOptions = useCallback((updates: Partial<SearchOptions>) => {
    const newOptions = { ...options, ...updates };
    setOptions(newOptions);
    
    if (searchText.trim()) {
      debouncedSearch({ ...currentQuery, options: newOptions });
    }
  }, [options, searchText, currentQuery, debouncedSearch]);

  // Handle keyboard navigation
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (showSuggestions && filteredSuggestions.length > 0) {
      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedSuggestion(prev => 
            prev < filteredSuggestions.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedSuggestion(prev => 
            prev > 0 ? prev - 1 : filteredSuggestions.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (selectedSuggestion >= 0) {
            const suggestion = filteredSuggestions[selectedSuggestion];
            handleSearchTextChange(suggestion.text);
          } else {
            performSearch(currentQuery);
          }
          setShowSuggestions(false);
          break;
        case 'Escape':
          setShowSuggestions(false);
          setSelectedSuggestion(-1);
          break;
      }
    } else if (e.key === 'Enter') {
      performSearch(currentQuery);
    }
  }, [showSuggestions, filteredSuggestions, selectedSuggestion, currentQuery, handleSearchTextChange, performSearch]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    if (searchText.trim()) {
      debouncedSearch({ ...currentQuery, filters: DEFAULT_FILTERS });
    }
  }, [searchText, currentQuery, debouncedSearch]);

  // Reset search options
  const resetOptions = useCallback(() => {
    setOptions(DEFAULT_OPTIONS);
    if (searchText.trim()) {
      debouncedSearch({ ...currentQuery, options: DEFAULT_OPTIONS });
    }
  }, [searchText, currentQuery, debouncedSearch]);

  // Save current search
  const handleSaveSearch = useCallback(() => {
    const name = prompt('Enter a name for this search:');
    if (name && name.trim()) {
      onSaveSearch(currentQuery, name.trim());
    }
  }, [currentQuery, onSaveSearch]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  // Auto-focus search input
  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  const hasActiveFilters = useMemo(() => {
    return Object.values(filters).some(value => {
      if (Array.isArray(value)) return value.length > 0;
      if (typeof value === 'object' && value !== null) {
        return Object.values(value).some(v => v !== null);
      }
      return value !== null;
    });
  }, [filters]);

  const hasActiveOptions = useMemo(() => {
    return JSON.stringify(options) !== JSON.stringify(DEFAULT_OPTIONS);
  }, [options]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search Header */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5" />
              Advanced Search
            </CardTitle>
            <div className="flex items-center gap-2">
              {hasActiveFilters && (
                <Badge variant="secondary" className="text-xs">
                  {Object.values(filters).filter(v => 
                    Array.isArray(v) ? v.length > 0 : v !== null
                  ).length} filters
                </Badge>
              )}
              {hasActiveOptions && (
                <Badge variant="outline" className="text-xs">
                  Custom options
                </Badge>
              )}
            </div>
          </div>
          <CardDescription>
            Search across documents with advanced filtering and intelligent suggestions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Main Search Input */}
          <div className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                ref={searchInputRef}
                placeholder="Search documents, annotations, bookmarks..."
                value={searchText}
                onChange={(e) => handleSearchTextChange(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={() => setShowSuggestions(searchText.length > 0)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
                className="pl-10 pr-12"
                disabled={isSearching}
              />
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
              {searchText && !isSearching && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                  onClick={() => handleSearchTextChange('')}
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            {/* Search Suggestions */}
            {showSuggestions && filteredSuggestions.length > 0 && (
              <Card className="absolute top-full left-0 right-0 z-50 mt-1 shadow-lg">
                <CardContent className="p-2">
                  <ScrollArea className="max-h-64">
                    {filteredSuggestions.map((suggestion, index) => (
                      <div
                        key={`${suggestion.type}-${suggestion.text}-${index}`}
                        className={cn(
                          "flex items-center gap-2 px-2 py-1.5 rounded cursor-pointer text-sm",
                          index === selectedSuggestion ? "bg-primary/10" : "hover:bg-muted"
                        )}
                        onClick={() => {
                          handleSearchTextChange(suggestion.text);
                          setShowSuggestions(false);
                        }}
                      >
                        {suggestion.icon ? (
                          <suggestion.icon className="h-3 w-3 text-muted-foreground" />
                        ) : (
                          <Search className="h-3 w-3 text-muted-foreground" />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="truncate">{suggestion.text}</div>
                          {suggestion.description && (
                            <div className="text-xs text-muted-foreground truncate">
                              {suggestion.description}
                            </div>
                          )}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {suggestion.type}
                        </Badge>
                      </div>
                    ))}
                  </ScrollArea>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Quick Actions */}
          <div className="flex items-center gap-2 flex-wrap">
            <Button
              variant={showFilters ? "default" : "outline"}
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="gap-1"
            >
              <Filter className="h-3 w-3" />
              Filters
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-1 text-xs">
                  {Object.values(filters).filter(v => 
                    Array.isArray(v) ? v.length > 0 : v !== null
                  ).length}
                </Badge>
              )}
            </Button>

            <Button
              variant={showOptions ? "default" : "outline"}
              size="sm"
              onClick={() => setShowOptions(!showOptions)}
              className="gap-1"
            >
              <Settings className="h-3 w-3" />
              Options
            </Button>

            {(hasActiveFilters || hasActiveOptions) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  clearFilters();
                  resetOptions();
                }}
                className="gap-1"
              >
                <X className="h-3 w-3" />
                Clear All
              </Button>
            )}

            <Button
              variant="ghost"
              size="sm"
              onClick={handleSaveSearch}
              disabled={!searchText.trim()}
              className="gap-1"
            >
              <Bookmark className="h-3 w-3" />
              Save Search
            </Button>

            <div className="flex-1" />

            {results.length > 0 && (
              <div className="text-sm text-muted-foreground">
                {results.length} results
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Filters Panel */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Search Filters</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={clearFilters}>
                  Clear Filters
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFilters(false)}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="content" className="space-y-4">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="metadata">Metadata</TabsTrigger>
                <TabsTrigger value="properties">Properties</TabsTrigger>
                <TabsTrigger value="advanced">Advanced</TabsTrigger>
              </TabsList>

              <TabsContent value="content" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Content Types */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Content Types</Label>
                    <div className="space-y-2">
                      {['Text', 'Annotations', 'Bookmarks', 'Forms', 'Metadata'].map((type) => (
                        <div key={type} className="flex items-center space-x-2">
                          <Checkbox
                            id={`content-${type.toLowerCase()}`}
                            checked={filters.contentTypes.includes(type.toLowerCase())}
                            onCheckedChange={(checked) => {
                              const newTypes = checked
                                ? [...filters.contentTypes, type.toLowerCase()]
                                : filters.contentTypes.filter(t => t !== type.toLowerCase());
                              updateFilters({ contentTypes: newTypes });
                            }}
                          />
                          <Label htmlFor={`content-${type.toLowerCase()}`} className="text-sm">
                            {type}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Page Range */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Page Range</Label>
                    <div className="flex items-center gap-2">
                      <Input
                        type="number"
                        placeholder="From"
                        value={filters.pageRange.start || ''}
                        onChange={(e) => updateFilters({
                          pageRange: { ...filters.pageRange, start: e.target.value ? parseInt(e.target.value) : null }
                        })}
                        className="w-20"
                      />
                      <span className="text-sm text-muted-foreground">to</span>
                      <Input
                        type="number"
                        placeholder="To"
                        value={filters.pageRange.end || ''}
                        onChange={(e) => updateFilters({
                          pageRange: { ...filters.pageRange, end: e.target.value ? parseInt(e.target.value) : null }
                        })}
                        className="w-20"
                      />
                    </div>
                  </div>
                </div>

                {/* Feature Toggles */}
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Document Features</Label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="has-annotations" className="text-sm">Has Annotations</Label>
                      <Switch
                        id="has-annotations"
                        checked={filters.hasAnnotations === true}
                        onCheckedChange={(checked) => updateFilters({ hasAnnotations: checked ? true : null })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="has-bookmarks" className="text-sm">Has Bookmarks</Label>
                      <Switch
                        id="has-bookmarks"
                        checked={filters.hasBookmarks === true}
                        onCheckedChange={(checked) => updateFilters({ hasBookmarks: checked ? true : null })}
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="has-forms" className="text-sm">Has Forms</Label>
                      <Switch
                        id="has-forms"
                        checked={filters.hasForms === true}
                        onCheckedChange={(checked) => updateFilters({ hasForms: checked ? true : null })}
                      />
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="metadata" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Authors */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Authors</Label>
                    <Input
                      placeholder="Enter author names..."
                      value={filters.authors.join(', ')}
                      onChange={(e) => updateFilters({
                        authors: e.target.value.split(',').map(a => a.trim()).filter(Boolean)
                      })}
                    />
                  </div>

                  {/* Tags */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Tags</Label>
                    <Input
                      placeholder="Enter tags..."
                      value={filters.tags.join(', ')}
                      onChange={(e) => updateFilters({
                        tags: e.target.value.split(',').map(t => t.trim()).filter(Boolean)
                      })}
                    />
                  </div>

                  {/* Language */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Language</Label>
                    <Select
                      value={filters.language || ''}
                      onValueChange={(value) => updateFilters({ language: value || null })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Any language" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">Any language</SelectItem>
                        <SelectItem value="en">English</SelectItem>
                        <SelectItem value="es">Spanish</SelectItem>
                        <SelectItem value="fr">French</SelectItem>
                        <SelectItem value="de">German</SelectItem>
                        <SelectItem value="it">Italian</SelectItem>
                        <SelectItem value="pt">Portuguese</SelectItem>
                        <SelectItem value="zh">Chinese</SelectItem>
                        <SelectItem value="ja">Japanese</SelectItem>
                        <SelectItem value="ko">Korean</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Rating */}
                  <div className="space-y-2">
                    <Label className="text-sm font-medium">Minimum Rating</Label>
                    <div className="space-y-2">
                      <Slider
                        value={[filters.rating || 0]}
                        onValueChange={([value]) => updateFilters({ rating: value > 0 ? value : null })}
                        max={5}
                        min={0}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Any</span>
                        <span>★★★★★</span>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="properties" className="space-y-4">
                {/* Date Range */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Date Range</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div>
                      <Label htmlFor="date-start" className="text-xs text-muted-foreground">From</Label>
                      <Input
                        id="date-start"
                        type="date"
                        value={filters.dateRange.start?.toISOString().split('T')[0] || ''}
                        onChange={(e) => updateFilters({
                          dateRange: {
                            ...filters.dateRange,
                            start: e.target.value ? new Date(e.target.value) : null
                          }
                        })}
                      />
                    </div>
                    <div>
                      <Label htmlFor="date-end" className="text-xs text-muted-foreground">To</Label>
                      <Input
                        id="date-end"
                        type="date"
                        value={filters.dateRange.end?.toISOString().split('T')[0] || ''}
                        onChange={(e) => updateFilters({
                          dateRange: {
                            ...filters.dateRange,
                            end: e.target.value ? new Date(e.target.value) : null
                          }
                        })}
                      />
                    </div>
                  </div>
                </div>

                {/* File Size */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">File Size (MB)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={filters.fileSize.min || ''}
                      onChange={(e) => updateFilters({
                        fileSize: { ...filters.fileSize, min: e.target.value ? parseFloat(e.target.value) : null }
                      })}
                      className="w-24"
                    />
                    <span className="text-sm text-muted-foreground">to</span>
                    <Input
                      type="number"
                      placeholder="Max"
                      value={filters.fileSize.max || ''}
                      onChange={(e) => updateFilters({
                        fileSize: { ...filters.fileSize, max: e.target.value ? parseFloat(e.target.value) : null }
                      })}
                      className="w-24"
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <div className="text-sm text-muted-foreground mb-4">
                  Advanced filtering options for power users
                </div>

                {/* Faceted Search */}
                {facets && (
                  <div className="space-y-4">
                    {facets.contentTypes.length > 0 && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Content Type Facets</Label>
                        <div className="flex flex-wrap gap-2">
                          {facets.contentTypes.map((facet) => (
                            <Badge
                              key={facet.name}
                              variant={facet.selected ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => {
                                // Toggle facet selection
                                const newTypes = facet.selected
                                  ? filters.contentTypes.filter(t => t !== facet.name)
                                  : [...filters.contentTypes, facet.name];
                                updateFilters({ contentTypes: newTypes });
                              }}
                            >
                              {facet.name} ({facet.count})
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {facets.authors.length > 0 && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Author Facets</Label>
                        <div className="flex flex-wrap gap-2">
                          {facets.authors.map((facet) => (
                            <Badge
                              key={facet.name}
                              variant={facet.selected ? "default" : "outline"}
                              className="cursor-pointer"
                              onClick={() => {
                                const newAuthors = facet.selected
                                  ? filters.authors.filter(a => a !== facet.name)
                                  : [...filters.authors, facet.name];
                                updateFilters({ authors: newAuthors });
                              }}
                            >
                              {facet.name} ({facet.count})
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}

      {/* Search Options Panel */}
      {showOptions && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">Search Options</CardTitle>
              <div className="flex items-center gap-2">
                <Button variant="ghost" size="sm" onClick={resetOptions}>
                  Reset Options
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowOptions(false)}
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="search" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="search">Search</TabsTrigger>
                <TabsTrigger value="display">Display</TabsTrigger>
                <TabsTrigger value="performance">Performance</TabsTrigger>
              </TabsList>

              <TabsContent value="search" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Search Behavior */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Search Behavior</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="case-sensitive" className="text-sm">Case Sensitive</Label>
                        <Switch
                          id="case-sensitive"
                          checked={options.caseSensitive}
                          onCheckedChange={(checked) => updateOptions({ caseSensitive: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="whole-words" className="text-sm">Whole Words Only</Label>
                        <Switch
                          id="whole-words"
                          checked={options.wholeWords}
                          onCheckedChange={(checked) => updateOptions({ wholeWords: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="use-regex" className="text-sm">Regular Expressions</Label>
                        <Switch
                          id="use-regex"
                          checked={options.useRegex}
                          onCheckedChange={(checked) => updateOptions({ useRegex: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="fuzzy-search" className="text-sm">Fuzzy Search</Label>
                        <Switch
                          id="fuzzy-search"
                          checked={options.fuzzySearch}
                          onCheckedChange={(checked) => updateOptions({ fuzzySearch: checked })}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Advanced Features */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Advanced Features</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="semantic-search" className="text-sm">Semantic Search</Label>
                        <Switch
                          id="semantic-search"
                          checked={options.semanticSearch}
                          onCheckedChange={(checked) => updateOptions({ semanticSearch: checked })}
                        />
                      </div>
                      <div className="flex items-center justify-between">
                        <Label htmlFor="include-ocr" className="text-sm">Include OCR Text</Label>
                        <Switch
                          id="include-ocr"
                          checked={options.includeOCR}
                          onCheckedChange={(checked) => updateOptions({ includeOCR: checked })}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Search Scope */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Search Scope</Label>
                  <Select
                    value={options.searchScope}
                    onValueChange={(value: string) => updateOptions({ searchScope: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Documents</SelectItem>
                      <SelectItem value="current">Current Document</SelectItem>
                      <SelectItem value="selection">Selected Text</SelectItem>
                      <SelectItem value="range">Page Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </TabsContent>

              <TabsContent value="display" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Sorting */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Sort Results</Label>
                    <div className="space-y-2">
                      <Select
                        value={options.sortBy}
                        onValueChange={(value: string) => updateOptions({ sortBy: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="relevance">Relevance</SelectItem>
                          <SelectItem value="date">Date</SelectItem>
                          <SelectItem value="title">Title</SelectItem>
                          <SelectItem value="author">Author</SelectItem>
                          <SelectItem value="size">File Size</SelectItem>
                          <SelectItem value="pages">Page Count</SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="flex items-center gap-2">
                        <Button
                          variant={options.sortOrder === 'asc' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateOptions({ sortOrder: 'asc' })}
                          className="flex-1"
                        >
                          <SortAsc className="h-3 w-3 mr-1" />
                          Ascending
                        </Button>
                        <Button
                          variant={options.sortOrder === 'desc' ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => updateOptions({ sortOrder: 'desc' })}
                          className="flex-1"
                        >
                          <SortDesc className="h-3 w-3 mr-1" />
                          Descending
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Grouping */}
                  <div className="space-y-4">
                    <Label className="text-sm font-medium">Group Results</Label>
                    <Select
                      value={options.groupBy}
                      onValueChange={(value: string) => updateOptions({ groupBy: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Grouping</SelectItem>
                        <SelectItem value="document">By Document</SelectItem>
                        <SelectItem value="page">By Page</SelectItem>
                        <SelectItem value="type">By Content Type</SelectItem>
                        <SelectItem value="author">By Author</SelectItem>
                        <SelectItem value="date">By Date</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="performance" className="space-y-4">
                {/* Max Results */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Maximum Results: {options.maxResults}</Label>
                  <Slider
                    value={[options.maxResults]}
                    onValueChange={([value]) => updateOptions({ maxResults: value })}
                    max={1000}
                    min={10}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>10</span>
                    <span>1000</span>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>Higher result limits may impact search performance. Consider using filters to narrow your search instead.</p>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
