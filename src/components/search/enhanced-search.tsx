import React, {
  useState,
  useC<PERSON>back,
  useMemo,
  useRef,
  useEffect,
} from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import {
  Search,
  ChevronDown,
  ChevronUp,
  X,
  Settings,
  History,
  Bookmark,
  FileText,
  Hash,
  Quote,
  Copy,
} from "lucide-react";

// Advanced search interfaces
export interface SearchResult {
  id: string;
  pageNumber: number;
  text: string;
  context: string;
  highlightedText: string;
  position: { x: number; y: number; width: number; height: number };
  type: "text" | "annotation" | "bookmark" | "form" | "metadata";
  relevanceScore: number;
  timestamp?: Date;
  author?: string;
  tags?: string[];
}

export interface SearchOptions {
  caseSensitive: boolean;
  wholeWords: boolean;
  useRegex: boolean;
  includeAnnotations: boolean;
  includeBookmarks: boolean;
  includeForms: boolean;
  includeMetadata: boolean;
  fuzzySearch: boolean;
  searchScope: "current" | "all" | "range";
  pageRange?: { start: number; end: number };
  maxResults?: number;
  sortBy: "relevance" | "page" | "date" | "type";
  groupBy?: "page" | "type" | "author" | "none";
}

export interface SearchHistory {
  id: string;
  query: string;
  options: SearchOptions;
  resultCount: number;
  timestamp: Date;
  isBookmarked?: boolean;
}

export interface SearchSuggestion {
  type: "recent" | "popular" | "smart" | "autocomplete";
  text: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  metadata?: unknown;
}

interface EnhancedSearchProps {
  onSearch: (query: string, options: SearchOptions) => Promise<SearchResult[]>;
  onResultSelect: (result: SearchResult) => void;
  onResultBookmark: (result: SearchResult) => void;
  placeholder?: string;
  initialQuery?: string;
  initialOptions?: Partial<SearchOptions>;
  searchHistory?: SearchHistory[];
  onHistoryUpdate?: (history: SearchHistory[]) => void;
  className?: string;
}

// Custom hook for search functionality
const useEnhancedSearch = (
  onSearch: EnhancedSearchProps["onSearch"],
  initialOptions: Partial<SearchOptions> = {}
) => {
  const [query, setQuery] = useState("");
  const [options, setOptions] = useState<SearchOptions>({
    caseSensitive: false,
    wholeWords: false,
    useRegex: false,
    includeAnnotations: true,
    includeBookmarks: true,
    includeForms: false,
    includeMetadata: false,
    fuzzySearch: false,
    searchScope: "all",
    maxResults: 100,
    sortBy: "relevance",
    groupBy: "none",
    ...initialOptions,
  });

  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [error, setError] = useState<string | null>(null);

  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const performSearch = useCallback(
    async (searchQuery?: string, searchOptions?: SearchOptions) => {
      const actualQuery = searchQuery ?? query;
      const actualOptions = searchOptions ?? options;

      if (!actualQuery.trim()) {
        setResults([]);
        setCurrentIndex(-1);
        return;
      }

      setIsLoading(true);
      setError(null);

      try {
        const searchResults = await onSearch(actualQuery, actualOptions);
        setResults(searchResults);
        setCurrentIndex(searchResults.length > 0 ? 0 : -1);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Search failed");
        setResults([]);
        setCurrentIndex(-1);
      } finally {
        setIsLoading(false);
      }
    },
    [query, options, onSearch]
  );

  const debouncedSearch = useCallback(
    (searchQuery: string, searchOptions?: SearchOptions) => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }

      searchTimeoutRef.current = setTimeout(() => {
        performSearch(searchQuery, searchOptions);
      }, 300);
    },
    [performSearch]
  );

  const navigateResults = useCallback(
    (direction: "next" | "prev") => {
      if (results.length === 0) return;

      setCurrentIndex((prev) => {
        if (direction === "next") {
          return prev < results.length - 1 ? prev + 1 : 0;
        } else {
          return prev > 0 ? prev - 1 : results.length - 1;
        }
      });
    },
    [results.length]
  );

  const updateQuery = useCallback(
    (newQuery: string) => {
      setQuery(newQuery);
      debouncedSearch(newQuery);
    },
    [debouncedSearch]
  );

  const updateOptions = useCallback(
    (newOptions: Partial<SearchOptions>) => {
      const updatedOptions = { ...options, ...newOptions };
      setOptions(updatedOptions);
      if (query.trim()) {
        debouncedSearch(query, updatedOptions);
      }
    },
    [options, query, debouncedSearch]
  );

  const clearSearch = useCallback(() => {
    setQuery("");
    setResults([]);
    setCurrentIndex(-1);
    setError(null);
  }, []);

  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return {
    query,
    options,
    results,
    currentIndex,
    isLoading,
    error,
    updateQuery,
    updateOptions,
    navigateResults,
    clearSearch,
    performSearch,
  };
};

// Search input with autocomplete
const SearchInput: React.FC<{
  query: string;
  onQueryChange: (query: string) => void;
  onSearch: (query: string) => void;
  suggestions: SearchSuggestion[];
  isLoading: boolean;
  placeholder?: string;
  className?: string;
}> = ({
  query,
  onQueryChange,
  onSearch,
  suggestions,
  isLoading,
  placeholder = "Search in document...",
  className,
}) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = useState(-1);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (showSuggestions && suggestions.length > 0) {
        switch (e.key) {
          case "ArrowDown":
            e.preventDefault();
            setSelectedSuggestion((prev) =>
              prev < suggestions.length - 1 ? prev + 1 : 0
            );
            break;
          case "ArrowUp":
            e.preventDefault();
            setSelectedSuggestion((prev) =>
              prev > 0 ? prev - 1 : suggestions.length - 1
            );
            break;
          case "Enter":
            e.preventDefault();
            if (selectedSuggestion >= 0) {
              onQueryChange(suggestions[selectedSuggestion].text);
              onSearch(suggestions[selectedSuggestion].text);
            } else {
              onSearch(query);
            }
            setShowSuggestions(false);
            break;
          case "Escape":
            setShowSuggestions(false);
            setSelectedSuggestion(-1);
            break;
        }
      } else if (e.key === "Enter") {
        onSearch(query);
      }
    },
    [
      showSuggestions,
      suggestions,
      selectedSuggestion,
      query,
      onQueryChange,
      onSearch,
    ]
  );

  const handleSuggestionClick = useCallback(
    (suggestion: SearchSuggestion) => {
      onQueryChange(suggestion.text);
      onSearch(suggestion.text);
      setShowSuggestions(false);
      inputRef.current?.focus();
    },
    [onQueryChange, onSearch]
  );

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
        <Input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => {
            onQueryChange(e.target.value);
            setShowSuggestions(true);
            setSelectedSuggestion(-1);
          }}
          onKeyDown={handleKeyDown}
          onFocus={() => setShowSuggestions(suggestions.length > 0)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
          placeholder={placeholder}
          className="pl-10 pr-10"
        />
        {isLoading && (
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
        {query && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              onQueryChange("");
              setShowSuggestions(false);
            }}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          >
            <X className="size-3" />
          </Button>
        )}
      </div>

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 max-h-60 overflow-hidden">
          <ScrollArea className="max-h-60">
            {suggestions.map((suggestion, index) => {
              const Icon = suggestion.icon || Search;
              return (
                <button
                  key={`${suggestion.type}-${index}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-accent transition-colors",
                    index === selectedSuggestion && "bg-accent"
                  )}
                >
                  <Icon className="size-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">
                      {suggestion.text}
                    </div>
                    {suggestion.description && (
                      <div className="text-xs text-muted-foreground truncate">
                        {suggestion.description}
                      </div>
                    )}
                  </div>
                  <Badge variant="outline" className="text-xs capitalize">
                    {suggestion.type}
                  </Badge>
                </button>
              );
            })}
          </ScrollArea>
        </Card>
      )}
    </div>
  );
};

// Advanced search options panel
const SearchOptionsPanel: React.FC<{
  options: SearchOptions;
  onOptionsChange: (options: Partial<SearchOptions>) => void;
  isOpen: boolean;
  onToggle: () => void;
}> = ({ options, onOptionsChange, isOpen, onToggle }) => {
  if (!isOpen) {
    return (
      <Button variant="ghost" size="sm" onClick={onToggle}>
        <Settings className="size-4 mr-1" />
        Options
      </Button>
    );
  }

  return (
    <Card className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">Search Options</h4>
        <Button variant="ghost" size="sm" onClick={onToggle}>
          <X className="size-4" />
        </Button>
      </div>

      {/* Text search options */}
      <div className="space-y-3">
        <h5 className="text-sm font-medium">Text Search</h5>
        <div className="grid grid-cols-2 gap-3">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.caseSensitive}
              onChange={(e) =>
                onOptionsChange({ caseSensitive: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Case sensitive</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.wholeWords}
              onChange={(e) =>
                onOptionsChange({ wholeWords: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Whole words</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.useRegex}
              onChange={(e) => onOptionsChange({ useRegex: e.target.checked })}
              className="rounded"
            />
            <span className="text-sm">Regular expressions</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.fuzzySearch}
              onChange={(e) =>
                onOptionsChange({ fuzzySearch: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Fuzzy search</span>
          </label>
        </div>
      </div>

      {/* Search scope */}
      <div className="space-y-3">
        <h5 className="text-sm font-medium">Search Scope</h5>
        <select
          value={options.searchScope}
          onChange={(e) =>
            onOptionsChange({
              searchScope: e.target.value as SearchOptions["searchScope"],
            })
          }
          className="w-full px-3 py-2 border rounded-md text-sm"
        >
          <option value="all">All pages</option>
          <option value="current">Current page</option>
          <option value="range">Page range</option>
        </select>

        {options.searchScope === "range" && (
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="Start"
              value={options.pageRange?.start || ""}
              onChange={(e) =>
                onOptionsChange({
                  pageRange: {
                    start: parseInt(e.target.value) || 1,
                    end: options.pageRange?.end || 1,
                  },
                })
              }
              className="flex-1"
            />
            <Input
              type="number"
              placeholder="End"
              value={options.pageRange?.end || ""}
              onChange={(e) =>
                onOptionsChange({
                  pageRange: {
                    start: options.pageRange?.start || 1,
                    end: parseInt(e.target.value) || 1,
                  },
                })
              }
              className="flex-1"
            />
          </div>
        )}
      </div>

      {/* Content types */}
      <div className="space-y-3">
        <h5 className="text-sm font-medium">Content Types</h5>
        <div className="grid grid-cols-2 gap-3">
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.includeAnnotations}
              onChange={(e) =>
                onOptionsChange({ includeAnnotations: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Annotations</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.includeBookmarks}
              onChange={(e) =>
                onOptionsChange({ includeBookmarks: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Bookmarks</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.includeForms}
              onChange={(e) =>
                onOptionsChange({ includeForms: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Form fields</span>
          </label>
          <label className="flex items-center space-x-2 cursor-pointer">
            <input
              type="checkbox"
              checked={options.includeMetadata}
              onChange={(e) =>
                onOptionsChange({ includeMetadata: e.target.checked })
              }
              className="rounded"
            />
            <span className="text-sm">Metadata</span>
          </label>
        </div>
      </div>

      {/* Sorting and grouping */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Sort by</label>
          <select
            value={options.sortBy}
            onChange={(e) =>
              onOptionsChange({
                sortBy: e.target.value as SearchOptions["sortBy"],
              })
            }
            className="w-full px-3 py-2 border rounded-md text-sm"
          >
            <option value="relevance">Relevance</option>
            <option value="page">Page number</option>
            <option value="date">Date</option>
            <option value="type">Type</option>
          </select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Group by</label>
          <select
            value={options.groupBy || "none"}
            onChange={(e) =>
              onOptionsChange({
                groupBy:
                  e.target.value === "none"
                    ? undefined
                    : (e.target.value as SearchOptions["groupBy"]),
              })
            }
            className="w-full px-3 py-2 border rounded-md text-sm"
          >
            <option value="none">None</option>
            <option value="page">Page</option>
            <option value="type">Type</option>
            <option value="author">Author</option>
          </select>
        </div>
      </div>
    </Card>
  );
};

// Main enhanced search component
export const EnhancedSearch: React.FC<EnhancedSearchProps> = ({
  onSearch,
  onResultSelect,
  onResultBookmark,
  placeholder,
  // initialQuery is not used in this component but kept for interface compatibility
  initialOptions = {},
  searchHistory = [],
  onHistoryUpdate,
  className,
}) => {
  const {
    query,
    options,
    results,
    currentIndex,
    isLoading,
    error,
    updateQuery,
    updateOptions,
    navigateResults,
    // clearSearch is not used in this component but extracted from hook
  } = useEnhancedSearch(onSearch, initialOptions);

  const [showOptions, setShowOptions] = useState(false);
  const [showHistory, setShowHistory] = useState(false);

  // Generate suggestions based on search history and smart suggestions
  const suggestions: SearchSuggestion[] = useMemo(() => {
    if (!query.trim()) {
      return searchHistory.slice(0, 5).map((item) => ({
        type: "recent" as const,
        text: item.query,
        description: `${item.resultCount} results`,
        icon: History,
      }));
    }

    const filtered = searchHistory
      .filter((item) => item.query.toLowerCase().includes(query.toLowerCase()))
      .slice(0, 3);

    return [
      ...filtered.map((item) => ({
        type: "recent" as const,
        text: item.query,
        description: `${item.resultCount} results`,
        icon: History,
      })),
      // Add smart suggestions here based on query patterns
      ...(query.length > 2
        ? [
            {
              type: "smart" as const,
              text: `"${query}"`,
              description: "Exact phrase",
              icon: Quote,
            },
          ]
        : []),
    ];
  }, [query, searchHistory]);

  // Group results if grouping is enabled
  const groupedResults = useMemo(() => {
    if (!options.groupBy) return { "All Results": results };

    return results.reduce((groups, result) => {
      let key: string;
      switch (options.groupBy) {
        case "page":
          key = `Page ${result.pageNumber}`;
          break;
        case "type":
          key = result.type.charAt(0).toUpperCase() + result.type.slice(1);
          break;
        case "author":
          key = result.author || "Unknown";
          break;
        default:
          key = "All Results";
      }

      if (!groups[key]) groups[key] = [];
      groups[key].push(result);
      return groups;
    }, {} as Record<string, SearchResult[]>);
  }, [results, options.groupBy]);

  const handleSearch = useCallback(
    (searchQuery: string) => {
      if (searchQuery.trim() && onHistoryUpdate) {
        const newHistoryItem: SearchHistory = {
          id: Date.now().toString(),
          query: searchQuery,
          options,
          resultCount: 0, // Will be updated after search
          timestamp: new Date(),
        };

        const updatedHistory = [
          newHistoryItem,
          ...searchHistory.filter((item) => item.query !== searchQuery),
        ].slice(0, 20);

        onHistoryUpdate(updatedHistory);
      }
    },
    [options, searchHistory, onHistoryUpdate]
  );

  const getResultIcon = (type: SearchResult["type"]) => {
    switch (type) {
      case "text":
        return FileText;
      case "annotation":
        return Hash;
      case "bookmark":
        return Bookmark;
      case "form":
        return FileText;
      case "metadata":
        return FileText;
      default:
        return FileText;
    }
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Search input */}
      <SearchInput
        query={query}
        onQueryChange={updateQuery}
        onSearch={handleSearch}
        suggestions={suggestions}
        isLoading={isLoading}
        placeholder={placeholder}
      />

      {/* Controls */}
      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center gap-2">
          <SearchOptionsPanel
            options={options}
            onOptionsChange={updateOptions}
            isOpen={showOptions}
            onToggle={() => setShowOptions(!showOptions)}
          />

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowHistory(!showHistory)}
          >
            <History className="size-4 mr-1" />
            History
          </Button>
        </div>

        {results.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {currentIndex + 1} of {results.length}
            </span>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateResults("prev")}
                disabled={results.length === 0}
              >
                <ChevronUp className="size-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => navigateResults("next")}
                disabled={results.length === 0}
              >
                <ChevronDown className="size-4" />
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <Card className="p-3 border-destructive bg-destructive/5">
          <p className="text-sm text-destructive">{error}</p>
        </Card>
      )}

      {/* Results */}
      {results.length > 0 && (
        <Card className="max-h-96 overflow-hidden">
          <ScrollArea className="max-h-96">
            <div className="p-2">
              {Object.entries(groupedResults).map(
                ([groupName, groupResults]) => (
                  <div key={groupName} className="mb-4 last:mb-0">
                    {options.groupBy && (
                      <div className="flex items-center gap-2 px-2 py-1 mb-2 bg-muted/50 rounded">
                        <span className="text-sm font-medium">{groupName}</span>
                        <Badge variant="secondary" className="text-xs">
                          {groupResults.length}
                        </Badge>
                      </div>
                    )}

                    {groupResults.map((result) => {
                      const Icon = getResultIcon(result.type);
                      const globalIndex = results.indexOf(result);
                      const isSelected = globalIndex === currentIndex;

                      return (
                        <button
                          key={result.id}
                          onClick={() => onResultSelect(result)}
                          className={cn(
                            "w-full flex items-start gap-3 p-3 text-left hover:bg-accent rounded transition-colors",
                            isSelected && "bg-accent ring-2 ring-ring"
                          )}
                        >
                          <Icon className="size-4 mt-0.5 text-muted-foreground flex-shrink-0" />

                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <Badge variant="outline" className="text-xs">
                                Page {result.pageNumber}
                              </Badge>
                              <Badge
                                variant="secondary"
                                className="text-xs capitalize"
                              >
                                {result.type}
                              </Badge>
                              {result.relevanceScore && (
                                <span className="text-xs text-muted-foreground">
                                  {Math.round(result.relevanceScore * 100)}%
                                  match
                                </span>
                              )}
                            </div>

                            <div
                              className="text-sm mb-1"
                              dangerouslySetInnerHTML={{
                                __html: result.highlightedText,
                              }}
                            />

                            <div className="text-xs text-muted-foreground line-clamp-2">
                              {result.context}
                            </div>

                            {result.author && (
                              <div className="text-xs text-muted-foreground mt-1">
                                by {result.author}
                                {result.timestamp && (
                                  <span>
                                    {" "}
                                    • {result.timestamp.toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                            )}
                          </div>

                          <div className="flex flex-col gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                onResultBookmark(result);
                              }}
                              className="h-6 w-6 p-0"
                              title="Bookmark result"
                            >
                              <Bookmark className="size-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard.writeText(result.text);
                              }}
                              className="h-6 w-6 p-0"
                              title="Copy text"
                            >
                              <Copy className="size-3" />
                            </Button>
                          </div>
                        </button>
                      );
                    })}
                  </div>
                )
              )}
            </div>
          </ScrollArea>
        </Card>
      )}

      {/* No results */}
      {query && !isLoading && results.length === 0 && !error && (
        <Card className="p-8 text-center">
          <Search className="size-8 mx-auto mb-2 text-muted-foreground" />
          <p className="text-muted-foreground">
            No results found for &quot;{query}&quot;
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Try adjusting your search options or using different keywords
          </p>
        </Card>
      )}

      {/* Search history */}
      {showHistory && searchHistory.length > 0 && (
        <Card className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium">Search History</h4>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowHistory(false)}
            >
              <X className="size-4" />
            </Button>
          </div>

          <div className="space-y-2 max-h-60 overflow-y-auto">
            {searchHistory.map((item) => (
              <button
                key={item.id}
                onClick={() => {
                  updateQuery(item.query);
                  handleSearch(item.query);
                  setShowHistory(false);
                }}
                className="w-full flex items-center justify-between p-2 hover:bg-accent rounded text-left"
              >
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{item.query}</div>
                  <div className="text-xs text-muted-foreground">
                    {item.resultCount} results •{" "}
                    {item.timestamp.toLocaleDateString()}
                  </div>
                </div>
                {item.isBookmarked && (
                  <Bookmark className="size-4 text-muted-foreground" />
                )}
              </button>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default EnhancedSearch;
