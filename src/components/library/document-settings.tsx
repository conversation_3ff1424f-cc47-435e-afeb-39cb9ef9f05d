"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';

import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Grid3X3,
  List,
  FileText,
  HardDrive,
  Zap,
  Shield,
  Database,
  Trash2,
  RotateCcw,
  Save,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type { DocumentLibrarySettings } from '@/lib/types/pdf';
import { documentLibrary } from '@/lib/document-library';
import { formatFileSize } from '@/lib/types/pdf';

interface DocumentSettingsProps {
  onSettingsChange?: (settings: DocumentLibrarySettings) => void;
  className?: string;
}

interface StorageInfo {
  used: number;
  available: number;
  total: number;
  documentCount: number;
}

export default function DocumentSettings({
  onSettingsChange,
  className
}: DocumentSettingsProps) {
  const [settings, setSettings] = useState<DocumentLibrarySettings | null>(null);
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Load current settings
  useEffect(() => {
    loadSettings();
    loadStorageInfo();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      await documentLibrary.initialize();
      const currentSettings = await documentLibrary.getSettings();
      setSettings(currentSettings);
    } catch {
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const loadStorageInfo = async () => {
    try {
      const documents = await documentLibrary.getAllDocuments();
      const totalSize = documents.reduce((sum, doc) => sum + doc.metadata.fileSize, 0);
      
      // Estimate available storage (this would be more accurate with actual IndexedDB quota API)
      const estimatedQuota = 1024 * 1024 * 1024; // 1GB estimate
      
      setStorageInfo({
        used: totalSize,
        available: estimatedQuota - totalSize,
        total: estimatedQuota,
        documentCount: documents.length
      });
    } catch (error) {
      console.error('Failed to load storage info:', error);
    }
  };

  // Update settings
  const updateSettings = useCallback((newSettings: Partial<DocumentLibrarySettings>) => {
    if (!settings) return;
    
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    setHasChanges(true);
    onSettingsChange?.(updatedSettings);
  }, [settings, onSettingsChange]);

  // Save settings
  const saveSettings = useCallback(async () => {
    if (!settings) return;

    try {
      setIsSaving(true);
      await documentLibrary.updateSettings(settings);
      setHasChanges(false);
      toast.success('Settings saved successfully');
    } catch {
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  }, [settings]);

  // Reset to defaults
  const resetToDefaults = useCallback(() => {
    const defaultSettings: DocumentLibrarySettings = {
      defaultView: 'grid',
      sortBy: 'dateAdded',
      sortOrder: 'desc',
      showThumbnails: true,
      thumbnailSize: 'medium',
      autoGenerateThumbnails: true,
      maxStorageSize: 500 * 1024 * 1024, // 500MB
      enableAutoBackup: true,
      backupInterval: 24
    };
    
    setSettings(defaultSettings);
    setHasChanges(true);
  }, []);

  // Clear all data
  const clearAllData = useCallback(async () => {
    if (!confirm('Are you sure you want to clear all document data? This action cannot be undone.')) {
      return;
    }

    try {
      const documents = await documentLibrary.getAllDocuments();
      for (const doc of documents) {
        await documentLibrary.removeDocument(doc.id);
      }
      
      await loadStorageInfo();
      toast.success('All document data cleared');
    } catch {
      toast.error('Failed to clear data');
    }
  }, []);

  if (isLoading || !settings) {
    return (
      <div className={cn("flex items-center justify-center h-64", className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
          <p className="text-sm text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Settings className="h-6 w-6" />
          <h2 className="text-xl font-semibold">Document Library Settings</h2>
          {hasChanges && <Badge variant="secondary">Unsaved Changes</Badge>}
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            <RotateCcw className="h-4 w-4 mr-1" />
            Reset to Defaults
          </Button>
          <Button onClick={saveSettings} disabled={!hasChanges || isSaving}>
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-1"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-1" />
                Save Settings
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs defaultValue="display" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="display">Display</TabsTrigger>
          <TabsTrigger value="storage">Storage</TabsTrigger>
          <TabsTrigger value="automation">Automation</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        {/* Display Settings */}
        <TabsContent value="display" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="h-5 w-5" />
                View Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Default View */}
              <div>
                <Label>Default View Mode</Label>
                <Select 
                  value={settings.defaultView} 
                  onValueChange={(value: string) => updateSettings({ defaultView: value })}
                >
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grid">
                      <div className="flex items-center gap-2">
                        <Grid3X3 className="h-4 w-4" />
                        Grid View
                      </div>
                    </SelectItem>
                    <SelectItem value="list">
                      <div className="flex items-center gap-2">
                        <List className="h-4 w-4" />
                        List View
                      </div>
                    </SelectItem>
                    <SelectItem value="compact">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        Compact View
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Default Sort */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Default Sort By</Label>
                  <Select 
                    value={settings.sortBy} 
                    onValueChange={(value: string) => updateSettings({ sortBy: value })}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="dateAdded">Date Added</SelectItem>
                      <SelectItem value="dateModified">Date Modified</SelectItem>
                      <SelectItem value="size">File Size</SelectItem>
                      <SelectItem value="lastAccessed">Last Accessed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Default Sort Order</Label>
                  <Select 
                    value={settings.sortOrder} 
                    onValueChange={(value: string) => updateSettings({ sortOrder: value })}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">Ascending</SelectItem>
                      <SelectItem value="desc">Descending</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Thumbnail Settings */}
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-thumbnails"
                    checked={settings.showThumbnails}
                    onCheckedChange={(checked) => updateSettings({ showThumbnails: !!checked })}
                  />
                  <Label htmlFor="show-thumbnails">Show document thumbnails</Label>
                </div>

                {settings.showThumbnails && (
                  <>
                    <div>
                      <Label>Thumbnail Size</Label>
                      <Select 
                        value={settings.thumbnailSize} 
                        onValueChange={(value: string) => updateSettings({ thumbnailSize: value })}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="small">Small</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="large">Large</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="auto-generate-thumbnails"
                        checked={settings.autoGenerateThumbnails}
                        onCheckedChange={(checked) => updateSettings({ autoGenerateThumbnails: !!checked })}
                      />
                      <Label htmlFor="auto-generate-thumbnails">Auto-generate thumbnails for new documents</Label>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Storage Settings */}
        <TabsContent value="storage" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <HardDrive className="h-5 w-5" />
                Storage Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Storage Usage */}
              {storageInfo && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Storage Used</span>
                    <span>{formatFileSize(storageInfo.used)} / {formatFileSize(storageInfo.total)}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full" 
                      style={{ width: `${(storageInfo.used / storageInfo.total) * 100}%` }}
                    ></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                    <div>Documents: {storageInfo.documentCount}</div>
                    <div>Available: {formatFileSize(storageInfo.available)}</div>
                  </div>
                </div>
              )}

              <Separator />

              {/* Storage Limits */}
              <div>
                <Label>Maximum Storage Size</Label>
                <div className="mt-2">
                  <Slider
                    value={[settings.maxStorageSize]}
                    onValueChange={([value]) => updateSettings({ maxStorageSize: value })}
                    max={2 * 1024 * 1024 * 1024} // 2GB
                    min={100 * 1024 * 1024} // 100MB
                    step={50 * 1024 * 1024} // 50MB steps
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>100MB</span>
                    <span className="font-medium">{formatFileSize(settings.maxStorageSize)}</span>
                    <span>2GB</span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Storage Actions */}
              <div className="space-y-2">
                <h4 className="font-medium">Storage Actions</h4>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={loadStorageInfo}>
                    <Database className="h-4 w-4 mr-1" />
                    Refresh Usage
                  </Button>
                  <Button variant="destructive" onClick={clearAllData}>
                    <Trash2 className="h-4 w-4 mr-1" />
                    Clear All Data
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  <AlertTriangle className="h-3 w-3 inline mr-1" />
                  Clearing all data will permanently remove all documents and cannot be undone.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Automation Settings */}
        <TabsContent value="automation" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Automation & Backup
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Auto Backup */}
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enable-auto-backup"
                    checked={settings.enableAutoBackup}
                    onCheckedChange={(checked) => updateSettings({ enableAutoBackup: !!checked })}
                  />
                  <Label htmlFor="enable-auto-backup">Enable automatic backups</Label>
                </div>

                {settings.enableAutoBackup && (
                  <div>
                    <Label>Backup Interval (hours)</Label>
                    <div className="mt-2">
                      <Slider
                        value={[settings.backupInterval]}
                        onValueChange={([value]) => updateSettings({ backupInterval: value })}
                        max={168} // 1 week
                        min={1}
                        step={1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-muted-foreground mt-1">
                        <span>1 hour</span>
                        <span className="font-medium">{settings.backupInterval} hours</span>
                        <span>1 week</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              {/* Auto Organization */}
              <div className="space-y-2">
                <h4 className="font-medium">Auto-Organization (Coming Soon)</h4>
                <div className="space-y-2 opacity-50">
                  <div className="flex items-center space-x-2">
                    <Checkbox disabled />
                    <Label>Auto-tag documents based on content</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox disabled />
                    <Label>Auto-categorize by document type</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox disabled />
                    <Label>Auto-organize into collections</Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Advanced Settings */}
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Advanced Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Performance Settings */}
              <div className="space-y-2">
                <h4 className="font-medium">Performance</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="lazy-loading" defaultChecked disabled />
                    <Label htmlFor="lazy-loading">Enable lazy loading for large libraries</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="cache-thumbnails" defaultChecked disabled />
                    <Label htmlFor="cache-thumbnails">Cache thumbnails for faster loading</Label>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Privacy Settings */}
              <div className="space-y-2">
                <h4 className="font-medium">Privacy & Security</h4>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="local-storage" defaultChecked disabled />
                    <Label htmlFor="local-storage">Store all data locally (no cloud sync)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="encrypt-metadata" disabled />
                    <Label htmlFor="encrypt-metadata">Encrypt sensitive metadata</Label>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Debug Information */}
              <div className="space-y-2">
                <h4 className="font-medium">Debug Information</h4>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>Library Version: 1.0.0</div>
                  <div>IndexedDB Support: {typeof indexedDB !== 'undefined' ? 'Yes' : 'No'}</div>
                  <div>Local Storage: {typeof localStorage !== 'undefined' ? 'Available' : 'Not Available'}</div>
                  <div>Settings Last Modified: {new Date().toLocaleString()}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
