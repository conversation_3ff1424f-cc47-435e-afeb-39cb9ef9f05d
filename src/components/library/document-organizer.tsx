"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

import { Separator } from '@/components/ui/separator';
import {
  Plus,
  X,
  Folder,
  Edit,
  FolderPlus,
  Star,
  Pin,
  MoreHorizontal,
  GripVertical
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import type {
  DocumentInstance,
  DocumentCollection
} from '@/lib/types/pdf';
import { documentLibrary } from '@/lib/document-library';

interface DocumentOrganizerProps {
  documents: DocumentInstance[];
  collections: DocumentCollection[];
  onDocumentsUpdate: () => void;
  onCollectionsUpdate: () => void;
}

interface DragItem {
  type: 'document' | 'collection';
  id: string;
  data: DocumentInstance | DocumentCollection;
}

export default function DocumentOrganizer({
  documents,
  collections,
  onDocumentsUpdate,
  onCollectionsUpdate
}: DocumentOrganizerProps) {
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null);
  const [dropTarget, setDropTarget] = useState<string | null>(null);
  const [showNewCollectionDialog, setShowNewCollectionDialog] = useState(false);
  const [showBulkEditDialog, setShowBulkEditDialog] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [newCollectionDescription, setNewCollectionDescription] = useState('');
  const [bulkTags, setBulkTags] = useState<string[]>([]);
  const [bulkCategories, setBulkCategories] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [newCategory, setNewCategory] = useState('');

  // Drag and Drop Handlers
  const handleDragStart = useCallback((e: React.DragEvent, item: DragItem) => {
    setDraggedItem(item);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', item.id);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  }, []);

  const handleDragEnter = useCallback((e: React.DragEvent, targetId: string) => {
    e.preventDefault();
    setDropTarget(targetId);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    // Only clear drop target if we're leaving the container
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDropTarget(null);
    }
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent, targetCollectionId: string) => {
    e.preventDefault();
    setDropTarget(null);

    if (!draggedItem) return;

    try {
      if (draggedItem.type === 'document') {
        // Add document to collection
        await documentLibrary.addDocumentToCollection(draggedItem.id, targetCollectionId);
        toast.success('Document moved to collection');
        onDocumentsUpdate();
        onCollectionsUpdate();
      }
    } catch {
      toast.error('Failed to move document');
    }

    setDraggedItem(null);
  }, [draggedItem, onDocumentsUpdate, onCollectionsUpdate]);

  // Collection Management
  const handleCreateCollection = useCallback(async () => {
    if (!newCollectionName.trim()) return;

    try {
      await documentLibrary.createCollection(
        newCollectionName.trim(),
        newCollectionDescription.trim() || undefined
      );
      
      setNewCollectionName('');
      setNewCollectionDescription('');
      setShowNewCollectionDialog(false);
      onCollectionsUpdate();
      toast.success('Collection created');
    } catch {
      toast.error('Failed to create collection');
    }
  }, [newCollectionName, newCollectionDescription, onCollectionsUpdate]);

  // Bulk Operations
  const handleSelectDocument = useCallback((documentId: string, selected: boolean) => {
    setSelectedDocuments(prev => 
      selected 
        ? [...prev, documentId]
        : prev.filter(id => id !== documentId)
    );
  }, []);

  const handleSelectAll = useCallback(() => {
    setSelectedDocuments(documents.map(doc => doc.id));
  }, [documents]);

  const handleDeselectAll = useCallback(() => {
    setSelectedDocuments([]);
  }, []);

  const handleBulkAddTags = useCallback(async () => {
    if (selectedDocuments.length === 0 || bulkTags.length === 0) return;

    try {
      for (const documentId of selectedDocuments) {
        const document = documents.find(doc => doc.id === documentId);
        if (document) {
          const existingTags = document.metadata.tags || [];
          const newTags = [...new Set([...existingTags, ...bulkTags])];
          
          await documentLibrary.updateDocument(documentId, {
            metadata: {
              ...document.metadata,
              tags: newTags
            }
          });
        }
      }

      setBulkTags([]);
      setShowBulkEditDialog(false);
      onDocumentsUpdate();
      toast.success(`Added tags to ${selectedDocuments.length} documents`);
    } catch {
      toast.error('Failed to add tags');
    }
  }, [selectedDocuments, bulkTags, documents, onDocumentsUpdate]);

  const handleBulkAddCategories = useCallback(async () => {
    if (selectedDocuments.length === 0 || bulkCategories.length === 0) return;

    try {
      for (const documentId of selectedDocuments) {
        const document = documents.find(doc => doc.id === documentId);
        if (document) {
          const existingCategories = document.metadata.categories || [];
          const newCategories = [...new Set([...existingCategories, ...bulkCategories])];
          
          await documentLibrary.updateDocument(documentId, {
            metadata: {
              ...document.metadata,
              categories: newCategories
            }
          });
        }
      }

      setBulkCategories([]);
      setShowBulkEditDialog(false);
      onDocumentsUpdate();
      toast.success(`Added categories to ${selectedDocuments.length} documents`);
    } catch {
      toast.error('Failed to add categories');
    }
  }, [selectedDocuments, bulkCategories, documents, onDocumentsUpdate]);

  const handleBulkFavorite = useCallback(async (isFavorite: boolean) => {
    if (selectedDocuments.length === 0) return;

    try {
      for (const documentId of selectedDocuments) {
        const document = documents.find(doc => doc.id === documentId);
        if (document) {
          await documentLibrary.updateDocument(documentId, {
            metadata: {
              ...document.metadata,
              isFavorite
            }
          });
        }
      }

      onDocumentsUpdate();
      toast.success(`${isFavorite ? 'Added to' : 'Removed from'} favorites: ${selectedDocuments.length} documents`);
    } catch {
      toast.error('Failed to update favorites');
    }
  }, [selectedDocuments, documents, onDocumentsUpdate]);

  const addBulkTag = () => {
    if (newTag.trim() && !bulkTags.includes(newTag.trim())) {
      setBulkTags(prev => [...prev, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeBulkTag = (tag: string) => {
    setBulkTags(prev => prev.filter(t => t !== tag));
  };

  const addBulkCategory = () => {
    if (newCategory.trim() && !bulkCategories.includes(newCategory.trim())) {
      setBulkCategories(prev => [...prev, newCategory.trim()]);
      setNewCategory('');
    }
  };

  const removeBulkCategory = (category: string) => {
    setBulkCategories(prev => prev.filter(c => c !== category));
  };

  return (
    <div className="space-y-6">
      {/* Bulk Actions Toolbar */}
      {selectedDocuments.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">
                  {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} selected
                </span>
                <Button variant="outline" size="sm" onClick={handleDeselectAll}>
                  Clear Selection
                </Button>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => handleBulkFavorite(true)}>
                  <Star className="h-4 w-4 mr-1" />
                  Add to Favorites
                </Button>
                <Button variant="outline" size="sm" onClick={() => handleBulkFavorite(false)}>
                  <Star className="h-4 w-4 mr-1" />
                  Remove from Favorites
                </Button>
                <Dialog open={showBulkEditDialog} onOpenChange={setShowBulkEditDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Bulk Edit
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Bulk Edit Documents</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {/* Bulk Tags */}
                      <div>
                        <Label>Add Tags</Label>
                        <div className="flex gap-2 mt-1">
                          <Input
                            placeholder="Add a tag..."
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                addBulkTag();
                              }
                            }}
                            className="flex-1"
                          />
                          <Button type="button" onClick={addBulkTag} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {bulkTags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {bulkTags.map(tag => (
                              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                {tag}
                                <X
                                  className="h-3 w-3 cursor-pointer"
                                  onClick={() => removeBulkTag(tag)}
                                />
                              </Badge>
                            ))}
                          </div>
                        )}
                        <Button 
                          onClick={handleBulkAddTags} 
                          disabled={bulkTags.length === 0}
                          className="mt-2"
                          size="sm"
                        >
                          Apply Tags
                        </Button>
                      </div>

                      <Separator />

                      {/* Bulk Categories */}
                      <div>
                        <Label>Add Categories</Label>
                        <div className="flex gap-2 mt-1">
                          <Input
                            placeholder="Add a category..."
                            value={newCategory}
                            onChange={(e) => setNewCategory(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                addBulkCategory();
                              }
                            }}
                            className="flex-1"
                          />
                          <Button type="button" onClick={addBulkCategory} size="sm">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {bulkCategories.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {bulkCategories.map(category => (
                              <Badge key={category} variant="outline" className="flex items-center gap-1">
                                <Folder className="h-3 w-3" />
                                {category}
                                <X
                                  className="h-3 w-3 cursor-pointer"
                                  onClick={() => removeBulkCategory(category)}
                                />
                              </Badge>
                            ))}
                          </div>
                        )}
                        <Button 
                          onClick={handleBulkAddCategories} 
                          disabled={bulkCategories.length === 0}
                          className="mt-2"
                          size="sm"
                        >
                          Apply Categories
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Collections Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              Collections
            </CardTitle>
            <Dialog open={showNewCollectionDialog} onOpenChange={setShowNewCollectionDialog}>
              <DialogTrigger asChild>
                <Button size="sm">
                  <FolderPlus className="h-4 w-4 mr-1" />
                  New Collection
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Collection</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="collection-name">Collection Name</Label>
                    <Input
                      id="collection-name"
                      placeholder="Enter collection name..."
                      value={newCollectionName}
                      onChange={(e) => setNewCollectionName(e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="collection-description">Description (Optional)</Label>
                    <Input
                      id="collection-description"
                      placeholder="Enter description..."
                      value={newCollectionDescription}
                      onChange={(e) => setNewCollectionDescription(e.target.value)}
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setShowNewCollectionDialog(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateCollection} disabled={!newCollectionName.trim()}>
                      Create Collection
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {collections.filter(c => !c.isSystem).map(collection => (
              <div
                key={collection.id}
                className={cn(
                  "p-4 border rounded-lg transition-colors",
                  dropTarget === collection.id ? "border-primary bg-primary/10" : "border-border"
                )}
                onDragOver={handleDragOver}
                onDragEnter={(e) => handleDragEnter(e, collection.id)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, collection.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-medium">{collection.name}</h3>
                    {collection.description && (
                      <p className="text-sm text-muted-foreground mt-1">
                        {collection.description}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground mt-2">
                      {collection.documentIds.length} document{collection.documentIds.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                  <Button variant="ghost" size="sm">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Document Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Documents</CardTitle>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleSelectAll}>
                Select All
              </Button>
              <Button variant="outline" size="sm" onClick={handleDeselectAll}>
                Clear Selection
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {documents.map(document => (
              <div
                key={document.id}
                className={cn(
                  "flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-colors",
                  selectedDocuments.includes(document.id) ? "border-primary bg-primary/10" : "border-border hover:bg-muted/50"
                )}
                draggable
                onDragStart={(e) => handleDragStart(e, { type: 'document', id: document.id, data: document })}
                onClick={() => handleSelectDocument(document.id, !selectedDocuments.includes(document.id))}
              >
                <GripVertical className="h-4 w-4 text-muted-foreground" />
                <input
                  type="checkbox"
                  checked={selectedDocuments.includes(document.id)}
                  onChange={(e) => handleSelectDocument(document.id, e.target.checked)}
                  onClick={(e) => e.stopPropagation()}
                />
                <div className="flex-1">
                  <h4 className="font-medium">{document.metadata.title}</h4>
                  <div className="flex items-center gap-2 mt-1">
                    {document.metadata.tags.slice(0, 3).map(tag => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {document.metadata.tags.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{document.metadata.tags.length - 3}
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  {document.metadata.isFavorite && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                  {document.metadata.isPinned && <Pin className="h-4 w-4 text-blue-500" />}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
