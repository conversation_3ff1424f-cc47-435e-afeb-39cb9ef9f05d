"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Clock,
  Pause,
  Settings,
  Target,
  TrendingUp,
  AlertTriangle
} from 'lucide-react';

export interface TimerSettings {
  totalDuration?: number; // Total presentation duration in milliseconds
  warningTime: number; // Warning time before end in milliseconds
  showElapsed: boolean;
  showRemaining: boolean;
  showProgress: boolean;
  enableWarnings: boolean;
  autoStart: boolean;
}

export interface TimeSegment {
  pageNumber: number;
  startTime: number;
  endTime?: number;
  duration?: number;
  notes?: string;
}

interface PresentationTimerProps {
  isPresenting: boolean;
  isPaused: boolean;
  startTime: number;
  currentPage: number;
  totalPages: number;
  settings: TimerSettings;
  onSettingsChange: (settings: TimerSettings) => void;
  onTimeWarning?: (timeRemaining: number) => void;
  className?: string;
}



export default function PresentationTimer({
  isPresenting,
  isPaused,
  startTime,
  currentPage,
  totalPages,
  settings,
  onSettingsChange,
  onTimeWarning,
  className
}: PresentationTimerProps) {
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [showSettings, setShowSettings] = useState(false);
  const [segments, setSegments] = useState<TimeSegment[]>([]);
  const [lastPageChangeTime, setLastPageChangeTime] = useState(startTime);
  const [warningShown, setWarningShown] = useState(false);

  // Update current time
  useEffect(() => {
    if (!isPresenting || isPaused) return;

    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, [isPresenting, isPaused]);

  // Track page changes for timing segments
  useEffect(() => {
    if (!isPresenting) return;

    const now = Date.now();
    
    // End previous segment
    setSegments(prev => {
      const updated = [...prev];
      if (updated.length > 0) {
        const lastSegment = updated[updated.length - 1];
        if (!lastSegment.endTime) {
          lastSegment.endTime = now;
          lastSegment.duration = now - lastSegment.startTime;
        }
      }
      return updated;
    });

    // Start new segment
    const newSegment: TimeSegment = {
      pageNumber: currentPage,
      startTime: now
    };
    
    setSegments(prev => [...prev, newSegment]);
    setLastPageChangeTime(now);
  }, [currentPage, isPresenting]);

  // Calculate times
  const elapsedTime = isPresenting ? currentTime - startTime : 0;
  const remainingTime = settings.totalDuration ? Math.max(0, settings.totalDuration - elapsedTime) : 0;
  const progressPercentage = settings.totalDuration ? (elapsedTime / settings.totalDuration) * 100 : 0;
  const currentPageTime = currentTime - lastPageChangeTime;

  // Check for warnings
  useEffect(() => {
    if (!settings.enableWarnings || !settings.totalDuration || warningShown) return;

    if (remainingTime <= settings.warningTime && remainingTime > 0) {
      setWarningShown(true);
      onTimeWarning?.(remainingTime);
    }
  }, [remainingTime, settings.enableWarnings, settings.totalDuration, settings.warningTime, warningShown, onTimeWarning]);

  // Reset warning when presentation restarts
  useEffect(() => {
    if (!isPresenting) {
      setWarningShown(false);
    }
  }, [isPresenting]);

  // Format time function
  const formatTime = useCallback((milliseconds: number) => {
    const totalSeconds = Math.floor(Math.abs(milliseconds) / 1000);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    const sign = milliseconds < 0 ? '-' : '';

    if (hours > 0) {
      return `${sign}${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${sign}${minutes}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  // Calculate average time per page
  const averageTimePerPage = segments.length > 0 
    ? segments.reduce((sum, segment) => sum + (segment.duration || 0), 0) / segments.length
    : 0;

  // Estimate remaining time based on current pace
  const estimatedRemainingTime = (totalPages - currentPage) * averageTimePerPage;

  // Get time status
  const getTimeStatus = () => {
    if (!settings.totalDuration) return 'neutral';
    
    const timeRatio = elapsedTime / settings.totalDuration;
    const pageRatio = currentPage / totalPages;
    
    if (timeRatio > pageRatio + 0.2) return 'behind';
    if (timeRatio < pageRatio - 0.2) return 'ahead';
    return 'on-track';
  };

  const timeStatus = getTimeStatus();

  if (!isPresenting) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Main timer display */}
      <Card className="p-4 bg-black/80 backdrop-blur-sm text-white">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            <span className="font-medium">Presentation Timer</span>
          </div>
          <Button
            onClick={() => setShowSettings(!showSettings)}
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-3">
          {/* Elapsed time */}
          {settings.showElapsed && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Elapsed:</span>
              <span className="text-xl font-mono">
                {formatTime(elapsedTime)}
              </span>
            </div>
          )}

          {/* Remaining time */}
          {settings.showRemaining && settings.totalDuration && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-300">Remaining:</span>
              <span className={`text-xl font-mono ${
                remainingTime <= settings.warningTime ? 'text-red-400' : 'text-white'
              }`}>
                {formatTime(remainingTime)}
              </span>
            </div>
          )}

          {/* Progress bar */}
          {settings.showProgress && settings.totalDuration && (
            <div className="space-y-1">
              <Progress 
                value={Math.min(progressPercentage, 100)} 
                className={`h-2 ${
                  progressPercentage > 100 ? 'bg-red-200' : 'bg-gray-200'
                }`}
              />
              <div className="flex justify-between text-xs text-gray-400">
                <span>0:00</span>
                <span>{formatTime(settings.totalDuration)}</span>
              </div>
            </div>
          )}

          {/* Current page timing */}
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-300">Current slide:</span>
            <span className="font-mono">{formatTime(currentPageTime)}</span>
          </div>

          {/* Pace indicator */}
          {settings.totalDuration && (
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-300">Pace:</span>
              <Badge 
                variant={
                  timeStatus === 'ahead' ? 'default' : 
                  timeStatus === 'behind' ? 'destructive' : 
                  'secondary'
                }
                className="text-xs"
              >
                {timeStatus === 'ahead' && <TrendingUp className="h-3 w-3 mr-1" />}
                {timeStatus === 'behind' && <AlertTriangle className="h-3 w-3 mr-1" />}
                {timeStatus === 'on-track' && <Target className="h-3 w-3 mr-1" />}
                {timeStatus.replace('-', ' ')}
              </Badge>
            </div>
          )}

          {/* Pause indicator */}
          {isPaused && (
            <div className="flex items-center justify-center gap-2 text-yellow-400">
              <Pause className="h-4 w-4" />
              <span className="text-sm">Paused</span>
            </div>
          )}
        </div>
      </Card>

      {/* Settings panel */}
      {showSettings && (
        <Card className="p-4 bg-black/90 backdrop-blur-sm text-white">
          <h3 className="font-medium mb-3">Timer Settings</h3>
          
          <div className="space-y-3">
            {/* Total duration */}
            <div>
              <label className="text-sm text-gray-300 block mb-1">
                Total Duration (minutes)
              </label>
              <Input
                type="number"
                value={settings.totalDuration ? Math.round(settings.totalDuration / 60000) : ''}
                onChange={(e) => {
                  const minutes = parseInt(e.target.value) || 0;
                  onSettingsChange({
                    ...settings,
                    totalDuration: minutes > 0 ? minutes * 60000 : undefined
                  });
                }}
                placeholder="No limit"
                className="bg-black/50 border-gray-600"
              />
            </div>

            {/* Warning time */}
            <div>
              <label className="text-sm text-gray-300 block mb-1">
                Warning Time (minutes)
              </label>
              <Input
                type="number"
                value={Math.round(settings.warningTime / 60000)}
                onChange={(e) => {
                  const minutes = parseInt(e.target.value) || 5;
                  onSettingsChange({
                    ...settings,
                    warningTime: minutes * 60000
                  });
                }}
                className="bg-black/50 border-gray-600"
              />
            </div>

            {/* Display options */}
            <div className="space-y-2">
              <label className="text-sm text-gray-300">Display Options</label>
              <div className="space-y-1">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={settings.showElapsed}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      showElapsed: e.target.checked
                    })}
                  />
                  Show elapsed time
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={settings.showRemaining}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      showRemaining: e.target.checked
                    })}
                  />
                  Show remaining time
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={settings.showProgress}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      showProgress: e.target.checked
                    })}
                  />
                  Show progress bar
                </label>
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={settings.enableWarnings}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      enableWarnings: e.target.checked
                    })}
                  />
                  Enable time warnings
                </label>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* Timing statistics */}
      {segments.length > 0 && (
        <Card className="p-3 bg-black/80 backdrop-blur-sm text-white">
          <h4 className="text-sm font-medium mb-2">Timing Stats</h4>
          <div className="text-xs space-y-1">
            <div className="flex justify-between">
              <span className="text-gray-300">Avg per slide:</span>
              <span className="font-mono">{formatTime(averageTimePerPage)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Est. remaining:</span>
              <span className="font-mono">{formatTime(estimatedRemainingTime)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Slides viewed:</span>
              <span>{segments.length} / {totalPages}</span>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
