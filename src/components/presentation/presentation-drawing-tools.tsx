"use client";

import React, { useRef, useEffect, useCallback, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  MousePointer,
  Pen,
  Highlighter,
  Eraser,
  Palette,
  Undo,
  Redo,
  Trash2,
  Target

} from 'lucide-react';
import type { DrawingStroke } from './presentation-mode';

export type DrawingTool = 'pointer' | 'pen' | 'highlighter' | 'laser' | 'eraser' | 'shape-circle' | 'shape-rectangle' | 'arrow' | 'text';

export interface DrawingSettings {
  penColor: string;
  penSize: number;
  highlighterColor: string;
  highlighterSize: number;
  laserColor: string;
  laserSize: number;
  eraserSize: number;
  opacity: number;
}

interface PresentationDrawingToolsProps {
  canvasRef: React.RefObject<HTMLCanvasElement>;
  currentTool: DrawingTool;
  onToolChange: (tool: DrawingTool) => void;
  settings: DrawingSettings;
  onSettingsChange: (settings: DrawingSettings) => void;
  strokes: DrawingStroke[];
  onStrokesChange: (strokes: DrawingStroke[]) => void;
  pageNumber: number;
  isPresenting: boolean;
  className?: string;
}

const DEFAULT_COLORS = [
  '#ff0000', '#00ff00', '#0000ff', '#ffff00', '#ff00ff', '#00ffff',
  '#ffffff', '#000000', '#808080', '#ffa500', '#800080', '#008000'
];

export default function PresentationDrawingTools({
  canvasRef,
  currentTool,
  onToolChange,
  settings,
  onSettingsChange,
  strokes,
  onStrokesChange,
  pageNumber,
  isPresenting,
  className
}: PresentationDrawingToolsProps) {
  const [isDrawing, setIsDrawing] = useState(false);
  const [currentStroke, setCurrentStroke] = useState<DrawingStroke | null>(null);
  const [undoStack, setUndoStack] = useState<DrawingStroke[][]>([]);
  const [redoStack, setRedoStack] = useState<DrawingStroke[][]>([]);
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [laserPosition, setLaserPosition] = useState<{ x: number; y: number } | null>(null);

  // Drawing state
  const isDrawingRef = useRef(false);
  const lastPointRef = useRef<{ x: number; y: number } | null>(null);

  // Get current page strokes
  const currentPageStrokes = strokes.filter(stroke => stroke.pageNumber === pageNumber);

  // Canvas drawing functions
  const drawStroke = useCallback((ctx: CanvasRenderingContext2D, stroke: DrawingStroke) => {
    if (stroke.points.length < 2) return;

    ctx.save();
    ctx.strokeStyle = stroke.color;
    ctx.lineWidth = stroke.size;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // Set blend mode for different tools
    switch (stroke.tool) {
      case 'highlighter':
        ctx.globalAlpha = 0.3;
        ctx.globalCompositeOperation = 'multiply';
        break;
      case 'laser':
        ctx.globalAlpha = 0.8;
        ctx.shadowColor = stroke.color;
        ctx.shadowBlur = 10;
        break;
      case 'pen':
      default:
        ctx.globalAlpha = 1;
        break;
    }

    ctx.beginPath();
    ctx.moveTo(stroke.points[0].x, stroke.points[0].y);

    for (let i = 1; i < stroke.points.length; i++) {
      const point = stroke.points[i];
      ctx.lineTo(point.x, point.y);
    }

    ctx.stroke();
    ctx.restore();
  }, []);

  // Redraw canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw all strokes for current page
    currentPageStrokes.forEach(stroke => {
      drawStroke(ctx, stroke);
    });

    // Draw current stroke being drawn
    if (currentStroke) {
      drawStroke(ctx, currentStroke);
    }

    // Draw laser pointer
    if (currentTool === 'laser' && laserPosition) {
      ctx.save();
      ctx.fillStyle = settings.laserColor;
      ctx.shadowColor = settings.laserColor;
      ctx.shadowBlur = 20;
      ctx.beginPath();
      ctx.arc(laserPosition.x, laserPosition.y, settings.laserSize, 0, 2 * Math.PI);
      ctx.fill();
      ctx.restore();
    }
  }, [canvasRef, currentPageStrokes, currentStroke, currentTool, laserPosition, settings.laserColor, settings.laserSize, drawStroke]);

  // Setup canvas
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const resizeCanvas = () => {
      const rect = canvas.getBoundingClientRect();
      canvas.width = rect.width;
      canvas.height = rect.height;
      redrawCanvas();
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    return () => {
      window.removeEventListener('resize', resizeCanvas);
    };
  }, [canvasRef, redrawCanvas]);

  // Redraw when strokes change
  useEffect(() => {
    redrawCanvas();
  }, [redrawCanvas]);

  // Get coordinates from mouse event
  const getCoordinates = useCallback((event: MouseEvent): { x: number; y: number } => {
    const canvas = canvasRef.current;
    if (!canvas) return { x: 0, y: 0 };

    const rect = canvas.getBoundingClientRect();
    return {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top
    };
  }, [canvasRef]);

  // Mouse event handlers
  const handleMouseDown = useCallback((event: MouseEvent) => {
    if (currentTool === 'pointer') return;

    const coords = getCoordinates(event);
    
    if (currentTool === 'laser') {
      setLaserPosition(coords);
      return;
    }

    if (currentTool === 'eraser') {
      // Find and remove strokes at this position
      const strokesToRemove = currentPageStrokes.filter(stroke => {
        return stroke.points.some(point => {
          const distance = Math.sqrt(
            Math.pow(point.x - coords.x, 2) + Math.pow(point.y - coords.y, 2)
          );
          return distance <= settings.eraserSize;
        });
      });

      if (strokesToRemove.length > 0) {
        const newStrokes = strokes.filter(stroke => !strokesToRemove.includes(stroke));
        onStrokesChange(newStrokes);
      }
      return;
    }

    // Start drawing
    const newStroke: DrawingStroke = {
      id: `stroke_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      pageNumber,
      points: [coords],
      color: currentTool === 'highlighter' ? settings.highlighterColor : settings.penColor,
      size: currentTool === 'highlighter' ? settings.highlighterSize : settings.penSize,
      tool: currentTool === 'highlighter' ? 'highlighter' : 'pen',
      timestamp: Date.now()
    };

    setCurrentStroke(newStroke);
    setIsDrawing(true);
    isDrawingRef.current = true;
    lastPointRef.current = coords;
  }, [currentTool, getCoordinates, pageNumber, settings, currentPageStrokes, strokes, onStrokesChange]);

  const handleMouseMove = useCallback((event: MouseEvent) => {
    const coords = getCoordinates(event);

    if (currentTool === 'laser') {
      setLaserPosition(coords);
      return;
    }

    if (!isDrawingRef.current || !currentStroke) return;

    // Add point to current stroke with smoothing
    const lastPoint = lastPointRef.current;
    if (lastPoint) {
      const distance = Math.sqrt(
        Math.pow(coords.x - lastPoint.x, 2) + Math.pow(coords.y - lastPoint.y, 2)
      );

      // Only add point if it's far enough from the last point (smoothing)
      if (distance > 2) {
        setCurrentStroke(prev => prev ? {
          ...prev,
          points: [...prev.points, coords]
        } : null);
        lastPointRef.current = coords;
      }
    }
  }, [currentTool, getCoordinates, currentStroke]);

  const handleMouseUp = useCallback(() => {
    if (currentTool === 'laser') {
      setLaserPosition(null);
      return;
    }

    if (currentStroke && isDrawingRef.current) {
      // Save stroke to history for undo/redo
      setUndoStack(prev => [...prev, strokes]);
      setRedoStack([]);

      // Add stroke to strokes
      onStrokesChange([...strokes, currentStroke]);
    }

    setCurrentStroke(null);
    setIsDrawing(false);
    isDrawingRef.current = false;
    lastPointRef.current = null;
  }, [currentTool, currentStroke, strokes, onStrokesChange]);

  // Setup mouse event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseUp);

    return () => {
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseUp);
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp]);

  // Undo/Redo functions
  const undo = useCallback(() => {
    if (undoStack.length > 0) {
      const previousState = undoStack[undoStack.length - 1];
      setRedoStack(prev => [strokes, ...prev]);
      setUndoStack(prev => prev.slice(0, -1));
      onStrokesChange(previousState);
    }
  }, [undoStack, strokes, onStrokesChange]);

  const redo = useCallback(() => {
    if (redoStack.length > 0) {
      const nextState = redoStack[0];
      setUndoStack(prev => [...prev, strokes]);
      setRedoStack(prev => prev.slice(1));
      onStrokesChange(nextState);
    }
  }, [redoStack, strokes, onStrokesChange]);

  // Clear all drawings on current page
  const clearPage = useCallback(() => {
    setUndoStack(prev => [...prev, strokes]);
    setRedoStack([]);
    const newStrokes = strokes.filter(stroke => stroke.pageNumber !== pageNumber);
    onStrokesChange(newStrokes);
  }, [strokes, pageNumber, onStrokesChange]);

  if (!isPresenting) return null;

  return (
    <div className={`absolute top-4 left-4 bg-black/80 backdrop-blur-sm rounded-lg p-2 ${className}`}>
      <div className="flex flex-col gap-2">
        {/* Tool selection */}
        <div className="flex gap-1">
          <Button
            onClick={() => onToolChange('pointer')}
            variant={currentTool === 'pointer' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <MousePointer className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => onToolChange('pen')}
            variant={currentTool === 'pen' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Pen className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => onToolChange('highlighter')}
            variant={currentTool === 'highlighter' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Highlighter className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => onToolChange('laser')}
            variant={currentTool === 'laser' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Target className="h-4 w-4" />
          </Button>
          <Button
            onClick={() => onToolChange('eraser')}
            variant={currentTool === 'eraser' ? 'default' : 'outline'}
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Eraser className="h-4 w-4" />
          </Button>
        </div>

        <Separator />

        {/* Color picker */}
        <div className="flex gap-1">
          <Button
            onClick={() => setShowColorPicker(!showColorPicker)}
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Palette className="h-4 w-4" />
          </Button>
          
          {/* Current color indicator */}
          <div
            className="w-8 h-8 rounded border-2 border-white/20"
            style={{
              backgroundColor: currentTool === 'highlighter' ? settings.highlighterColor : 
                              currentTool === 'laser' ? settings.laserColor : 
                              settings.penColor
            }}
          />
        </div>

        {/* Color palette */}
        {showColorPicker && (
          <Card className="p-2 bg-black/90">
            <div className="grid grid-cols-4 gap-1">
              {DEFAULT_COLORS.map(color => (
                <button
                  key={color}
                  className="w-6 h-6 rounded border border-white/20 hover:scale-110 transition-transform"
                  style={{ backgroundColor: color }}
                  onClick={() => {
                    if (currentTool === 'highlighter') {
                      onSettingsChange({ ...settings, highlighterColor: color });
                    } else if (currentTool === 'laser') {
                      onSettingsChange({ ...settings, laserColor: color });
                    } else {
                      onSettingsChange({ ...settings, penColor: color });
                    }
                    setShowColorPicker(false);
                  }}
                />
              ))}
            </div>
          </Card>
        )}

        {/* Size slider */}
        {currentTool !== 'pointer' && (
          <div className="w-32">
            <Slider
              value={[
                currentTool === 'highlighter' ? settings.highlighterSize :
                currentTool === 'laser' ? settings.laserSize :
                currentTool === 'eraser' ? settings.eraserSize :
                settings.penSize
              ]}
              onValueChange={([value]) => {
                if (currentTool === 'highlighter') {
                  onSettingsChange({ ...settings, highlighterSize: value });
                } else if (currentTool === 'laser') {
                  onSettingsChange({ ...settings, laserSize: value });
                } else if (currentTool === 'eraser') {
                  onSettingsChange({ ...settings, eraserSize: value });
                } else {
                  onSettingsChange({ ...settings, penSize: value });
                }
              }}
              max={currentTool === 'laser' ? 30 : currentTool === 'eraser' ? 50 : 20}
              min={1}
              step={1}
              className="w-full"
            />
          </div>
        )}

        <Separator />

        {/* Action buttons */}
        <div className="flex gap-1">
          <Button
            onClick={undo}
            disabled={undoStack.length === 0}
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            onClick={redo}
            disabled={redoStack.length === 0}
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Button
            onClick={clearPage}
            disabled={currentPageStrokes.length === 0}
            variant="outline"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
