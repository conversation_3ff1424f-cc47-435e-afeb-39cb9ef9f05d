"use client";

import { useState } from "react";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Bookmark,
  Trash2,
  Edit3,
  Check,
  X,
  Clock,
  BookmarkPlus,
  Plus,
  Search,
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface BookmarkItem {
  id: string;
  page: number;
  title: string;
  timestamp: number;
}

interface PDFBookmarksEnhancedProps {
  bookmarks: BookmarkItem[];
  currentPage: number;
  onPageSelect: (page: number) => void;
  onAddBookmark: () => void;
  onRemoveBookmark: (bookmarkId: string) => void;
  onUpdateBookmark: (bookmarkId: string, newTitle: string) => void;
}

export default function PDFBookmarksEnhanced({
  bookmarks,
  currentPage,
  onPageSelect,
  onAddBookmark,
  onRemoveBookmark,
  onUpdateBookmark,
}: PDFBookmarksEnhancedProps) {
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [sortBy, setSortBy] = useState<"page" | "title" | "date">("page");
  const [searchQuery, setSearchQuery] = useState("");

  const startEditing = (bookmark: BookmarkItem) => {
    setEditingId(bookmark.id);
    setEditTitle(bookmark.title);
  };

  const saveEdit = () => {
    if (editingId && editTitle.trim()) {
      onUpdateBookmark(editingId, editTitle.trim());
    }
    setEditingId(null);
    setEditTitle("");
  };

  const cancelEdit = () => {
    setEditingId(null);
    setEditTitle("");
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const filteredBookmarks = bookmarks.filter((bookmark) =>
    bookmark.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedBookmarks = [...filteredBookmarks].sort((a, b) => {
    switch (sortBy) {
      case "page":
        return a.page - b.page;
      case "title":
        return a.title.localeCompare(b.title);
      case "date":
        return b.timestamp - a.timestamp;
      default:
        return 0;
    }
  });

  return (
    <div className="h-full flex flex-col" data-testid="pdf-bookmarks">
      {/* Header */}
      <div className="p-4 border-b space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-sm">Bookmarks</h3>
          <Button size="sm" onClick={onAddBookmark} className="h-7">
            <Plus className="h-3 w-3 mr-1" />
            Add
          </Button>
        </div>

        {/* Search */}
        {bookmarks.length > 0 && (
          <div className="relative">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-muted-foreground" />
            <Input
              placeholder="Search bookmarks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-7 h-8 text-xs"
            />
          </div>
        )}

        {/* Sort options */}
        {bookmarks.length > 1 && (
          <div className="flex gap-1">
            <Button
              variant={sortBy === "page" ? "default" : "ghost"}
              size="sm"
              className="text-xs h-6"
              onClick={() => setSortBy("page")}
            >
              Page
            </Button>
            <Button
              variant={sortBy === "title" ? "default" : "ghost"}
              size="sm"
              className="text-xs h-6"
              onClick={() => setSortBy("title")}
            >
              Title
            </Button>
            <Button
              variant={sortBy === "date" ? "default" : "ghost"}
              size="sm"
              className="text-xs h-6"
              onClick={() => setSortBy("date")}
            >
              Recent
            </Button>
          </div>
        )}

        {filteredBookmarks.length !== bookmarks.length && (
          <div className="text-xs text-muted-foreground">
            Showing {filteredBookmarks.length} of {bookmarks.length} bookmarks
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {bookmarks.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 text-center h-full">
            <BookmarkPlus className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="font-medium text-lg mb-2">No Bookmarks Yet</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Save important pages for quick access
            </p>
            <Button onClick={onAddBookmark} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add First Bookmark
            </Button>
          </div>
        ) : filteredBookmarks.length === 0 ? (
          <div className="flex flex-col items-center justify-center p-6 text-center h-full">
            <Search className="h-8 w-8 text-muted-foreground mb-3" />
            <p className="text-sm text-muted-foreground">
              No bookmarks match your search
            </p>
          </div>
        ) : (
          <ScrollArea className="h-full">
            <div className="p-2 space-y-2">
              {sortedBookmarks.map((bookmark) => (
                <div
                  key={bookmark.id}
                  className={`group rounded-lg border p-3 transition-all hover:shadow-sm ${
                    bookmark.page === currentPage
                      ? "bg-primary/10 border-primary/20 shadow-sm"
                      : "bg-background hover:bg-muted/30"
                  }`}
                >
                  <div className="flex items-start gap-2">
                    <Bookmark
                      className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                        bookmark.page === currentPage
                          ? "text-primary fill-current"
                          : "text-muted-foreground"
                      }`}
                    />

                    <div className="flex-1 min-w-0">
                      {editingId === bookmark.id ? (
                        <div className="space-y-2">
                          <Input
                            value={editTitle}
                            onChange={(e) => setEditTitle(e.target.value)}
                            className="h-7 text-sm"
                            onKeyDown={(e) => {
                              if (e.key === "Enter") saveEdit();
                              if (e.key === "Escape") cancelEdit();
                            }}
                            autoFocus
                          />
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              className="h-6 text-xs"
                              onClick={saveEdit}
                            >
                              <Check className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 text-xs"
                              onClick={cancelEdit}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <>
                          <button
                            onClick={() => onPageSelect(bookmark.page)}
                            className="text-left w-full group-hover:text-primary transition-colors"
                          >
                            <div className="font-medium text-sm truncate mb-1">
                              {bookmark.title}
                            </div>
                            <div className="flex items-center gap-2 text-xs text-muted-foreground">
                              <Badge variant="outline" className="text-xs">
                                Page {bookmark.page}
                              </Badge>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {formatDate(bookmark.timestamp)}
                              </div>
                            </div>
                          </button>

                          <div className="flex items-center gap-1 mt-2 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              size="sm"
                              variant="ghost"
                              className="h-6 w-6 p-0"
                              onClick={() => startEditing(bookmark)}
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>

                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete Bookmark
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete &quot;
                                    {bookmark.title}&quot;? This action cannot be
                                    undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() =>
                                      onRemoveBookmark(bookmark.id)
                                    }
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </div>
    </div>
  );
}
