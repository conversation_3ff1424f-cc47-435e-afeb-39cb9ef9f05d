"use client";

import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PenTool, Settings } from "lucide-react";
import PDFFloatingToolbar from "./pdf-floating-toolbar";
import type { AnnotationType } from "../annotations/pdf-annotations";

interface FloatingToolbarManagerProps {
  selectedTool: AnnotationType | null;
  onToolSelect: (tool: AnnotationType | null) => void;
  selectedColor: string;
  onColorChange: (color: string) => void;
  annotationCount: number;
  onUndo?: () => void;
  onRedo?: () => void;
  onClearAll?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
}

export default function PDFFloatingToolbarManager({
  selectedTool,
  onToolSelect,
  selectedColor,
  onColorChange,
  annotationCount,
  onUndo,
  onRedo,
  onClearAll,
  canUndo,
  canRedo,
}: FloatingToolbarManagerProps) {
  const [isToolbarVisible, setIsToolbarVisible] = useState(false);
  const [toolbarPosition, setToolbarPosition] = useState({ x: 20, y: 100 });
  const [showToggleButton, setShowToggleButton] = useState(true);

  // Auto-show toolbar when annotation tool is selected
  useEffect(() => {
    if (selectedTool && !isToolbarVisible) {
      setIsToolbarVisible(true);
    }
  }, [selectedTool, isToolbarVisible]);

  // Save toolbar position to localStorage
  useEffect(() => {
    const savedPosition = localStorage.getItem("pdf-floating-toolbar-position");
    if (savedPosition) {
      try {
        const position = JSON.parse(savedPosition);
        setToolbarPosition(position);
      } catch (error) {
        console.warn("Failed to load toolbar position:", error);
      }
    }
  }, []);

  const handlePositionChange = useCallback(
    (position: { x: number; y: number }) => {
      setToolbarPosition(position);
      localStorage.setItem(
        "pdf-floating-toolbar-position",
        JSON.stringify(position)
      );
    },
    []
  );

  const toggleToolbar = useCallback(() => {
    setIsToolbarVisible(!isToolbarVisible);
  }, [isToolbarVisible]);

  // Auto-hide toggle button when toolbar is visible
  useEffect(() => {
    if (isToolbarVisible) {
      const timer = setTimeout(() => setShowToggleButton(false), 2000);
      return () => clearTimeout(timer);
    } else {
      setShowToggleButton(true);
    }
  }, [isToolbarVisible]);

  return (
    <TooltipProvider>
      {/* Toggle Button */}
      {showToggleButton && !isToolbarVisible && (
        <div className="fixed bottom-4 right-4 z-40">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={toggleToolbar}
                className="h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-300"
                size="sm"
              >
                <PenTool className="h-5 w-5" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">Show Annotation Toolbar</TooltipContent>
          </Tooltip>
        </div>
      )}

      {/* Floating Toolbar */}
      <PDFFloatingToolbar
        selectedTool={selectedTool}
        onToolSelect={onToolSelect}
        selectedColor={selectedColor}
        onColorChange={onColorChange}
        isVisible={isToolbarVisible}
        onToggleVisibility={toggleToolbar}
        position={toolbarPosition}
        onPositionChange={handlePositionChange}
        annotationCount={annotationCount}
        onUndo={onUndo}
        onRedo={onRedo}
        onClearAll={onClearAll}
        canUndo={canUndo}
        canRedo={canRedo}
      />

      {/* Show toggle button on hover when toolbar is visible */}
      {isToolbarVisible && (
        <div
          className="fixed bottom-4 right-4 z-30 opacity-0 hover:opacity-100 transition-opacity duration-300"
          onMouseEnter={() => setShowToggleButton(true)}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                onClick={toggleToolbar}
                className="h-10 w-10 rounded-full shadow-md"
                size="sm"
              >
                <Settings className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">Hide Annotation Toolbar</TooltipContent>
          </Tooltip>
        </div>
      )}
    </TooltipProvider>
  );
}
