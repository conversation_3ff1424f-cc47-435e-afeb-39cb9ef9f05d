"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import {
  X,
  FileText,
  Plus,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  AlertCircle,
  Loader2,
  Pin,
  PinOff
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { DocumentInstance } from '@/lib/types/pdf';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  TooltipProvider,
} from '@/components/ui/tooltip';

export type TabDisplayMode = 'full' | 'compact' | 'minimal' | 'icons-only';
export type TabLayout = 'horizontal' | 'vertical' | 'grid';

interface DocumentTabsProps {
  documents: DocumentInstance[];
  activeDocumentId: string | null;
  onDocumentSelect: (documentId: string) => void;
  onDocumentClose: (documentId: string) => void;
  onNewDocument: () => void;
  // Enhanced features (optional for backward compatibility)
  onDocumentReorder?: (fromIndex: number, toIndex: number) => void;
  displayMode?: TabDisplayMode;
  layout?: TabLayout;
  maxVisibleTabs?: number;
  showPinnedTabs?: boolean;
  pinnedDocuments?: string[];
  onDocumentPin?: (documentId: string, pinned: boolean) => void;
  className?: string;
}

interface TabItemProps {
  document: DocumentInstance;
  isActive: boolean;
  isPinned?: boolean;
  displayMode?: TabDisplayMode;
  onSelect: () => void;
  onClose: (e: React.MouseEvent) => void;
  onPin?: (e: React.MouseEvent) => void;
  isDragging?: boolean;
  onDragStart?: (e: React.DragEvent) => void;
  onDragEnd?: () => void;
  isCompact?: boolean; // Legacy prop for backward compatibility
}

const TabItem: React.FC<TabItemProps> = ({
  document,
  isActive,
  isPinned = false,
  displayMode,
  onSelect,
  onClose,
  onPin,
  isDragging = false,
  onDragStart,
  onDragEnd,
  isCompact = false // Legacy prop for backward compatibility
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const getTabIcon = () => {
    if (document.isLoading) {
      return <Loader2 className="h-3 w-3 animate-spin" />;
    }
    if (document.hasError) {
      return <AlertCircle className="h-3 w-3 text-destructive" />;
    }
    return <FileText className="h-3 w-3" />;
  };

  const getTabTitle = () => {
    // Use displayMode if provided, otherwise fall back to legacy isCompact
    if (displayMode === 'icons-only') return null;

    const maxLength = displayMode === 'minimal' ? 8
      : displayMode === 'compact' ? 12
      : displayMode === 'full' ? 20
      : isCompact ? 15 : 25; // Legacy fallback

    return document.title.length > maxLength
      ? `${document.title.substring(0, maxLength)}...`
      : document.title;
  };

  const getTabWidth = () => {
    if (!displayMode) return ''; // Legacy mode - no fixed width

    switch (displayMode) {
      case 'icons-only':
        return 'w-8';
      case 'minimal':
        return 'w-16';
      case 'compact':
        return 'w-24';
      case 'full':
        return 'w-32';
      default:
        return 'w-32';
    }
  };

  return (
    <div
      className={cn(
        "relative flex items-center gap-1 px-2 py-1 text-sm border-b-2 cursor-pointer transition-all duration-200 min-w-0 shrink-0",
        getTabWidth(),
        isActive
          ? "border-primary bg-background text-foreground"
          : "border-transparent bg-muted/50 text-muted-foreground hover:bg-muted hover:text-foreground",
        document.hasError && "border-destructive/50",
        isDragging && "opacity-50 scale-95",
        isPinned && "bg-primary/5",
        // Legacy compact styling
        !displayMode && isCompact && "px-2 py-1",
        !displayMode && !isCompact && "px-3 py-2 gap-2"
      )}
      onClick={onSelect}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      draggable={onDragStart ? true : false}
      onDragStart={onDragStart}
      onDragEnd={onDragEnd}
      title={`${document.title}${document.hasError ? ' (Error)' : ''}${document.isLoading ? ' (Loading...)' : ''}${isPinned ? ' (Pinned)' : ''}`}
    >
      {/* Pin indicator */}
      {isPinned && (
        <div className="absolute -top-1 -left-1 w-2 h-2 bg-primary rounded-full" />
      )}

      {getTabIcon()}

      {displayMode !== 'icons-only' && getTabTitle() && (
        <span className="truncate flex-1 min-w-0 text-xs">
          {getTabTitle()}
        </span>
      )}

      {/* Legacy title display for backward compatibility */}
      {!displayMode && (
        <span className="truncate flex-1 min-w-0">
          {getTabTitle()}
        </span>
      )}

      {/* Action buttons */}
      {(isHovered || isActive) && displayMode !== 'icons-only' && (
        <div className="flex items-center gap-0.5">
          {/* Pin button */}
          {onPin && (
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-primary/20"
              onClick={onPin}
              title={isPinned ? "Unpin document" : "Pin document"}
            >
              {isPinned ? (
                <PinOff className="h-2.5 w-2.5" />
              ) : (
                <Pin className="h-2.5 w-2.5" />
              )}
            </Button>
          )}

          {/* Close button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
            onClick={onClose}
            title="Close document"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </div>
      )}

      {/* Legacy close button for backward compatibility */}
      {!displayMode && (isHovered || isActive) && (
        <Button
          variant="ghost"
          size="sm"
          className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
          onClick={onClose}
          title="Close document"
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  );
};

export default function DocumentTabs({
  documents,
  activeDocumentId,
  onDocumentSelect,
  onDocumentClose,
  onNewDocument,
  // Enhanced features (optional for backward compatibility)
  onDocumentReorder,
  displayMode = 'compact',
  layout = 'horizontal',
  maxVisibleTabs = 6,
  showPinnedTabs = true,
  pinnedDocuments = [],
  onDocumentPin,
  className
}: DocumentTabsProps) {
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropTargetIndex, setDropTargetIndex] = useState<number | null>(null);

  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const tabsScrollRef = useRef<HTMLDivElement>(null);

  // Separate pinned and unpinned documents
  const pinnedDocs = showPinnedTabs
    ? documents.filter(doc => pinnedDocuments.includes(doc.id))
    : [];
  const unpinnedDocs = documents.filter(doc => !pinnedDocuments.includes(doc.id));
  const allDocs = [...pinnedDocs, ...unpinnedDocs];

  // Check if scrolling is needed
  useEffect(() => {
    const checkScrollNeeded = () => {
      if (tabsContainerRef.current && tabsScrollRef.current) {
        const containerWidth = tabsContainerRef.current.clientWidth;
        const scrollWidth = tabsScrollRef.current.scrollWidth;
        setShowScrollButtons(scrollWidth > containerWidth);
      }
    };

    checkScrollNeeded();
    window.addEventListener('resize', checkScrollNeeded);
    return () => window.removeEventListener('resize', checkScrollNeeded);
  }, [documents, displayMode]);

  const scrollTabs = (direction: 'left' | 'right') => {
    if (!tabsScrollRef.current) return;
    
    const scrollAmount = 200;
    const newPosition = direction === 'left' 
      ? Math.max(0, scrollPosition - scrollAmount)
      : scrollPosition + scrollAmount;
    
    tabsScrollRef.current.scrollTo({ left: newPosition, behavior: 'smooth' });
    setScrollPosition(newPosition);
  };

  const handleTabClose = useCallback((e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    onDocumentClose(documentId);
  }, [onDocumentClose]);

  // Handle tab pin
  const handleTabPin = useCallback((e: React.MouseEvent, documentId: string) => {
    e.stopPropagation();
    if (onDocumentPin) {
      const isPinned = pinnedDocuments.includes(documentId);
      onDocumentPin(documentId, !isPinned);
    }
  }, [onDocumentPin, pinnedDocuments]);

  // Handle drag and drop
  const handleDragStart = useCallback((e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  }, []);

  const handleDragEnd = useCallback(() => {
    if (draggedIndex !== null && dropTargetIndex !== null && onDocumentReorder) {
      onDocumentReorder(draggedIndex, dropTargetIndex);
    }
    setDraggedIndex(null);
    setDropTargetIndex(null);
  }, [draggedIndex, dropTargetIndex, onDocumentReorder]);

  const visibleDocuments = allDocs.slice(0, maxVisibleTabs);
  const hiddenDocuments = allDocs.slice(maxVisibleTabs);

  if (documents.length === 0) {
    return (
      <Card className={cn("border-b rounded-none", className)}>
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center gap-2 text-muted-foreground">
            <FileText className="h-4 w-4" />
            <span className="text-sm">No documents open</span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onNewDocument}
            className="h-8 px-3"
          >
            <Plus className="h-4 w-4 mr-1" />
            Open Document
          </Button>
        </div>
      </Card>
    );
  }

  // Grid layout for vertical/grid modes
  if (layout === 'grid' || layout === 'vertical') {
    return (
      <TooltipProvider>
        <Card className={cn("border-r rounded-none w-64", className)}>
          <div className="p-2 border-b">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">Documents</h3>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onNewDocument}
                  className="h-6 w-6 p-0"
                  title="Open new document"
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>

          {/* Document list */}
          <div className="flex-1 overflow-auto">
            {allDocs.map((document) => (
              <div
                key={document.id}
                className={cn(
                  "flex items-center gap-2 p-2 hover:bg-muted cursor-pointer",
                  document.id === activeDocumentId && "bg-primary/10"
                )}
                onClick={() => onDocumentSelect(document.id)}
              >
                <FileText className="h-4 w-4 shrink-0" />
                <span className="truncate flex-1 text-sm">{document.title}</span>
                {pinnedDocuments.includes(document.id) && (
                  <Pin className="h-3 w-3 text-primary shrink-0" />
                )}
              </div>
            ))}
          </div>
        </Card>
      </TooltipProvider>
    );
  }

  return (
    <Card className={cn("border-b rounded-none", className)}>
      <div className="flex items-center">
        {/* Scroll left button */}
        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('left')}
            disabled={scrollPosition === 0}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        )}

        {/* Tabs container */}
        <div 
          ref={tabsContainerRef}
          className="flex-1 overflow-hidden"
        >
          <div
            ref={tabsScrollRef}
            className="flex overflow-x-auto scrollbar-hide"
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {visibleDocuments.map((document, index) => (
              <TabItem
                key={document.id}
                document={document}
                isActive={document.id === activeDocumentId}
                isPinned={pinnedDocuments.includes(document.id)}
                displayMode={displayMode}
                onSelect={() => onDocumentSelect(document.id)}
                onClose={(e) => handleTabClose(e, document.id)}
                onPin={onDocumentPin ? (e) => handleTabPin(e, document.id) : undefined}
                isDragging={draggedIndex === index}
                onDragStart={onDocumentReorder ? (e) => handleDragStart(e, index) : undefined}
                onDragEnd={onDocumentReorder ? handleDragEnd : undefined}
                isCompact={!displayMode && documents.length > 4} // Legacy fallback
              />
            ))}
          </div>
        </div>

        {/* Scroll right button */}
        {showScrollButtons && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 shrink-0"
            onClick={() => scrollTabs('right')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        )}

        {/* Overflow menu for hidden tabs */}
        {hiddenDocuments.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 shrink-0"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {hiddenDocuments.map((document) => (
                <DropdownMenuItem
                  key={document.id}
                  onClick={() => onDocumentSelect(document.id)}
                  className="flex items-center gap-2"
                >
                  {document.isLoading ? (
                    <Loader2 className="h-3 w-3 animate-spin" />
                  ) : document.hasError ? (
                    <AlertCircle className="h-3 w-3 text-destructive" />
                  ) : (
                    <FileText className="h-3 w-3" />
                  )}
                  <span className="truncate flex-1">{document.title}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                    onClick={(e) => handleTabClose(e, document.id)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        <Separator orientation="vertical" className="h-6" />

        {/* New document button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewDocument}
          className="h-8 px-3 shrink-0"
          title="Open new document"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </Card>
  );
}
