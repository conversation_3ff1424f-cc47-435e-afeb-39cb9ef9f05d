import React, { useRef, useState, useCallback, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface GestureState {
  isPinching: boolean
  isSwipingHorizontally: boolean
  isSwipingVertically: boolean
  startDistance: number
  startScale: number
  startX: number
  startY: number
  deltaX: number
  deltaY: number
}

interface UseGesturesProps {
  onZoom?: (scale: number, center: { x: number; y: number }) => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onPan?: (deltaX: number, deltaY: number) => void
  onTap?: (x: number, y: number) => void
  onDoubleTap?: (x: number, y: number) => void
  
  // Configuration
  swipeThreshold?: number
  pinchThreshold?: number
  doubleTapDelay?: number
  minScale?: number
  maxScale?: number
  
  // Current state
  currentScale?: number
  enabled?: boolean
}

export const useGestures = ({
  onZoom,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  onPan,
  onTap,
  onDoubleTap,
  swipeThreshold = 50,
  pinchThreshold = 10,
  doubleTapDelay = 300,
  minScale = 0.5,
  maxScale = 3.0,
  currentScale = 1.0,
  enabled = true
}: UseGesturesProps) => {
  const gestureStateRef = useRef<GestureState>({
    isPinching: false,
    isSwipingHorizontally: false,
    isSwipingVertically: false,
    startDistance: 0,
    startScale: currentScale,
    startX: 0,
    startY: 0,
    deltaX: 0,
    deltaY: 0
  })

  const lastTapRef = useRef<{ time: number; x: number; y: number } | null>(null)
  const touchesRef = useRef<Touch[]>([])

  const getDistance = useCallback((touch1: Touch, touch2: Touch): number => {
    const dx = touch2.clientX - touch1.clientX
    const dy = touch2.clientY - touch1.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  const getCenter = useCallback((touch1: Touch, touch2: Touch) => ({
    x: (touch1.clientX + touch2.clientX) / 2,
    y: (touch1.clientY + touch2.clientY) / 2
  }), [])

  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (!enabled) return
    
    touchesRef.current = Array.from(e.touches)
    const touch = e.touches[0]
    
    gestureStateRef.current.startX = touch.clientX
    gestureStateRef.current.startY = touch.clientY
    gestureStateRef.current.deltaX = 0
    gestureStateRef.current.deltaY = 0

    if (e.touches.length === 2) {
      // Start pinch gesture
      const distance = getDistance(e.touches[0], e.touches[1])
      gestureStateRef.current.isPinching = true
      gestureStateRef.current.startDistance = distance
      gestureStateRef.current.startScale = currentScale
    }
  }, [enabled, currentScale, getDistance])

  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!enabled) return
    
    e.preventDefault() // Prevent default scrolling
    
    const touch = e.touches[0]
    gestureStateRef.current.deltaX = touch.clientX - gestureStateRef.current.startX
    gestureStateRef.current.deltaY = touch.clientY - gestureStateRef.current.startY

    if (e.touches.length === 2 && gestureStateRef.current.isPinching) {
      // Handle pinch zoom
      const distance = getDistance(e.touches[0], e.touches[1])
      const scale = (distance / gestureStateRef.current.startDistance) * gestureStateRef.current.startScale
      const clampedScale = Math.max(minScale, Math.min(maxScale, scale))
      
      if (Math.abs(distance - gestureStateRef.current.startDistance) > pinchThreshold) {
        const center = getCenter(e.touches[0], e.touches[1])
        onZoom?.(clampedScale, center)
      }
    } else if (e.touches.length === 1) {
      // Handle single touch gestures
      const absX = Math.abs(gestureStateRef.current.deltaX)
      const absY = Math.abs(gestureStateRef.current.deltaY)

      if (absX > absY && absX > 10) {
        // Horizontal swipe/pan
        gestureStateRef.current.isSwipingHorizontally = true
        onPan?.(gestureStateRef.current.deltaX, 0)
      } else if (absY > absX && absY > 10) {
        // Vertical swipe/pan
        gestureStateRef.current.isSwipingVertically = true
        onPan?.(0, gestureStateRef.current.deltaY)
      }
    }
  }, [enabled, onZoom, onPan, getDistance, getCenter, minScale, maxScale, pinchThreshold])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!enabled) return

    const touch = touchesRef.current[0]
    
    // Handle swipe gestures
    if (gestureStateRef.current.isSwipingHorizontally) {
      if (Math.abs(gestureStateRef.current.deltaX) > swipeThreshold) {
        if (gestureStateRef.current.deltaX > 0) {
          onSwipeRight?.()
        } else {
          onSwipeLeft?.()
        }
      }
    } else if (gestureStateRef.current.isSwipingVertically) {
      if (Math.abs(gestureStateRef.current.deltaY) > swipeThreshold) {
        if (gestureStateRef.current.deltaY > 0) {
          onSwipeDown?.()
        } else {
          onSwipeUp?.()
        }
      }
    } else if (e.changedTouches.length === 1 && 
               Math.abs(gestureStateRef.current.deltaX) < 10 && 
               Math.abs(gestureStateRef.current.deltaY) < 10) {
      // Handle tap gestures
      const now = Date.now()
      const tapX = touch.clientX
      const tapY = touch.clientY

      if (lastTapRef.current && 
          now - lastTapRef.current.time < doubleTapDelay &&
          Math.abs(tapX - lastTapRef.current.x) < 20 &&
          Math.abs(tapY - lastTapRef.current.y) < 20) {
        // Double tap
        onDoubleTap?.(tapX, tapY)
        lastTapRef.current = null
      } else {
        // Single tap
        setTimeout(() => {
          if (lastTapRef.current?.time === now) {
            onTap?.(tapX, tapY)
          }
        }, doubleTapDelay)
        lastTapRef.current = { time: now, x: tapX, y: tapY }
      }
    }

    // Reset gesture state
    gestureStateRef.current = {
      isPinching: false,
      isSwipingHorizontally: false,
      isSwipingVertically: false,
      startDistance: 0,
      startScale: currentScale,
      startX: 0,
      startY: 0,
      deltaX: 0,
      deltaY: 0
    }
  }, [enabled, onSwipeLeft, onSwipeRight, onSwipeUp, onSwipeDown, onTap, onDoubleTap, swipeThreshold, doubleTapDelay, currentScale])

  return {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  }
}

interface GestureEnabledPageProps {
  children: React.ReactNode
  onZoom?: (scale: number, center: { x: number; y: number }) => void
  onNavigateNext?: () => void
  onNavigatePrev?: () => void
  onToggleFullscreen?: () => void
  onShowContextMenu?: (x: number, y: number) => void
  currentScale?: number
  className?: string
  enableGestures?: boolean
}

export const GestureEnabledPage: React.FC<GestureEnabledPageProps> = ({
  children,
  onZoom,
  onNavigateNext,
  onNavigatePrev,
  onShowContextMenu,
  currentScale = 1.0,
  className,
  enableGestures = true
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isInteracting, setIsInteracting] = useState(false)

  const gestures = useGestures({
    onZoom: (scale, center) => {
      onZoom?.(scale, center)
    },
    onSwipeLeft: () => {
      onNavigateNext?.()
    },
    onSwipeRight: () => {
      onNavigatePrev?.()
    },
    onDoubleTap: (x, y) => {
      // Double tap to zoom or show context menu
      if (currentScale === 1.0) {
        onZoom?.(2.0, { x, y })
      } else {
        onZoom?.(1.0, { x, y })
      }
    },
    onTap: (x, y) => {
      onShowContextMenu?.(x, y)
    },
    currentScale,
    enabled: enableGestures
  })

  useEffect(() => {
    const container = containerRef.current
    if (!container || !enableGestures) return

    // Add passive event listeners for better performance
    container.addEventListener('touchstart', gestures.onTouchStart, { passive: false })
    container.addEventListener('touchmove', gestures.onTouchMove, { passive: false })
    container.addEventListener('touchend', gestures.onTouchEnd, { passive: true })

    return () => {
      container.removeEventListener('touchstart', gestures.onTouchStart)
      container.removeEventListener('touchmove', gestures.onTouchMove)
      container.removeEventListener('touchend', gestures.onTouchEnd)
    }
  }, [gestures, enableGestures])

  const handleInteractionStart = useCallback(() => {
    setIsInteracting(true)
  }, [])

  const handleInteractionEnd = useCallback(() => {
    setIsInteracting(false)
  }, [])

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative select-none touch-manipulation",
        "transition-transform duration-200 ease-out",
        isInteracting && "transition-none",
        className
      )}
      onMouseDown={handleInteractionStart}
      onMouseUp={handleInteractionEnd}
      onTouchStart={handleInteractionStart}
      onTouchEnd={handleInteractionEnd}
      style={{
        WebkitUserSelect: 'none',
        WebkitTouchCallout: 'none',
        WebkitTapHighlightColor: 'transparent'
      }}
    >
      {children}
      
      {/* Visual feedback for interactions */}
      {isInteracting && (
        <div className="absolute inset-0 pointer-events-none">
          <div className="w-full h-full border-2 border-primary/20 rounded animate-pulse" />
        </div>
      )}
    </div>
  )
}

export default GestureEnabledPage