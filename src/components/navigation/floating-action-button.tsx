import React, { useState, useEffect, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import {
  Plus,
  Edit3,
  Bookmark,
  Search,
  Download,
  Layers,
  MessageSquare,
  Undo,
  Redo,
  ChevronUp,
  X
} from 'lucide-react'

interface FloatingAction {
  id: string
  icon: React.ComponentType<{ className?: string }>
  label: string
  action: () => void
  shortcut?: string
  color?: string
  disabled?: boolean
  badge?: string | number
}

interface FloatingActionButtonProps {
  actions: FloatingAction[]
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  autoHide?: boolean
  autoHideDelay?: number
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  actions,
  position = 'bottom-right',
  autoHide = true,
  autoHideDelay = 3000,
  size = 'md',
  className
}) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [isVisible, setIsVisible] = useState(true)
  const [lastActivity, setLastActivity] = useState(Date.now())

  // Auto-hide functionality
  useEffect(() => {
    if (!autoHide) return

    const handleActivity = () => {
      setLastActivity(Date.now())
      setIsVisible(true)
    }

    const checkActivity = () => {
      if (Date.now() - lastActivity > autoHideDelay && !isExpanded) {
        setIsVisible(false)
      }
    }

    // Listen for user activity
    const events = ['mousemove', 'touchstart', 'scroll', 'keydown']
    events.forEach(event => {
      document.addEventListener(event, handleActivity, { passive: true })
    })

    const interval = setInterval(checkActivity, 1000)

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity)
      })
      clearInterval(interval)
    }
  }, [autoHide, autoHideDelay, isExpanded, lastActivity])

  const handleToggle = useCallback(() => {
    setIsExpanded(prev => !prev)
    setLastActivity(Date.now())
  }, [])

  const handleActionClick = useCallback((action: FloatingAction) => {
    action.action()
    setIsExpanded(false)
    setLastActivity(Date.now())
  }, [])

  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  }

  const sizeClasses = {
    sm: 'size-12',
    md: 'size-14',
    lg: 'size-16'
  }

  const iconSizeClasses = {
    sm: 'size-4',
    md: 'size-5',
    lg: 'size-6'
  }

  if (!isVisible && autoHide) {
    return (
      <div 
        className={cn(
          "fixed z-50 transition-all duration-300 ease-in-out",
          positionClasses[position],
          "opacity-20 hover:opacity-100",
          className
        )}
        onMouseEnter={() => setIsVisible(true)}
      >
        <Button
          variant="default"
          size="sm"
          className="rounded-full size-8 shadow-lg"
          onClick={() => setIsVisible(true)}
        >
          <ChevronUp className="size-3" />
        </Button>
      </div>
    )
  }

  return (
    <div 
      className={cn(
        "fixed z-50 flex flex-col items-center gap-3",
        positionClasses[position],
        className
      )}
    >
      {/* Action buttons */}
      <div className={cn(
        "flex flex-col gap-2 transition-all duration-300 ease-in-out",
        isExpanded ? "opacity-100 scale-100" : "opacity-0 scale-95 pointer-events-none",
        position.includes('bottom') ? "flex-col-reverse" : "flex-col"
      )}>
        {actions.map((action, index) => (
          <div
            key={action.id}
            className="relative group"
            style={{
              transitionDelay: isExpanded ? `${index * 50}ms` : '0ms'
            }}
          >
            <Button
              variant="secondary"
              size="sm"
              onClick={() => handleActionClick(action)}
              disabled={action.disabled}
              className={cn(
                "rounded-full shadow-lg hover:shadow-xl transition-all duration-200",
                sizeClasses[size],
                action.color && `bg-${action.color} hover:bg-${action.color}/90`,
                action.disabled && "opacity-50 cursor-not-allowed"
              )}
              title={`${action.label}${action.shortcut ? ` (${action.shortcut})` : ''}`}
            >
              <action.icon className={iconSizeClasses[size]} />
              {action.badge && (
                <div className="absolute -top-1 -right-1 bg-destructive text-destructive-foreground text-xs rounded-full min-w-[1.25rem] h-5 flex items-center justify-center px-1">
                  {action.badge}
                </div>
              )}
            </Button>

            {/* Tooltip */}
            <div className={cn(
              "absolute whitespace-nowrap bg-popover text-popover-foreground px-2 py-1 rounded text-sm shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10",
              position.includes('right') ? "right-full mr-3" : "left-full ml-3",
              "top-1/2 -translate-y-1/2"
            )}>
              {action.label}
              {action.shortcut && (
                <span className="ml-2 text-muted-foreground">
                  {action.shortcut}
                </span>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Main toggle button */}
      <Button
        variant="default"
        onClick={handleToggle}
        className={cn(
          "rounded-full shadow-lg hover:shadow-xl transition-all duration-300",
          sizeClasses[size],
          isExpanded && "rotate-45"
        )}
      >
        {isExpanded ? (
          <X className={iconSizeClasses[size]} />
        ) : (
          <Plus className={iconSizeClasses[size]} />
        )}
      </Button>
    </div>
  )
}

// Predefined action sets for common scenarios
export const createAnnotationActions = (handlers: {
  onHighlight: () => void
  onNote: () => void
  onBookmark: () => void
  onUndo: () => void
  onRedo: () => void
}): FloatingAction[] => [
  {
    id: 'highlight',
    icon: Edit3,
    label: 'Highlight',
    action: handlers.onHighlight,
    shortcut: 'H',
    color: 'yellow-500'
  },
  {
    id: 'note',
    icon: MessageSquare,
    label: 'Add Note',
    action: handlers.onNote,
    shortcut: 'N',
    color: 'blue-500'
  },
  {
    id: 'bookmark',
    icon: Bookmark,
    label: 'Bookmark',
    action: handlers.onBookmark,
    shortcut: 'Ctrl+D'
  },
  {
    id: 'undo',
    icon: Undo,
    label: 'Undo',
    action: handlers.onUndo,
    shortcut: 'Ctrl+Z'
  },
  {
    id: 'redo',
    icon: Redo,
    label: 'Redo',
    action: handlers.onRedo,
    shortcut: 'Ctrl+Y'
  }
]

export const createReadingActions = (handlers: {
  onSearch: () => void
  onBookmark: () => void
  onDownload: () => void
}): FloatingAction[] => [
  {
    id: 'search',
    icon: Search,
    label: 'Search',
    action: handlers.onSearch,
    shortcut: 'Ctrl+F'
  },
  {
    id: 'bookmark',
    icon: Bookmark,
    label: 'Bookmark',
    action: handlers.onBookmark,
    shortcut: 'Ctrl+D'
  },
  {
    id: 'download',
    icon: Download,
    label: 'Download',
    action: handlers.onDownload,
    shortcut: 'Ctrl+S'
  }
]

export const createFormActions = (handlers: {
  onFillForm: () => void
  onValidate: () => void
  onSave: () => void
}): FloatingAction[] => [
  {
    id: 'fill-form',
    icon: Layers,
    label: 'Fill Form',
    action: handlers.onFillForm,
    shortcut: 'F'
  },
  {
    id: 'validate',
    icon: Edit3,
    label: 'Validate',
    action: handlers.onValidate,
    shortcut: 'V'
  },
  {
    id: 'save',
    icon: Download,
    label: 'Save Form',
    action: handlers.onSave,
    shortcut: 'Ctrl+S'
  }
]

export default FloatingActionButton