"use client";

import React, {
  useState,
  useCallback,
  useEffect,
  useMemo,
} from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { cn } from "@/lib/utils";
import {
  Accessibility,
  Eye,
  Volume2,
  Play,
  Pause,
  Square,
  Zap,
  Check,
  AlertTriangle,
  Navigation,
  Focus,
  RotateCcw,
  Info,
  BookOpen,
  Search,
} from "lucide-react";
import { toast } from "sonner";

// Enhanced accessibility types
export interface AccessibilitySettings {
  // Visual settings
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  letterSpacing: number;
  wordSpacing: number;
  contrast: "normal" | "high" | "higher" | "inverted" | "custom";
  colorBlindMode:
    | "none"
    | "protanopia"
    | "deuteranopia"
    | "tritanopia"
    | "monochrome";
  brightness: number;
  saturation: number;

  // Text enhancement
  dyslexiaFont: boolean;
  textBackground: boolean;
  textBackgroundColor: string;
  textColor: string;
  linkHighlight: boolean;

  // Motion and interaction
  reducedMotion: boolean;
  autoScrolling: boolean;
  scrollSpeed: number;
  pauseOnHover: boolean;

  // Audio settings
  textToSpeech: boolean;
  speechRate: number;
  speechPitch: number;
  speechVolume: number;
  highlightWhileSpeaking: boolean;
  speechVoice: string;
  speechLanguage: string;

  // Navigation and interaction
  keyboardNavigation: boolean;
  focusIndicator: boolean;
  skipToContent: boolean;
  tocNavigation: boolean;
  singleKeyShortcuts: boolean;
  clickHelper: boolean;

  // Screen reader optimization
  screenReader: boolean;
  ariaDescriptions: boolean;
  landmarkNavigation: boolean;
  readingOrder: boolean;

  // Cognitive assistance
  readingGuide: boolean;
  readingGuideColor: string;
  readingGuideThickness: number;
  wordSpacingEnhancement: boolean;
  sentenceSpacing: boolean;
  comprehensionMode: boolean;

  // Advanced features
  magnification: number;
  magnificationFollow: boolean;
  mouseTracking: boolean;
  cursorSize: number;
  customCursor: boolean;
}

export interface AccessibilityIssue {
  id: string;
  type: "error" | "warning" | "info" | "enhancement";
  severity: "critical" | "major" | "minor" | "suggestion";
  category:
    | "color"
    | "text"
    | "structure"
    | "navigation"
    | "multimedia"
    | "interaction"
    | "cognitive";
  description: string;
  detailedDescription: string;
  pageNumber: number;
  element?: string;
  location?: { x: number; y: number; width: number; height: number };
  suggestion: string;
  wcagLevel: "A" | "AA" | "AAA";
  wcagCriteria: string;
  autoFixAvailable: boolean;
  userImpact: "low" | "medium" | "high" | "critical";
}

export interface AccessibilityProfile {
  id: string;
  name: string;
  description: string;
  icon: string;
  settings: Partial<AccessibilitySettings>;
  isDefault?: boolean;
  category: "visual" | "motor" | "cognitive" | "hearing" | "custom";
}

interface PDFAccessibilityProps {
  pdfDocument?: unknown;
  numPages?: number;
  currentPage?: number;
  onSettingsChange?: (settings: AccessibilitySettings) => void;
  onIssueNavigate?: (
    pageNumber: number,
    location?: { x: number; y: number }
  ) => void;
  className?: string;
}

// Pre-configured accessibility profiles
const ACCESSIBILITY_PROFILES: AccessibilityProfile[] = [
  {
    id: "low-vision",
    name: "Low Vision",
    description: "Large text, high contrast, and magnification",
    icon: "eye",
    category: "visual",
    settings: {
      fontSize: 20,
      contrast: "high",
      magnification: 1.5,
      brightness: 1.2,
      focusIndicator: true,
    },
  },
  {
    id: "color-blind",
    name: "Color Blind",
    description: "Enhanced color differentiation",
    icon: "palette",
    category: "visual",
    settings: {
      colorBlindMode: "deuteranopia",
      linkHighlight: true,
      contrast: "higher",
    },
  },
  {
    id: "dyslexia",
    name: "Dyslexia",
    description: "Dyslexia-friendly fonts and spacing",
    icon: "type",
    category: "cognitive",
    settings: {
      dyslexiaFont: true,
      lineHeight: 1.8,
      letterSpacing: 2,
      wordSpacing: 4,
      readingGuide: true,
      comprehensionMode: true,
    },
  },
  {
    id: "motor-impairment",
    name: "Motor Impairment",
    description: "Enhanced navigation and interaction",
    icon: "mouse-pointer",
    category: "motor",
    settings: {
      singleKeyShortcuts: true,
      cursorSize: 2,
      clickHelper: true,
      pauseOnHover: true,
      skipToContent: true,
    },
  },
  {
    id: "adhd",
    name: "ADHD",
    description: "Reduced distractions and focus aids",
    icon: "focus",
    category: "cognitive",
    settings: {
      reducedMotion: true,
      readingGuide: true,
      textBackground: true,
      autoScrolling: false,
      comprehensionMode: true,
    },
  },
];

const DEFAULT_SETTINGS: AccessibilitySettings = {
  fontSize: 16,
  fontFamily: "Arial",
  lineHeight: 1.5,
  letterSpacing: 0,
  wordSpacing: 0,
  contrast: "normal",
  colorBlindMode: "none",
  brightness: 1,
  saturation: 1,
  dyslexiaFont: false,
  textBackground: false,
  textBackgroundColor: "#FFFF00",
  textColor: "#000000",
  linkHighlight: false,
  reducedMotion: false,
  autoScrolling: false,
  scrollSpeed: 1,
  pauseOnHover: false,
  textToSpeech: false,
  speechRate: 1,
  speechPitch: 1,
  speechVolume: 0.8,
  highlightWhileSpeaking: true,
  speechVoice: "",
  speechLanguage: "en-US",
  keyboardNavigation: true,
  focusIndicator: true,
  skipToContent: true,
  tocNavigation: true,
  singleKeyShortcuts: false,
  clickHelper: false,
  screenReader: true,
  ariaDescriptions: true,
  landmarkNavigation: true,
  readingOrder: true,
  readingGuide: false,
  readingGuideColor: "#FF0000",
  readingGuideThickness: 2,
  wordSpacingEnhancement: false,
  sentenceSpacing: false,
  comprehensionMode: false,
  magnification: 1,
  magnificationFollow: true,
  mouseTracking: false,
  cursorSize: 1,
  customCursor: false,
};

// Custom hook for accessibility analysis
const useAccessibilityAnalysis = () => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [issues, setIssues] = useState<AccessibilityIssue[]>([]);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  const analyzeAccessibility = useCallback(async () => {
    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      // Simulate comprehensive accessibility analysis
      const analysisSteps = [
        "Analyzing color contrast...",
        "Checking text structure...",
        "Validating navigation elements...",
        "Examining multimedia content...",
        "Testing keyboard accessibility...",
        "Evaluating cognitive load...",
      ];

      const foundIssues: AccessibilityIssue[] = [];

      for (let i = 0; i < analysisSteps.length; i++) {
        setAnalysisProgress(((i + 1) / analysisSteps.length) * 100);
        await new Promise((resolve) => setTimeout(resolve, 500));

        // Add mock issues for demonstration
        if (i === 0) {
          foundIssues.push({
            id: "contrast-1",
            type: "error",
            severity: "major",
            category: "color",
            description: "Insufficient color contrast",
            detailedDescription:
              "The contrast ratio between text and background is 2.1:1, which fails WCAG AA standards that require at least 4.5:1 for normal text.",
            pageNumber: 1,
            element: "Paragraph text",
            location: { x: 100, y: 200, width: 300, height: 20 },
            suggestion:
              "Increase contrast ratio to at least 4.5:1 for normal text or 3:1 for large text",
            wcagLevel: "AA",
            wcagCriteria: "1.4.3 Contrast (Minimum)",
            autoFixAvailable: true,
            userImpact: "high",
          });
        }

        if (i === 1) {
          foundIssues.push({
            id: "structure-1",
            type: "warning",
            severity: "minor",
            category: "structure",
            description: "Missing heading structure",
            detailedDescription:
              "The document jumps from H1 to H3 without an H2, breaking the logical heading hierarchy.",
            pageNumber: 2,
            element: "Heading structure",
            suggestion:
              "Add proper heading hierarchy (H1→H2→H3) to improve navigation for screen reader users",
            wcagLevel: "A",
            wcagCriteria: "1.3.1 Info and Relationships",
            autoFixAvailable: false,
            userImpact: "medium",
          });
        }

        if (i === 3) {
          foundIssues.push({
            id: "multimedia-1",
            type: "info",
            severity: "suggestion",
            category: "multimedia",
            description: "Images without alternative text",
            detailedDescription:
              "Several images in the document lack alternative text, making them inaccessible to screen reader users.",
            pageNumber: 3,
            element: "Image elements",
            suggestion: "Add descriptive alt text for all informative images",
            wcagLevel: "A",
            wcagCriteria: "1.1.1 Non-text Content",
            autoFixAvailable: false,
            userImpact: "medium",
          });
        }
      }

      setIssues(foundIssues);
    } catch (error) {
      console.error("Accessibility analysis failed:", error);
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  }, []);

  return {
    isAnalyzing,
    analysisProgress,
    issues,
    analyzeAccessibility,
  };
};

// Custom hook for text-to-speech
const useTextToSpeech = (settings: AccessibilitySettings) => {
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [currentUtterance, setCurrentUtterance] = useState<SpeechSynthesisUtterance | null>(null);

  const [availableVoices, setAvailableVoices] = useState<
    SpeechSynthesisVoice[]
  >([]);

  useEffect(() => {
    if ("speechSynthesis" in window) {
      const updateVoices = () => {
        setAvailableVoices(window.speechSynthesis.getVoices());
      };

      updateVoices();
      window.speechSynthesis.onvoiceschanged = updateVoices;
    }
  }, []);

  const speak = useCallback(
    (text: string) => {
      if (!("speechSynthesis" in window)) return;

      // Stop current speech
      window.speechSynthesis.cancel();

      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = settings.speechRate;
      utterance.pitch = settings.speechPitch;
      utterance.volume = settings.speechVolume;
      utterance.lang = settings.speechLanguage;

      // Find and set the selected voice
      const selectedVoice = availableVoices.find(
        (voice) => voice.name === settings.speechVoice
      );
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }

      utterance.onstart = () => {
        setIsSpeaking(true);
        setIsPaused(false);
      };

      utterance.onend = () => {
        setIsSpeaking(false);
        setIsPaused(false);
        setCurrentUtterance(null);
      };

      utterance.onerror = () => {
        setIsSpeaking(false);
        setIsPaused(false);
        setCurrentUtterance(null);
      };

      setCurrentUtterance(utterance);
      window.speechSynthesis.speak(utterance);
    },
    [settings, availableVoices]
  );

  const pause = useCallback(() => {
    if ("speechSynthesis" in window && isSpeaking) {
      window.speechSynthesis.pause();
      setIsPaused(true);
    }
  }, [isSpeaking]);

  const resume = useCallback(() => {
    if ("speechSynthesis" in window && isPaused) {
      window.speechSynthesis.resume();
      setIsPaused(false);
    }
  }, [isPaused]);

  const stop = useCallback(() => {
    if ("speechSynthesis" in window) {
      window.speechSynthesis.cancel();
      setIsSpeaking(false);
      setIsPaused(false);
      setCurrentUtterance(null);
    }
  }, []);

  return {
    isSpeaking,
    isPaused,
    currentUtterance,
    availableVoices,
    speak,
    pause,
    resume,
    stop,
  };
};

// Accessibility Profile Selector
const AccessibilityProfileSelector: React.FC<{
  onProfileSelect: (profile: AccessibilityProfile) => void;
  className?: string;
}> = ({ onProfileSelect, className }) => {
  return (
    <Card className={cn("p-4", className)}>
      <h3 className="font-medium mb-3">Quick Setup Profiles</h3>
      <div className="grid grid-cols-1 gap-2">
        {ACCESSIBILITY_PROFILES.map((profile) => (
          <Button
            key={profile.id}
            variant="outline"
            onClick={() => onProfileSelect(profile)}
            className="flex items-start justify-start h-auto p-3 text-left"
          >
            <div className="flex-1">
              <div className="font-medium">{profile.name}</div>
              <div className="text-xs text-muted-foreground mt-1">
                {profile.description}
              </div>
            </div>
          </Button>
        ))}
      </div>
    </Card>
  );
};

// Issues panel component
const AccessibilityIssuesPanel: React.FC<{
  issues: AccessibilityIssue[];
  onIssueSelect: (issue: AccessibilityIssue) => void;
  onAutoFix: (issueId: string) => void;
  className?: string;
}> = ({ issues, onIssueSelect, onAutoFix, className }) => {
  const [filter, setFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");

  const filteredIssues = useMemo(() => {
    return issues.filter((issue) => {
      const matchesFilter = filter === "all" || issue.type === filter;
      const matchesSearch =
        !searchQuery ||
        issue.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        issue.category.toLowerCase().includes(searchQuery.toLowerCase());

      return matchesFilter && matchesSearch;
    });
  }, [issues, filter, searchQuery]);

  const getIssueIcon = (type: AccessibilityIssue["type"]) => {
    switch (type) {
      case "error":
        return <AlertTriangle className="size-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="size-4 text-orange-500" />;
      case "info":
        return <Info className="size-4 text-blue-500" />;
      case "enhancement":
        return <Check className="size-4 text-green-500" />;
    }
  };

  const getSeverityColor = (severity: AccessibilityIssue["severity"]) => {
    switch (severity) {
      case "critical":
        return "bg-red-50 border-red-200 text-red-700";
      case "major":
        return "bg-orange-50 border-orange-200 text-orange-700";
      case "minor":
        return "bg-yellow-50 border-yellow-200 text-yellow-700";
      case "suggestion":
        return "bg-blue-50 border-blue-200 text-blue-700";
    }
  };

  const issueStats = useMemo(
    () => ({
      total: issues.length,
      errors: issues.filter((i) => i.type === "error").length,
      warnings: issues.filter((i) => i.type === "warning").length,
      info: issues.filter((i) => i.type === "info").length,
      fixable: issues.filter((i) => i.autoFixAvailable).length,
    }),
    [issues]
  );

  return (
    <Card className={cn("p-4", className)}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-medium">Accessibility Issues</h3>
        <Badge variant="secondary">{filteredIssues.length}</Badge>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-4 gap-2 mb-4 text-center">
        <div>
          <div className="text-lg font-bold text-red-600">
            {issueStats.errors}
          </div>
          <div className="text-xs text-muted-foreground">Errors</div>
        </div>
        <div>
          <div className="text-lg font-bold text-orange-600">
            {issueStats.warnings}
          </div>
          <div className="text-xs text-muted-foreground">Warnings</div>
        </div>
        <div>
          <div className="text-lg font-bold text-blue-600">
            {issueStats.info}
          </div>
          <div className="text-xs text-muted-foreground">Info</div>
        </div>
        <div>
          <div className="text-lg font-bold text-green-600">
            {issueStats.fixable}
          </div>
          <div className="text-xs text-muted-foreground">Fixable</div>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="space-y-2 mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
          <Input
            placeholder="Search issues..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        <div className="flex gap-1 overflow-x-auto">
          {["all", "error", "warning", "info", "enhancement"].map((type) => (
            <Button
              key={type}
              variant={filter === type ? "default" : "ghost"}
              size="sm"
              onClick={() => setFilter(type)}
              className="text-xs capitalize whitespace-nowrap"
            >
              {type}
            </Button>
          ))}
        </div>
      </div>

      {/* Issues List */}
      <ScrollArea className="max-h-80">
        <div className="space-y-3">
          {filteredIssues.map((issue) => (
            <Card
              key={issue.id}
              className={cn(
                "p-3 cursor-pointer hover:bg-accent transition-colors",
                getSeverityColor(issue.severity)
              )}
              onClick={() => onIssueSelect(issue)}
            >
              <div className="flex items-start gap-3">
                {getIssueIcon(issue.type)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium text-sm">
                      {issue.description}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      Page {issue.pageNumber}
                    </Badge>
                  </div>

                  <div className="text-xs text-muted-foreground mb-2">
                    {issue.wcagCriteria} • {issue.severity} • {issue.userImpact}{" "}
                    impact
                  </div>

                  <div className="text-xs">{issue.suggestion}</div>

                  {issue.autoFixAvailable && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={(e) => {
                        e.stopPropagation();
                        onAutoFix(issue.id);
                      }}
                      className="mt-2"
                    >
                      <Zap className="size-3 mr-1" />
                      Auto Fix
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      </ScrollArea>

      {filteredIssues.length === 0 && (
        <div className="text-center text-muted-foreground py-8">
          <Check className="size-8 mx-auto mb-2 opacity-50" />
          <p>No accessibility issues found</p>
        </div>
      )}
    </Card>
  );
};

export default function PDFAccessibility({
  currentPage = 1,
  onSettingsChange,
  onIssueNavigate,
  className,
}: PDFAccessibilityProps) {
  const [settings, setSettings] =
    useState<AccessibilitySettings>(DEFAULT_SETTINGS);
  const [activeTab, setActiveTab] = useState<
    "profiles" | "visual" | "audio" | "navigation" | "cognitive" | "issues"
  >("profiles");

  const { isAnalyzing, analysisProgress, issues, analyzeAccessibility } =
    useAccessibilityAnalysis();

  const { isSpeaking, isPaused, speak, pause, resume, stop } =
    useTextToSpeech(settings);

  const updateSetting = useCallback(
    <K extends keyof AccessibilitySettings>(
      key: K,
      value: AccessibilitySettings[K]
    ) => {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      onSettingsChange?.(newSettings);
    },
    [settings, onSettingsChange]
  );

  const applyProfile = useCallback(
    (profile: AccessibilityProfile) => {
      const newSettings = { ...settings, ...profile.settings };
      setSettings(newSettings);
      onSettingsChange?.(newSettings);
      toast.success(`Applied ${profile.name} profile`);
    },
    [settings, onSettingsChange]
  );

  const handleIssueSelect = useCallback(
    (issue: AccessibilityIssue) => {
      if (onIssueNavigate && issue.location) {
        onIssueNavigate(issue.pageNumber, {
          x: issue.location.x,
          y: issue.location.y,
        });
      }
    },
    [onIssueNavigate]
  );

  const handleAutoFix = useCallback((issueId: string) => {
    // Implement auto-fix logic here
    console.log("Auto-fixing issue:", issueId);
    toast.success("Issue auto-fixed successfully");
  }, []);

  const readCurrentPage = useCallback(() => {
    // Mock text extraction for current page
    const mockPageText = `This is the content of page ${currentPage}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.`;
    speak(mockPageText);
  }, [currentPage, speak]);

  const resetSettings = useCallback(() => {
    setSettings(DEFAULT_SETTINGS);
    onSettingsChange?.(DEFAULT_SETTINGS);
    toast.success("Settings reset to defaults");
  }, [onSettingsChange]);

  return (
    <div className={cn("space-y-4", className)}>
      {/* Header */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Accessibility className="size-5" />
            <h3 className="font-semibold">Enhanced Accessibility</h3>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={analyzeAccessibility}
              disabled={isAnalyzing}
            >
              <Zap className="size-4 mr-1" />
              {isAnalyzing
                ? `Analyzing... ${Math.round(analysisProgress)}%`
                : "Analyze"}
            </Button>

            <Button variant="ghost" size="sm" onClick={resetSettings}>
              <RotateCcw className="size-4 mr-1" />
              Reset
            </Button>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-2">
          <Button
            size="sm"
            onClick={isSpeaking ? (isPaused ? resume : pause) : readCurrentPage}
            variant={isSpeaking ? "destructive" : "outline"}
          >
            {isSpeaking ? (
              isPaused ? (
                <>
                  <Play className="size-4 mr-1" />
                  Resume
                </>
              ) : (
                <>
                  <Pause className="size-4 mr-1" />
                  Pause
                </>
              )
            ) : (
              <>
                <Volume2 className="size-4 mr-1" />
                Read Page
              </>
            )}
          </Button>

          {isSpeaking && (
            <Button size="sm" variant="ghost" onClick={stop}>
              <Square className="size-4 mr-1" />
              Stop
            </Button>
          )}
        </div>
      </Card>

      {/* Tab Navigation */}
      <div className="flex gap-1 overflow-x-auto">
        {[
          { id: "profiles", label: "Profiles", icon: BookOpen },
          { id: "visual", label: "Visual", icon: Eye },
          { id: "audio", label: "Audio", icon: Volume2 },
          { id: "navigation", label: "Navigation", icon: Navigation },
          { id: "cognitive", label: "Cognitive", icon: Focus },
          { id: "issues", label: "Issues", icon: AlertTriangle },
        ].map((tab) => (
          <Button
            key={tab.id}
            variant={activeTab === tab.id ? "default" : "ghost"}
            size="sm"
            onClick={() => setActiveTab(tab.id as "profiles" | "visual" | "audio" | "navigation" | "cognitive" | "issues")}
            className="flex items-center gap-1"
          >
            <tab.icon className="size-4" />
            {tab.label}
            {tab.id === "issues" && issues.length > 0 && (
              <Badge variant="destructive" className="ml-1 h-4 w-4 p-0 text-xs">
                {issues.length}
              </Badge>
            )}
          </Button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === "profiles" && (
        <AccessibilityProfileSelector onProfileSelect={applyProfile} />
      )}

      {activeTab === "visual" && (
        <Card className="p-4 space-y-4">
          <h4 className="font-medium">Visual Settings</h4>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">
                Font Size: {settings.fontSize}px
              </label>
              <Slider
                value={[settings.fontSize]}
                onValueChange={([value]) => updateSetting("fontSize", value)}
                min={12}
                max={32}
                step={1}
                className="mt-2"
              />
            </div>

            <div>
              <label className="text-sm font-medium">
                Line Height: {settings.lineHeight}
              </label>
              <Slider
                value={[settings.lineHeight]}
                onValueChange={([value]) => updateSetting("lineHeight", value)}
                min={1}
                max={3}
                step={0.1}
                className="mt-2"
              />
            </div>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.dyslexiaFont}
                onCheckedChange={(checked) =>
                  updateSetting("dyslexiaFont", checked as boolean)
                }
              />
              <label className="text-sm">Use dyslexia-friendly font</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.textBackground}
                onCheckedChange={(checked) =>
                  updateSetting("textBackground", checked as boolean)
                }
              />
              <label className="text-sm">Highlight text background</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.linkHighlight}
                onCheckedChange={(checked) =>
                  updateSetting("linkHighlight", checked as boolean)
                }
              />
              <label className="text-sm">Enhanced link highlighting</label>
            </div>
          </div>
        </Card>
      )}

      {activeTab === "audio" && (
        <Card className="p-4 space-y-4">
          <h4 className="font-medium">Audio Settings</h4>

          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.textToSpeech}
                onCheckedChange={(checked) =>
                  updateSetting("textToSpeech", checked as boolean)
                }
              />
              <label className="text-sm">Enable text-to-speech</label>
            </div>

            {settings.textToSpeech && (
              <>
                <div>
                  <label className="text-sm font-medium">
                    Speech Rate: {settings.speechRate}x
                  </label>
                  <Slider
                    value={[settings.speechRate]}
                    onValueChange={([value]) =>
                      updateSetting("speechRate", value)
                    }
                    min={0.5}
                    max={3}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">
                    Volume: {Math.round(settings.speechVolume * 100)}%
                  </label>
                  <Slider
                    value={[settings.speechVolume]}
                    onValueChange={([value]) =>
                      updateSetting("speechVolume", value)
                    }
                    min={0}
                    max={1}
                    step={0.1}
                    className="mt-2"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    checked={settings.highlightWhileSpeaking}
                    onCheckedChange={(checked) =>
                      updateSetting(
                        "highlightWhileSpeaking",
                        checked as boolean
                      )
                    }
                  />
                  <label className="text-sm">
                    Highlight text while speaking
                  </label>
                </div>
              </>
            )}
          </div>
        </Card>
      )}

      {activeTab === "navigation" && (
        <Card className="p-4 space-y-4">
          <h4 className="font-medium">Navigation & Interaction</h4>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.keyboardNavigation}
                onCheckedChange={(checked) =>
                  updateSetting("keyboardNavigation", checked as boolean)
                }
              />
              <label className="text-sm">Enhanced keyboard navigation</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.focusIndicator}
                onCheckedChange={(checked) =>
                  updateSetting("focusIndicator", checked as boolean)
                }
              />
              <label className="text-sm">Visible focus indicators</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.singleKeyShortcuts}
                onCheckedChange={(checked) =>
                  updateSetting("singleKeyShortcuts", checked as boolean)
                }
              />
              <label className="text-sm">Single-key shortcuts</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.skipToContent}
                onCheckedChange={(checked) =>
                  updateSetting("skipToContent", checked as boolean)
                }
              />
              <label className="text-sm">Skip to content links</label>
            </div>
          </div>
        </Card>
      )}

      {activeTab === "cognitive" && (
        <Card className="p-4 space-y-4">
          <h4 className="font-medium">Cognitive Assistance</h4>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.readingGuide}
                onCheckedChange={(checked) =>
                  updateSetting("readingGuide", checked as boolean)
                }
              />
              <label className="text-sm">Reading guide/ruler</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.comprehensionMode}
                onCheckedChange={(checked) =>
                  updateSetting("comprehensionMode", checked as boolean)
                }
              />
              <label className="text-sm">Comprehension mode</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.reducedMotion}
                onCheckedChange={(checked) =>
                  updateSetting("reducedMotion", checked as boolean)
                }
              />
              <label className="text-sm">Reduce motion and animations</label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                checked={settings.pauseOnHover}
                onCheckedChange={(checked) =>
                  updateSetting("pauseOnHover", checked as boolean)
                }
              />
              <label className="text-sm">Pause animations on hover</label>
            </div>
          </div>
        </Card>
      )}

      {activeTab === "issues" && (
        <AccessibilityIssuesPanel
          issues={issues}
          onIssueSelect={handleIssueSelect}
          onAutoFix={handleAutoFix}
        />
      )}
    </div>
  );
}
