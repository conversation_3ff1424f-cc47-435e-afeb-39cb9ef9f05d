"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Accessibility,
  List,
  Heading,
  FileText,
  Link,
  Image,
  Table,
  MapPin,
  ChevronRight,
  ChevronDown,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  EnhancedScreenReaderSupport, 
  type ScreenReaderConfig,
  type ContentStructure,
  type NavigationContext,
} from '@/lib/accessibility/enhanced-screen-reader';

interface EnhancedScreenReaderPanelProps {
  screenReader: EnhancedScreenReaderSupport;
  currentPage: number;
  totalPages: number;
  onNavigateToPage?: (page: number) => void;
  className?: string;
}

export default function EnhancedScreenReaderPanel({
  screenReader,
  currentPage,
  totalPages,
  onNavigateToPage,
  className,
}: EnhancedScreenReaderPanelProps) {
  const [config, setConfig] = useState<ScreenReaderConfig>({
    enableLiveRegions: true,
    enableLandmarks: true,
    enableStructuralNavigation: true,
    enableContentDescription: true,
    enableProgressAnnouncements: true,
    verbosityLevel: 'standard',
    announcementDelay: 100,
    enableSpatialNavigation: true,
    enableTableNavigation: true,
    enableFormNavigation: true,
  });

  const [contentStructure, setContentStructure] = useState<ContentStructure[]>([]);
  const [navigationContext, setNavigationContext] = useState<NavigationContext | null>(null);

  const [currentStructure, setCurrentStructure] = useState<ContentStructure | null>(null);
  const [expandedStructures, setExpandedStructures] = useState<Set<string>>(new Set());

  // Update data when page changes
  useEffect(() => {
    updateContentStructure();
  }, [currentPage, updateContentStructure]);

  // Listen for screen reader events
  useEffect(() => {
    const handleStructureExtracted = (data: { pageNumber: number }) => {
      if (data.pageNumber === currentPage) {
        updateContentStructure();
      }
    };

    const handleStructureFocused = (data: { structure: ContentStructure }) => {
      setCurrentStructure(data.structure);
      setNavigationContext(data.context);
    };

    screenReader.addEventListener('structure-extracted', handleStructureExtracted);
    screenReader.addEventListener('structure-focused', handleStructureFocused);

    return () => {
      screenReader.removeEventListener('structure-extracted', handleStructureExtracted);
      screenReader.removeEventListener('structure-focused', handleStructureFocused);
    };
  }, [screenReader, currentPage, updateContentStructure]);

  const updateContentStructure = useCallback(() => {
    const structures = screenReader.getContentStructure(currentPage);
    setContentStructure(structures);
    setNavigationContext(screenReader.getNavigationContext());
  }, [screenReader, currentPage]);

  const handleConfigChange = useCallback((key: keyof ScreenReaderConfig, value: ScreenReaderConfig[keyof ScreenReaderConfig]) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    // In a real implementation, this would update the screen reader config
  }, []);

  const handleStructureClick = useCallback((structure: ContentStructure) => {
    setCurrentStructure(structure);
    if (structure.pageNumber !== currentPage) {
      onNavigateToPage?.(structure.pageNumber);
    }
    // Focus the structure in the screen reader
    screenReader.announce(`Navigated to ${structure.type}: ${structure.text}`, 'navigation');
  }, [screenReader, currentPage, onNavigateToPage]);

  const toggleStructureExpansion = useCallback((structureId: string) => {
    setExpandedStructures(prev => {
      const newSet = new Set(prev);
      if (newSet.has(structureId)) {
        newSet.delete(structureId);
      } else {
        newSet.add(structureId);
      }
      return newSet;
    });
  }, []);

  const getStructureIcon = (type: ContentStructure['type']) => {
    switch (type) {
      case 'heading': return <Heading className="h-4 w-4" />;
      case 'paragraph': return <FileText className="h-4 w-4" />;
      case 'list': return <List className="h-4 w-4" />;
      case 'table': return <Table className="h-4 w-4" />;
      case 'image': return <Image className="h-4 w-4" />;
      case 'link': return <Link className="h-4 w-4" />;
      case 'landmark': return <MapPin className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getStructureColor = (type: ContentStructure['type']) => {
    switch (type) {
      case 'heading': return 'text-blue-600';
      case 'paragraph': return 'text-gray-600';
      case 'list': return 'text-green-600';
      case 'table': return 'text-purple-600';
      case 'image': return 'text-orange-600';
      case 'link': return 'text-indigo-600';
      case 'landmark': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const renderStructureTree = (structures: ContentStructure[], level = 0) => {
    return structures.map((structure) => {
      const structureId = `${structure.pageNumber}-${structure.position.x}-${structure.position.y}`;
      const isExpanded = expandedStructures.has(structureId);
      const hasChildren = structure.children && structure.children.length > 0;
      const isCurrent = currentStructure === structure;

      return (
        <div key={structureId} className={cn("space-y-1", level > 0 && "ml-4")}>
          <div
            className={cn(
              "flex items-center gap-2 p-2 rounded cursor-pointer hover:bg-muted/50",
              isCurrent && "bg-primary/10 border border-primary/20"
            )}
            onClick={() => handleStructureClick(structure)}
          >
            {hasChildren && (
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleStructureExpansion(structureId);
                }}
                className="p-1"
              >
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </button>
            )}
            
            {!hasChildren && <div className="w-5" />}
            
            <div className={cn("p-1", getStructureColor(structure.type))}>
              {getStructureIcon(structure.type)}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="text-sm font-medium truncate">
                {structure.type === 'heading' && structure.level && (
                  <span className="text-xs text-muted-foreground mr-2">
                    H{structure.level}
                  </span>
                )}
                {structure.text.length > 50 
                  ? structure.text.substring(0, 50) + '...'
                  : structure.text
                }
              </div>
              <div className="text-xs text-muted-foreground">
                Page {structure.pageNumber} • {structure.type}
              </div>
            </div>
            
            {Object.keys(structure.attributes).length > 0 && (
              <Badge variant="outline" className="text-xs">
                ARIA
              </Badge>
            )}
          </div>
          
          {hasChildren && isExpanded && (
            <div className="ml-2">
              {renderStructureTree(structure.children!, level + 1)}
            </div>
          )}
        </div>
      );
    });
  };

  const structureStats = {
    headings: contentStructure.filter(s => s.type === 'heading').length,
    paragraphs: contentStructure.filter(s => s.type === 'paragraph').length,
    lists: contentStructure.filter(s => s.type === 'list').length,
    tables: contentStructure.filter(s => s.type === 'table').length,
    images: contentStructure.filter(s => s.type === 'image').length,
    links: contentStructure.filter(s => s.type === 'link').length,
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Accessibility className="h-5 w-5" />
            Enhanced Screen Reader
          </CardTitle>
          <CardDescription>
            Advanced accessibility features and structural navigation
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="navigation" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="navigation">Navigation</TabsTrigger>
          <TabsTrigger value="structure">Structure</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
          <TabsTrigger value="help">Help</TabsTrigger>
        </TabsList>

        <TabsContent value="navigation" className="space-y-4">
          {/* Quick Navigation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Navigation</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextHeading()}
                  className="justify-start"
                >
                  <Heading className="h-4 w-4 mr-2" />
                  Next Heading
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextParagraph()}
                  className="justify-start"
                >
                  <FileText className="h-4 w-4 mr-2" />
                  Next Paragraph
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextList()}
                  className="justify-start"
                >
                  <List className="h-4 w-4 mr-2" />
                  Next List
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextTable()}
                  className="justify-start"
                >
                  <Table className="h-4 w-4 mr-2" />
                  Next Table
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextLink()}
                  className="justify-start"
                >
                  <Link className="h-4 w-4 mr-2" />
                  Next Link
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => screenReader.navigateToNextGraphic()}
                  className="justify-start"
                >
                  <Image className="h-4 w-4 mr-2" />
                  Next Image
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Current Context */}
          {navigationContext && navigationContext.currentElement && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Current Context</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className={getStructureColor(navigationContext.currentElement.type)}>
                    {getStructureIcon(navigationContext.currentElement.type)}
                  </div>
                  <div>
                    <div className="font-medium">
                      {navigationContext.currentElement.type}
                      {navigationContext.currentElement.level && ` (Level ${navigationContext.currentElement.level})`}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {navigationContext.currentElement.text.substring(0, 100)}
                      {navigationContext.currentElement.text.length > 100 && '...'}
                    </div>
                  </div>
                </div>

                {navigationContext.breadcrumb.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Breadcrumb:</Label>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground mt-1">
                      {navigationContext.breadcrumb.map((item, index) => (
                        <React.Fragment key={index}>
                          {index > 0 && <ChevronRight className="h-3 w-3" />}
                          <span>{item.text.substring(0, 20)}</span>
                        </React.Fragment>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Page Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Page Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{structureStats.headings}</div>
                  <div className="text-muted-foreground">Headings</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{structureStats.paragraphs}</div>
                  <div className="text-muted-foreground">Paragraphs</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{structureStats.lists}</div>
                  <div className="text-muted-foreground">Lists</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">{structureStats.tables}</div>
                  <div className="text-muted-foreground">Tables</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">{structureStats.images}</div>
                  <div className="text-muted-foreground">Images</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-indigo-600">{structureStats.links}</div>
                  <div className="text-muted-foreground">Links</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="structure" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Structure</CardTitle>
              <CardDescription>
                Hierarchical view of page content with ARIA information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96">
                {contentStructure.length > 0 ? (
                  <div className="space-y-1">
                    {renderStructureTree(contentStructure.filter(s => !s.parent))}
                  </div>
                ) : (
                  <div className="text-center text-muted-foreground py-8">
                    No content structure detected on this page
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Screen Reader Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="live-regions">Live Regions</Label>
                  <Switch
                    id="live-regions"
                    checked={config.enableLiveRegions}
                    onCheckedChange={(checked) => handleConfigChange('enableLiveRegions', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="landmarks">Landmarks</Label>
                  <Switch
                    id="landmarks"
                    checked={config.enableLandmarks}
                    onCheckedChange={(checked) => handleConfigChange('enableLandmarks', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="structural-nav">Structural Navigation</Label>
                  <Switch
                    id="structural-nav"
                    checked={config.enableStructuralNavigation}
                    onCheckedChange={(checked) => handleConfigChange('enableStructuralNavigation', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="content-desc">Content Description</Label>
                  <Switch
                    id="content-desc"
                    checked={config.enableContentDescription}
                    onCheckedChange={(checked) => handleConfigChange('enableContentDescription', checked)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="verbosity">Verbosity Level</Label>
                  <Select
                    value={config.verbosityLevel}
                    onValueChange={(value: string) => handleConfigChange('verbosityLevel', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="minimal">Minimal</SelectItem>
                      <SelectItem value="standard">Standard</SelectItem>
                      <SelectItem value="verbose">Verbose</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="help" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Keyboard Shortcuts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="font-medium">Navigation</div>
                    <div className="space-y-1 text-muted-foreground">
                      <div>Alt + H: Next heading</div>
                      <div>Alt + P: Next paragraph</div>
                      <div>Alt + L: Next list</div>
                      <div>Alt + T: Next table</div>
                      <div>Alt + G: Next graphic</div>
                      <div>Alt + K: Next link</div>
                    </div>
                  </div>
                  
                  <div>
                    <div className="font-medium">Table Navigation</div>
                    <div className="space-y-1 text-muted-foreground">
                      <div>Ctrl + Alt + ↑: Cell above</div>
                      <div>Ctrl + Alt + ↓: Cell below</div>
                      <div>Ctrl + Alt + ←: Cell left</div>
                      <div>Ctrl + Alt + →: Cell right</div>
                      <div>Ctrl + Alt + Home: First cell</div>
                      <div>Ctrl + Alt + End: Last cell</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">ARIA Features</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Live Regions</Badge>
                  <span>Real-time announcements for dynamic content</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Landmarks</Badge>
                  <span>Semantic page regions for quick navigation</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Headings</Badge>
                  <span>Hierarchical document structure</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Tables</Badge>
                  <span>Row and column headers for data tables</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">Forms</Badge>
                  <span>Labels and descriptions for form controls</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
