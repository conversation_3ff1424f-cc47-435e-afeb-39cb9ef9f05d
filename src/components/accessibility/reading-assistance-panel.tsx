"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Eye,
  Volume2,
  Focus,
  Timer,
  Play,
  Square,
  Zap,
  Target,
  TrendingUp,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  ReadingAssistanceManager, 
  type ReadingAssistanceConfig,
  type ReadingSession,
} from '@/lib/accessibility/reading-assistance';

interface ReadingAssistancePanelProps {
  assistanceManager: ReadingAssistanceManager;
  className?: string;
}

export default function ReadingAssistancePanel({
  assistanceManager,
  className,
}: ReadingAssistancePanelProps) {
  const [config, setConfig] = useState<ReadingAssistanceConfig>(assistanceManager.getConfig());
  const [currentSession, setCurrentSession] = useState<ReadingSession | null>(null);
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [readingSpeed, setReadingSpeed] = useState(0);
  const [wordCount, setWordCount] = useState(0);
  const [availableVoices, setAvailableVoices] = useState<SpeechSynthesisVoice[]>([]);

  // Load data on mount
  useEffect(() => {
    updateData();
    loadVoices();
  }, [updateData, loadVoices]);

  // Listen for speech synthesis voices loaded
  useEffect(() => {
    if ('speechSynthesis' in window) {
      window.speechSynthesis.onvoiceschanged = loadVoices;
    }
  }, [loadVoices]);

  // Listen for assistance manager events
  useEffect(() => {
    const handleConfigUpdated = () => updateData();
    const handleSessionStarted = (data: { session: ReadingSession }) => {
      setCurrentSession(data.session);
      setIsSessionActive(true);
    };
    const handleSessionEnded = (data: { session: ReadingSession }) => {
      setCurrentSession(data.session);
      setIsSessionActive(false);
    };
    const handleReadingSpeedUpdate = (data: { currentSpeed: number; wordCount: number }) => {
      setReadingSpeed(data.currentSpeed);
      setWordCount(data.wordCount);
    };

    assistanceManager.addEventListener('config-updated', handleConfigUpdated);
    assistanceManager.addEventListener('session-started', handleSessionStarted);
    assistanceManager.addEventListener('session-ended', handleSessionEnded);
    assistanceManager.addEventListener('reading-speed-update', handleReadingSpeedUpdate);

    return () => {
      assistanceManager.removeEventListener('config-updated', handleConfigUpdated);
      assistanceManager.removeEventListener('session-started', handleSessionStarted);
      assistanceManager.removeEventListener('session-ended', handleSessionEnded);
      assistanceManager.removeEventListener('reading-speed-update', handleReadingSpeedUpdate);
    };
  }, [assistanceManager, updateData]);

  const updateData = useCallback(() => {
    setConfig(assistanceManager.getConfig());
  }, [assistanceManager]);

  const loadVoices = useCallback(() => {
    setAvailableVoices(assistanceManager.getAvailableVoices());
  }, [assistanceManager]);

  const handleConfigChange = useCallback((updates: Partial<ReadingAssistanceConfig>) => {
    assistanceManager.updateConfig(updates);
  }, [assistanceManager]);

  const handleStartSession = useCallback(() => {
    assistanceManager.startReadingSession();
  }, [assistanceManager]);

  const handleEndSession = useCallback(() => {
    assistanceManager.endReadingSession();
  }, [assistanceManager]);

  const ColorPicker = ({ label, value, onChange }: { label: string; value: string; onChange: (value: string) => void }) => (
    <div className="flex items-center gap-2">
      <Label className="text-sm">{label}</Label>
      <div className="flex items-center gap-2">
        <div
          className="w-6 h-6 rounded border cursor-pointer"
          style={{ backgroundColor: value }}
          onClick={() => {
            const input = document.createElement('input');
            input.type = 'color';
            input.value = value;
            input.onchange = (e) => onChange((e.target as HTMLInputElement).value);
            input.click();
          }}
        />
      </div>
    </div>
  );

  const formatTime = (milliseconds: number): string => {
    const minutes = Math.floor(milliseconds / (1000 * 60));
    const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={cn("space-y-4", className)}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Reading Assistance Tools
          </CardTitle>
          <CardDescription>
            Advanced reading guides, focus indicators, and cognitive assistance features
          </CardDescription>
        </CardHeader>
      </Card>

      <Tabs defaultValue="guides" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="guides">Guides</TabsTrigger>
          <TabsTrigger value="focus">Focus</TabsTrigger>
          <TabsTrigger value="speech">Speech</TabsTrigger>
          <TabsTrigger value="session">Session</TabsTrigger>
        </TabsList>

        <TabsContent value="guides" className="space-y-4">
          {/* Reading Guide Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Eye className="h-5 w-5" />
                Reading Guides
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="reading-guide">Enable Reading Guide</Label>
                <Switch
                  id="reading-guide"
                  checked={config.enableReadingGuide}
                  onCheckedChange={(checked) => handleConfigChange({ enableReadingGuide: checked })}
                />
              </div>

              {config.enableReadingGuide && (
                <div className="space-y-4 ml-4">
                  <div className="space-y-2">
                    <Label>Guide Type</Label>
                    <Select
                      value={config.readingGuideType}
                      onValueChange={(value: string) => handleConfigChange({ readingGuideType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="line">Line</SelectItem>
                        <SelectItem value="ruler">Ruler</SelectItem>
                        <SelectItem value="window">Window</SelectItem>
                        <SelectItem value="spotlight">Spotlight</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <ColorPicker
                    label="Guide Color"
                    value={config.readingGuideColor}
                    onChange={(value) => handleConfigChange({ readingGuideColor: value })}
                  />

                  <div className="space-y-2">
                    <Label>Opacity: {Math.round(config.readingGuideOpacity * 100)}%</Label>
                    <Slider
                      value={[config.readingGuideOpacity]}
                      onValueChange={([value]) => handleConfigChange({ readingGuideOpacity: value })}
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Width: {config.readingGuideWidth}px</Label>
                    <Slider
                      value={[config.readingGuideWidth]}
                      onValueChange={([value]) => handleConfigChange({ readingGuideWidth: value })}
                      min={1}
                      max={10}
                      step={1}
                      className="w-full"
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="reading-mask">Enable Reading Mask</Label>
                <Switch
                  id="reading-mask"
                  checked={config.enableReadingMask}
                  onCheckedChange={(checked) => handleConfigChange({ enableReadingMask: checked })}
                />
              </div>

              {config.enableReadingMask && (
                <div className="space-y-4 ml-4">
                  <ColorPicker
                    label="Mask Color"
                    value={config.maskColor}
                    onChange={(value) => handleConfigChange({ maskColor: value })}
                  />

                  <div className="space-y-2">
                    <Label>Mask Opacity: {Math.round(config.maskOpacity * 100)}%</Label>
                    <Slider
                      value={[config.maskOpacity]}
                      onValueChange={([value]) => handleConfigChange({ maskOpacity: value })}
                      min={0.1}
                      max={1.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Text Enhancement Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Zap className="h-5 w-5" />
                Text Enhancements
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="line-spacing">Enhanced Line Spacing</Label>
                <Switch
                  id="line-spacing"
                  checked={config.enableLineSpacing}
                  onCheckedChange={(checked) => handleConfigChange({ enableLineSpacing: checked })}
                />
              </div>

              {config.enableLineSpacing && (
                <div className="space-y-2 ml-4">
                  <Label>Line Height: {config.lineSpacingMultiplier.toFixed(1)}</Label>
                  <Slider
                    value={[config.lineSpacingMultiplier]}
                    onValueChange={([value]) => handleConfigChange({ lineSpacingMultiplier: value })}
                    min={1.0}
                    max={3.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="word-highlight">Word Highlighting</Label>
                <Switch
                  id="word-highlight"
                  checked={config.enableWordHighlight}
                  onCheckedChange={(checked) => handleConfigChange({ enableWordHighlight: checked })}
                />
              </div>

              {config.enableWordHighlight && (
                <div className="ml-4">
                  <ColorPicker
                    label="Highlight Color"
                    value={config.wordHighlightColor}
                    onChange={(value) => handleConfigChange({ wordHighlightColor: value })}
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="bionic-reading">Bionic Reading</Label>
                <Switch
                  id="bionic-reading"
                  checked={config.enableBionic}
                  onCheckedChange={(checked) => handleConfigChange({ enableBionic: checked })}
                />
              </div>

              {config.enableBionic && (
                <div className="space-y-2 ml-4">
                  <Label>Intensity: {Math.round(config.bionicIntensity * 100)}%</Label>
                  <Slider
                    value={[config.bionicIntensity]}
                    onValueChange={([value]) => handleConfigChange({ bionicIntensity: value })}
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="syllable-breaks">Syllable Breaks</Label>
                <Switch
                  id="syllable-breaks"
                  checked={config.enableSyllableBreaks}
                  onCheckedChange={(checked) => handleConfigChange({ enableSyllableBreaks: checked })}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="focus" className="space-y-4">
          {/* Focus Indicator Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Focus className="h-5 w-5" />
                Focus Indicators
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="focus-indicator">Enable Focus Indicator</Label>
                <Switch
                  id="focus-indicator"
                  checked={config.enableFocusIndicator}
                  onCheckedChange={(checked) => handleConfigChange({ enableFocusIndicator: checked })}
                />
              </div>

              {config.enableFocusIndicator && (
                <div className="space-y-4 ml-4">
                  <div className="space-y-2">
                    <Label>Indicator Style</Label>
                    <Select
                      value={config.focusIndicatorStyle}
                      onValueChange={(value: string) => handleConfigChange({ focusIndicatorStyle: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="outline">Outline</SelectItem>
                        <SelectItem value="highlight">Highlight</SelectItem>
                        <SelectItem value="shadow">Shadow</SelectItem>
                        <SelectItem value="glow">Glow</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <ColorPicker
                    label="Indicator Color"
                    value={config.focusIndicatorColor}
                    onChange={(value) => handleConfigChange({ focusIndicatorColor: value })}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Reading Speed Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <TrendingUp className="h-5 w-5" />
                Reading Speed
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="reading-speed">Track Reading Speed</Label>
                <Switch
                  id="reading-speed"
                  checked={config.enableReadingSpeed}
                  onCheckedChange={(checked) => handleConfigChange({ enableReadingSpeed: checked })}
                />
              </div>

              {config.enableReadingSpeed && (
                <div className="space-y-4 ml-4">
                  <div className="space-y-2">
                    <Label>Target Speed: {config.targetReadingSpeed} WPM</Label>
                    <Slider
                      value={[config.targetReadingSpeed]}
                      onValueChange={([value]) => handleConfigChange({ targetReadingSpeed: value })}
                      min={50}
                      max={500}
                      step={10}
                      className="w-full"
                    />
                  </div>

                  {readingSpeed > 0 && (
                    <div className="p-3 bg-muted rounded-lg">
                      <div className="text-sm font-medium">Current Reading Speed</div>
                      <div className="text-2xl font-bold text-primary">
                        {Math.round(readingSpeed)} WPM
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Words read: {wordCount}
                      </div>
                    </div>
                  )}
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label htmlFor="pause-reminders">Pause Reminders</Label>
                <Switch
                  id="pause-reminders"
                  checked={config.enablePauseReminders}
                  onCheckedChange={(checked) => handleConfigChange({ enablePauseReminders: checked })}
                />
              </div>

              {config.enablePauseReminders && (
                <div className="space-y-2 ml-4">
                  <Label>Reminder Interval: {config.pauseReminderInterval} minutes</Label>
                  <Slider
                    value={[config.pauseReminderInterval]}
                    onValueChange={([value]) => handleConfigChange({ pauseReminderInterval: value })}
                    min={5}
                    max={60}
                    step={5}
                    className="w-full"
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="speech" className="space-y-4">
          {/* Text-to-Speech Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Volume2 className="h-5 w-5" />
                Text-to-Speech
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="text-to-speech">Enable Text-to-Speech</Label>
                <Switch
                  id="text-to-speech"
                  checked={config.enableTextToSpeech}
                  onCheckedChange={(checked) => handleConfigChange({ enableTextToSpeech: checked })}
                />
              </div>

              {config.enableTextToSpeech && (
                <div className="space-y-4 ml-4">
                  <div className="space-y-2">
                    <Label>Voice</Label>
                    <Select
                      value={config.speechVoice}
                      onValueChange={(value) => handleConfigChange({ speechVoice: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select a voice" />
                      </SelectTrigger>
                      <SelectContent>
                        {availableVoices.map((voice) => (
                          <SelectItem key={voice.name} value={voice.name}>
                            {voice.name} ({voice.lang})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Speech Rate: {config.speechRate.toFixed(1)}</Label>
                    <Slider
                      value={[config.speechRate]}
                      onValueChange={([value]) => handleConfigChange({ speechRate: value })}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Speech Pitch: {config.speechPitch.toFixed(1)}</Label>
                    <Slider
                      value={[config.speechPitch]}
                      onValueChange={([value]) => handleConfigChange({ speechPitch: value })}
                      min={0.5}
                      max={2.0}
                      step={0.1}
                      className="w-full"
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                variant="outline"
                onClick={() => assistanceManager.toggleReadingGuide()}
                className="w-full justify-start"
              >
                <Eye className="h-4 w-4 mr-2" />
                Toggle Reading Guide (Alt+R)
              </Button>
              
              <Button
                variant="outline"
                onClick={() => assistanceManager.toggleReadingMask()}
                className="w-full justify-start"
              >
                <Target className="h-4 w-4 mr-2" />
                Toggle Reading Mask (Alt+M)
              </Button>
              
              <Button
                variant="outline"
                onClick={() => assistanceManager.toggleTextToSpeech()}
                className="w-full justify-start"
              >
                <Volume2 className="h-4 w-4 mr-2" />
                Toggle Text-to-Speech (Alt+S)
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="session" className="space-y-4">
          {/* Reading Session */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Timer className="h-5 w-5" />
                Reading Session
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-2">
                {!isSessionActive ? (
                  <Button onClick={handleStartSession} className="flex-1">
                    <Play className="h-4 w-4 mr-2" />
                    Start Reading Session
                  </Button>
                ) : (
                  <Button onClick={handleEndSession} variant="outline" className="flex-1">
                    <Square className="h-4 w-4 mr-2" />
                    End Session
                  </Button>
                )}
              </div>

              {currentSession && (
                <div className="space-y-3">
                  <div className="p-3 bg-muted rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="font-medium">Session Time</div>
                        <div className="text-muted-foreground">
                          {isSessionActive 
                            ? formatTime(Date.now() - currentSession.startTime.getTime())
                            : currentSession.endTime 
                              ? formatTime(currentSession.endTime.getTime() - currentSession.startTime.getTime())
                              : '0:00'
                          }
                        </div>
                      </div>
                      <div>
                        <div className="font-medium">Words Read</div>
                        <div className="text-muted-foreground">{currentSession.wordsRead}</div>
                      </div>
                      <div>
                        <div className="font-medium">Average Speed</div>
                        <div className="text-muted-foreground">
                          {Math.round(currentSession.averageSpeed)} WPM
                        </div>
                      </div>
                      <div>
                        <div className="font-medium">Focus Score</div>
                        <div className="text-muted-foreground">
                          {Math.round(currentSession.focusScore * 100)}%
                        </div>
                      </div>
                    </div>
                  </div>

                  {currentSession.assistanceUsed.length > 0 && (
                    <div>
                      <div className="text-sm font-medium mb-2">Assistance Used:</div>
                      <div className="flex flex-wrap gap-1">
                        {currentSession.assistanceUsed.map((assistance, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {assistance}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
