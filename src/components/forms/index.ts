// Form components - consolidated with enhanced features
export { default as PDFFormDesigner } from "./pdf-form-designer";
export { default as PDFFormManager } from "./pdf-form-manager";
export { default as PDFFormOverlay } from "./pdf-form-overlay";
export { default as PDFFormValidation } from "./pdf-form-validation";

// Export enhanced types and interfaces from the consolidated form manager
export type {
  FormFieldType,
  FormField,
  FormFieldOption,
  FormFieldValidation,
  ValidationRule,
  ValidationResult,
  FormFieldDependency,
  FormTemplate,
  FormValidationResult,
  FormData,
} from "./pdf-form-manager";
