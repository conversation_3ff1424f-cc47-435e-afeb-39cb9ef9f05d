"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Progress } from "@/components/ui/progress";
import {
  AlertCircle,
  CheckCircle,
  AlertTriangle,
  FileCheck,
  RefreshCw,
} from "lucide-react";
import type { FormField, FormData } from "./pdf-form-manager";

interface ValidationRule {
  id: string;
  fieldName: string;
  rule: string;
  message: string;
  severity: "error" | "warning" | "info";
}

interface ValidationResult {
  isValid: boolean;
  errors: ValidationRule[];
  warnings: ValidationRule[];
  completionRate: number;
  fieldResults: Record<string, { isValid: boolean; message?: string }>;
}

interface PDFFormValidationProps {
  formFields: FormField[];
  formData: FormData;
  onValidationChange: (result: ValidationResult) => void;
}

export default function PDFFormValidation({
  formFields,
  formData,
  onValidationChange,
}: PDFFormValidationProps) {
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: [],
    warnings: [],
    completionRate: 0,
    fieldResults: {},
  });

  const validateForm = useCallback(() => {
    const errors: ValidationRule[] = [];
    const warnings: ValidationRule[] = [];
    const fieldResults: Record<string, { isValid: boolean; message?: string }> =
      {};
    let completedFields = 0;

    formFields.forEach((field) => {
      const value = formData[field.name];
      let isFieldValid = true;
      let fieldMessage = "";

      // Check if field has a value
      const hasValue =
        value !== "" &&
        value !== null &&
        value !== undefined &&
        value !== false;
      if (hasValue) {
        completedFields++;
      }

      // Required field validation
      if (field.required && !hasValue) {
        errors.push({
          id: `${field.id}-required`,
          fieldName: field.name,
          rule: "required",
          message: `${field.name} is required`,
          severity: "error",
        });
        isFieldValid = false;
        fieldMessage = "This field is required";
      }

      // Type-specific validation
      if (hasValue) {
        switch (field.type) {
          case "email":
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (typeof value === 'string' && !emailRegex.test(value)) {
              errors.push({
                id: `${field.id}-email`,
                fieldName: field.name,
                rule: "email",
                message: `${field.name} must be a valid email address`,
                severity: "error",
              });
              isFieldValid = false;
              fieldMessage = "Invalid email format";
            }
            break;

          case "number":
            if (isNaN(Number(value))) {
              errors.push({
                id: `${field.id}-number`,
                fieldName: field.name,
                rule: "number",
                message: `${field.name} must be a valid number`,
                severity: "error",
              });
              isFieldValid = false;
              fieldMessage = "Must be a number";
            } else {
              const numValue = Number(value);
              if (
                field.validation?.min !== undefined &&
                numValue < field.validation.min
              ) {
                errors.push({
                  id: `${field.id}-min`,
                  fieldName: field.name,
                  rule: "min",
                  message: `${field.name} must be at least ${field.validation.min}`,
                  severity: "error",
                });
                isFieldValid = false;
                fieldMessage = `Minimum value is ${field.validation.min}`;
              }
              if (
                field.validation?.max !== undefined &&
                numValue > field.validation.max
              ) {
                errors.push({
                  id: `${field.id}-max`,
                  fieldName: field.name,
                  rule: "max",
                  message: `${field.name} must be at most ${field.validation.max}`,
                  severity: "error",
                });
                isFieldValid = false;
                fieldMessage = `Maximum value is ${field.validation.max}`;
              }
            }
            break;

          case "text":
          case "textarea":
            if (
              field.validation?.minLength &&
              typeof value === 'string' &&
              value.length < field.validation.minLength
            ) {
              errors.push({
                id: `${field.id}-minlength`,
                fieldName: field.name,
                rule: "minLength",
                message: `${field.name} must be at least ${field.validation.minLength} characters`,
                severity: "error",
              });
              isFieldValid = false;
              fieldMessage = `Minimum ${field.validation.minLength} characters`;
            }
            if (
              field.validation?.maxLength &&
              typeof value === 'string' &&
              value.length > field.validation.maxLength
            ) {
              errors.push({
                id: `${field.id}-maxlength`,
                fieldName: field.name,
                rule: "maxLength",
                message: `${field.name} must be at most ${field.validation.maxLength} characters`,
                severity: "error",
              });
              isFieldValid = false;
              fieldMessage = `Maximum ${field.validation.maxLength} characters`;
            }
            if (field.validation?.pattern && typeof value === 'string') {
              const regex = new RegExp(field.validation.pattern);
              if (!regex.test(value)) {
                errors.push({
                  id: `${field.id}-pattern`,
                  fieldName: field.name,
                  rule: "pattern",
                  message: `${field.name} format is invalid`,
                  severity: "error",
                });
                isFieldValid = false;
                fieldMessage = "Invalid format";
              }
            }
            break;

          case "date":
            if (typeof value === 'string' || typeof value === 'number') {
              const dateValue = new Date(value);
              if (isNaN(dateValue.getTime())) {
              errors.push({
                id: `${field.id}-date`,
                fieldName: field.name,
                rule: "date",
                message: `${field.name} must be a valid date`,
                severity: "error",
              });
                isFieldValid = false;
                fieldMessage = "Invalid date";
              }
            }
            break;
        }
      }

      // Warnings for optional fields
      if (!field.required && !hasValue) {
        warnings.push({
          id: `${field.id}-optional`,
          fieldName: field.name,
          rule: "optional",
          message: `${field.name} is optional but recommended`,
          severity: "warning",
        });
      }

      fieldResults[field.name] = {
        isValid: isFieldValid,
        message: fieldMessage || undefined,
      };
    });

    const completionRate =
      formFields.length > 0 ? (completedFields / formFields.length) * 100 : 0;

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      completionRate,
      fieldResults,
    };

    setValidationResult(result);
    onValidationChange(result);
  }, [formFields, formData, onValidationChange]);

  useEffect(() => {
    validateForm();
  }, [formFields, formData, validateForm]);

  const getValidationIcon = (severity: "error" | "warning" | "info") => {
    switch (severity) {
      case "error":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getStatusColor = () => {
    if (validationResult.errors.length > 0) return "text-red-600";
    if (validationResult.warnings.length > 0) return "text-yellow-600";
    return "text-green-600";
  };

  const getStatusText = () => {
    if (validationResult.errors.length > 0) return "Has Errors";
    if (validationResult.warnings.length > 0) return "Has Warnings";
    return "Valid";
  };

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileCheck className="h-5 w-5" />
          Form Validation
        </CardTitle>
        <CardDescription>Check form completeness and validity</CardDescription>

        {/* Validation Summary */}
        <div className="grid grid-cols-3 gap-4 p-3 bg-muted/50 rounded-lg">
          <div className="text-center">
            <div className={`text-lg font-bold ${getStatusColor()}`}>
              {getStatusText()}
            </div>
            <div className="text-xs text-muted-foreground">Status</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-red-600">
              {validationResult.errors.length}
            </div>
            <div className="text-xs text-muted-foreground">Errors</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-yellow-600">
              {validationResult.warnings.length}
            </div>
            <div className="text-xs text-muted-foreground">Warnings</div>
          </div>
        </div>

        {/* Completion Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Completion Progress</span>
            <span className="text-sm text-muted-foreground">
              {Math.round(validationResult.completionRate)}%
            </span>
          </div>
          <Progress value={validationResult.completionRate} />
        </div>

        <Button size="sm" onClick={validateForm} className="w-full">
          <RefreshCw className="h-4 w-4 mr-2" />
          Re-validate Form
        </Button>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="space-y-4">
            {/* Errors */}
            {validationResult.errors.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <h4 className="font-medium text-red-700">Errors</h4>
                  <Badge variant="destructive">
                    {validationResult.errors.length}
                  </Badge>
                </div>
                <div className="space-y-2">
                  {validationResult.errors.map((error) => (
                    <div
                      key={error.id}
                      className="p-3 bg-red-50 border border-red-200 rounded-lg"
                    >
                      <div className="flex items-start gap-2">
                        {getValidationIcon(error.severity)}
                        <div className="flex-1">
                          <div className="font-medium text-sm text-red-800">
                            {error.fieldName}
                          </div>
                          <div className="text-sm text-red-700">
                            {error.message}
                          </div>
                          <Badge variant="outline" className="text-xs mt-1">
                            {error.rule}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Warnings */}
            {validationResult.warnings.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <h4 className="font-medium text-yellow-700">Warnings</h4>
                  <Badge variant="secondary">
                    {validationResult.warnings.length}
                  </Badge>
                </div>
                <div className="space-y-2">
                  {validationResult.warnings.map((warning) => (
                    <div
                      key={warning.id}
                      className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
                    >
                      <div className="flex items-start gap-2">
                        {getValidationIcon(warning.severity)}
                        <div className="flex-1">
                          <div className="font-medium text-sm text-yellow-800">
                            {warning.fieldName}
                          </div>
                          <div className="text-sm text-yellow-700">
                            {warning.message}
                          </div>
                          <Badge variant="outline" className="text-xs mt-1">
                            {warning.rule}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Success State */}
            {validationResult.isValid &&
              validationResult.warnings.length === 0 && (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="font-medium text-lg mb-2 text-green-700">
                    Form is Valid!
                  </h3>
                  <p className="text-sm text-green-600">
                    All fields have been completed correctly and validation has
                    passed.
                  </p>
                  <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="text-sm text-green-700">
                      ✅ {formFields.length} fields validated
                      <br />✅ {Math.round(validationResult.completionRate)}%
                      completion rate
                      <br />✅ Ready for submission
                    </div>
                  </div>
                </div>
              )}

            {/* Field Status Summary */}
            {formFields.length > 0 && (
              <div>
                <h4 className="font-medium mb-3">Field Status</h4>
                <div className="space-y-2">
                  {formFields.map((field) => {
                    const fieldResult =
                      validationResult.fieldResults[field.name];
                    const hasValue =
                      formData[field.name] !== "" &&
                      formData[field.name] !== null &&
                      formData[field.name] !== undefined &&
                      formData[field.name] !== false;

                    return (
                      <div
                        key={field.id}
                        className="flex items-center justify-between p-2 border rounded"
                      >
                        <div className="flex items-center gap-2">
                          <div
                            className={`w-2 h-2 rounded-full ${
                              fieldResult?.isValid === false
                                ? "bg-red-500"
                                : hasValue
                                ? "bg-green-500"
                                : "bg-gray-300"
                            }`}
                          />
                          <span className="text-sm font-medium">
                            {field.name}
                          </span>
                          {field.required && (
                            <span className="text-red-500 text-xs">*</span>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={
                              fieldResult?.isValid === false
                                ? "destructive"
                                : hasValue
                                ? "default"
                                : "secondary"
                            }
                            className="text-xs"
                          >
                            {fieldResult?.isValid === false
                              ? "Invalid"
                              : hasValue
                              ? "Complete"
                              : "Empty"}
                          </Badge>
                          {fieldResult?.message && (
                            <span
                              className="text-xs text-muted-foreground"
                              title={fieldResult.message}
                            >
                              ⚠️
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}
