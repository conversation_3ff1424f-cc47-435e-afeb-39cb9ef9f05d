"use client";

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { Document, Page } from 'react-pdf';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  GitCompare,
  Download,
  ZoomIn,
  ZoomOut,
  Eye,
  EyeOff,
  X,
  ChevronLeft,
  ChevronRight,
  Maximize,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';
import { diffLines, diffWords, diffChars } from 'diff';
import type { DocumentInstance } from '@/lib/types/pdf';
import { configurePDFWorker } from '@/lib/pdf-worker-config';
import { ComparisonExporter } from './comparison-export';

// Configure PDF.js worker only on client side
if (typeof window !== 'undefined') {
  configurePDFWorker();
}

export interface ComparisonResult {
  pageNumber: number;
  differences: DiffItem[];
  textDifferences: TextDiff[];
  visualDifferences: VisualDiff[];
  similarity: number;
}

export interface DiffItem {
  id: string;
  type: 'added' | 'removed' | 'modified';
  content: string;
  position: { x: number; y: number; width: number; height: number };
  pageNumber: number;
}

export interface TextDiff {
  type: 'added' | 'removed' | 'unchanged';
  value: string;
  lineNumber?: number;
  charIndex?: number;
}

export interface VisualDiff {
  type: 'layout' | 'content' | 'formatting';
  description: string;
  position: { x: number; y: number; width: number; height: number };
  severity: 'low' | 'medium' | 'high';
}

export interface ComparisonOptions {
  compareMode: 'side-by-side' | 'overlay' | 'text-only';
  diffGranularity: 'page' | 'paragraph' | 'line' | 'word' | 'character';
  ignoreWhitespace: boolean;
  ignoreCase: boolean;
  highlightColors: {
    added: string;
    removed: string;
    modified: string;
  };
  showLineNumbers: boolean;
  syncScroll: boolean;
}

interface PDFComparisonViewerProps {
  document1: DocumentInstance;
  document2: DocumentInstance;
  onClose: () => void;
  onExport?: (format: 'pdf' | 'html' | 'json') => void;
  initialOptions?: Partial<ComparisonOptions>;
  className?: string;
}

const DEFAULT_OPTIONS: ComparisonOptions = {
  compareMode: 'side-by-side',
  diffGranularity: 'line',
  ignoreWhitespace: false,
  ignoreCase: false,
  highlightColors: {
    added: '#22c55e',
    removed: '#ef4444',
    modified: '#f59e0b'
  },
  showLineNumbers: true,
  syncScroll: true
};

export default function PDFComparisonViewer({
  document1,
  document2,
  onClose,
  onExport,
  initialOptions,
  className
}: PDFComparisonViewerProps) {
  // State management
  const [options] = useState<ComparisonOptions>({
    ...DEFAULT_OPTIONS,
    ...initialOptions
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [scale, setScale] = useState(1.0);
  const [isComparing, setIsComparing] = useState(false);
  const [comparisonResults, setComparisonResults] = useState<ComparisonResult[]>([]);

  const [showDifferencesOnly, setShowDifferencesOnly] = useState(false);
  const [activeTab, setActiveTab] = useState<'visual' | 'text' | 'summary'>('visual');

  // Document state
  const [doc1Pages, setDoc1Pages] = useState(0);
  const [doc2Pages, setDoc2Pages] = useState(0);

  // Refs for scroll synchronization
  const leftScrollRef = useRef<HTMLDivElement>(null);
  const rightScrollRef = useRef<HTMLDivElement>(null);
  const isScrolling = useRef(false);

  // Load document success handlers
  const onDocument1LoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setDoc1Pages(numPages);
    toast.success(`Document 1 loaded: ${numPages} pages`);
  }, []);

  const onDocument2LoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setDoc2Pages(numPages);
    toast.success(`Document 2 loaded: ${numPages} pages`);
  }, []);



  // Perform document comparison
  const performComparison = useCallback(async () => {
    if (!doc1Pages || !doc2Pages) return;

    setIsComparing(true);
    const results: ComparisonResult[] = [];

    try {
      const maxPages = Math.max(doc1Pages, doc2Pages);
      
      for (let pageNum = 1; pageNum <= maxPages; pageNum++) {
        const doc1HasPage = pageNum <= doc1Pages;
        const doc2HasPage = pageNum <= doc2Pages;

        let textDifferences: TextDiff[] = [];
        let similarity = 0;

        if (doc1HasPage && doc2HasPage) {
          const text1 = doc1Text[pageNum - 1] || '';
          const text2 = doc2Text[pageNum - 1] || '';

          // Perform text comparison based on granularity
          let diffs;
          switch (options.diffGranularity) {
            case 'character':
              diffs = diffChars(text1, text2);
              break;
            case 'word':
              diffs = diffWords(text1, text2);
              break;
            case 'line':
            default:
              diffs = diffLines(text1, text2);
              break;
          }

          // Convert diff results to our format
          textDifferences = diffs.map(diff => ({
            type: diff.added ? 'added' : diff.removed ? 'removed' : 'unchanged',
            value: diff.value
          }));

          // Calculate similarity
          const totalLength = text1.length + text2.length;
          const unchangedLength = diffs
            .filter(diff => !diff.added && !diff.removed)
            .reduce((sum, diff) => sum + diff.value.length, 0);
          similarity = totalLength > 0 ? (unchangedLength * 2) / totalLength : 1;
        } else {
          // Page exists in only one document
          textDifferences = [{
            type: doc1HasPage ? 'removed' : 'added',
            value: doc1HasPage ? doc1Text[pageNum - 1] || '' : doc2Text[pageNum - 1] || ''
          }];
          similarity = 0;
        }

        results.push({
          pageNumber: pageNum,
          differences: [], // Will be populated with visual differences
          textDifferences,
          visualDifferences: [], // Will be populated with visual analysis
          similarity
        });
      }

      setComparisonResults(results);
      toast.success(`Comparison completed: ${results.length} pages analyzed`);
    } catch (error) {
      console.error('Comparison error:', error);
      toast.error('Failed to perform document comparison');
    } finally {
      setIsComparing(false);
    }
  }, [doc1Pages, doc2Pages, doc1Text, doc2Text, options.diffGranularity]);

  // Scroll synchronization
  const handleScroll = useCallback((source: 'left' | 'right') => {
    if (!options.syncScroll || isScrolling.current) return;

    return (event: React.UIEvent<HTMLDivElement>) => {
      isScrolling.current = true;
      const sourceElement = event.currentTarget;
      const targetElement = source === 'left' ? rightScrollRef.current : leftScrollRef.current;

      if (targetElement) {
        targetElement.scrollTop = sourceElement.scrollTop;
        targetElement.scrollLeft = sourceElement.scrollLeft;
      }

      setTimeout(() => {
        isScrolling.current = false;
      }, 100);
    };
  }, [options.syncScroll]);

  // Navigation functions
  const goToPage = useCallback((page: number) => {
    const maxPage = Math.max(doc1Pages, doc2Pages);
    if (page >= 1 && page <= maxPage) {
      setCurrentPage(page);
    }
  }, [doc1Pages, doc2Pages]);

  const nextPage = useCallback(() => goToPage(currentPage + 1), [currentPage, goToPage]);
  const prevPage = useCallback(() => goToPage(currentPage - 1), [currentPage, goToPage]);

  // Zoom functions
  const zoomIn = useCallback(() => setScale(prev => Math.min(prev * 1.2, 3.0)), []);
  const zoomOut = useCallback(() => setScale(prev => Math.max(prev / 1.2, 0.3)), []);
  const resetZoom = useCallback(() => setScale(1.0), []);

  // Export functionality
  const handleExport = useCallback(async (format: 'pdf' | 'html' | 'json') => {
    if (onExport) {
      onExport(format);
    } else {
      // Use the comparison exporter
      try {
        switch (format) {
          case 'json':
            await ComparisonExporter.exportAsJSON(document1, document2, comparisonResults, options);
            break;
          case 'html':
            await ComparisonExporter.exportAsHTML(document1, document2, comparisonResults, options);
            break;
          case 'pdf':
            // For now, export as HTML (PDF generation would require additional libraries)
            await ComparisonExporter.exportAsHTML(document1, document2, comparisonResults, options);
            break;
        }
        toast.success(`Comparison exported as ${format.toUpperCase()}`);
      } catch (error) {
        console.error('Export error:', error);
        toast.error('Failed to export comparison');
      }
    }
  }, [onExport, document1, document2, comparisonResults, options]);

  // Effect to trigger comparison when documents are loaded
  useEffect(() => {
    if (doc1Pages > 0 && doc2Pages > 0 && doc1Text.length > 0 && doc2Text.length > 0) {
      performComparison();
    }
  }, [doc1Pages, doc2Pages, doc1Text, doc2Text, performComparison]);

  const currentResult = comparisonResults.find(r => r.pageNumber === currentPage);
  const maxPages = Math.max(doc1Pages, doc2Pages);

  return (
    <div className={`h-screen flex flex-col bg-background ${className}`}>
      {/* Header */}
      <div className="border-b p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-2">
              <GitCompare className="h-5 w-5" />
              <h1 className="text-lg font-semibold">Document Comparison</h1>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport('json')}
              disabled={comparisonResults.length === 0}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={performComparison}
              disabled={isComparing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isComparing ? 'animate-spin' : ''}`} />
              {isComparing ? 'Comparing...' : 'Refresh'}
            </Button>
          </div>
        </div>

        {/* Document titles */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex-1">
            <h3 className="font-medium text-sm text-muted-foreground">Document 1</h3>
            <p className="text-sm">{document1.title}</p>
          </div>
          <div className="px-4">
            <Badge variant="outline">
              {comparisonResults.length > 0 && currentResult
                ? `${Math.round(currentResult.similarity * 100)}% similar`
                : 'Analyzing...'}
            </Badge>
          </div>
          <div className="flex-1 text-right">
            <h3 className="font-medium text-sm text-muted-foreground">Document 2</h3>
            <p className="text-sm">{document2.title}</p>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="border-b p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {/* Navigation */}
            <Button
              variant="outline"
              size="sm"
              onClick={prevPage}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm px-2">
              Page {currentPage} of {maxPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={nextPage}
              disabled={currentPage >= maxPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>

            <Separator orientation="vertical" className="h-6" />

            {/* Zoom controls */}
            <Button variant="outline" size="sm" onClick={zoomOut}>
              <ZoomOut className="h-4 w-4" />
            </Button>
            <span className="text-sm px-2">{Math.round(scale * 100)}%</span>
            <Button variant="outline" size="sm" onClick={zoomIn}>
              <ZoomIn className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={resetZoom}>
              <Maximize className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant={showDifferencesOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowDifferencesOnly(!showDifferencesOnly)}
            >
              {showDifferencesOnly ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              <span className="ml-2">Differences Only</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 flex">
        {/* Comparison view */}
        <div className="flex-1 flex">
          {options.compareMode === 'side-by-side' && (
            <>
              {/* Document 1 */}
              <div className="flex-1 border-r">
                <ScrollArea
                  ref={leftScrollRef}
                  className="h-full"
                  onScrollCapture={handleScroll('left')}
                >
                  <div className="p-4">
                    <Document
                      file={document1.file}
                      onLoadSuccess={onDocument1LoadSuccess}
                      className="max-w-full"
                    >
                      <Page
                        pageNumber={currentPage}
                        scale={scale}
                        rotate={rotation}
                        className="shadow-sm"
                      />
                    </Document>
                  </div>
                </ScrollArea>
              </div>

              {/* Document 2 */}
              <div className="flex-1">
                <ScrollArea
                  ref={rightScrollRef}
                  className="h-full"
                  onScrollCapture={handleScroll('right')}
                >
                  <div className="p-4">
                    <Document
                      file={document2.file}
                      onLoadSuccess={onDocument2LoadSuccess}
                      className="max-w-full"
                    >
                      <Page
                        pageNumber={currentPage}
                        scale={scale}
                        rotate={rotation}
                        className="shadow-sm"
                      />
                    </Document>
                  </div>
                </ScrollArea>
              </div>
            </>
          )}
        </div>

        {/* Sidebar with comparison details */}
        <div className="w-80 border-l bg-muted/20">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'visual' | 'text' | 'summary')}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="visual">Visual</TabsTrigger>
              <TabsTrigger value="text">Text</TabsTrigger>
              <TabsTrigger value="summary">Summary</TabsTrigger>
            </TabsList>

            <TabsContent value="visual" className="p-4">
              <h3 className="font-medium mb-3">Visual Differences</h3>
              {currentResult?.visualDifferences.length ? (
                <div className="space-y-2">
                  {currentResult.visualDifferences.map((diff, index) => (
                    <Card key={index} className="p-3">
                      <div className="flex items-center justify-between mb-2">
                        <Badge variant={diff.severity === 'high' ? 'destructive' : 'secondary'}>
                          {diff.type}
                        </Badge>
                        <Badge variant="outline">{diff.severity}</Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">{diff.description}</p>
                    </Card>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No visual differences detected on this page.</p>
              )}
            </TabsContent>

            <TabsContent value="text" className="p-4">
              <h3 className="font-medium mb-3">Text Differences</h3>
              {currentResult?.textDifferences.length ? (
                <ScrollArea className="h-96">
                  <div className="space-y-1">
                    {currentResult.textDifferences.map((diff, index) => (
                      <div
                        key={index}
                        className={`p-2 rounded text-xs ${
                          diff.type === 'added'
                            ? 'bg-green-100 text-green-800'
                            : diff.type === 'removed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-gray-50'
                        }`}
                      >
                        {diff.value}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              ) : (
                <p className="text-sm text-muted-foreground">No text differences detected on this page.</p>
              )}
            </TabsContent>

            <TabsContent value="summary" className="p-4">
              <h3 className="font-medium mb-3">Comparison Summary</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Overall Similarity</h4>
                  <Progress
                    value={(currentResult?.similarity || 0) * 100}
                    className="h-2"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {Math.round((currentResult?.similarity || 0) * 100)}% similar
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Document Info</h4>
                  <div className="text-xs space-y-1">
                    <p>Document 1: {doc1Pages} pages</p>
                    <p>Document 2: {doc2Pages} pages</p>
                    <p>Total differences: {comparisonResults.reduce((sum, r) => sum + r.differences.length, 0)}</p>
                  </div>
                </div>

                {isComparing && (
                  <div>
                    <h4 className="text-sm font-medium mb-2">Analysis Progress</h4>
                    <Progress value={50} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-1">Analyzing documents...</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
