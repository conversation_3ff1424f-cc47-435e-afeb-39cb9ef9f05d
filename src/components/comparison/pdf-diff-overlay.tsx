"use client";

import React, { useMemo } from 'react';
import { Badge } from '@/components/ui/badge';
import type { DiffItem, TextDiff, ComparisonOptions } from './pdf-comparison-viewer';

interface PDFDiffOverlayProps {
  differences: DiffItem[];
  textDifferences: TextDiff[];
  pageNumber: number;
  scale: number;
  options: ComparisonOptions;
  onDiffClick?: (diff: DiffItem) => void;
  className?: string;
}

export default function PDFDiffOverlay({
  differences,
  textDifferences,
  pageNumber: _pageNumber,
  scale,
  options,
  onDiffClick,
  className
}: PDFDiffOverlayProps) {
  // Calculate overlay positions based on scale
  const scaledDifferences = useMemo(() => {
    return differences.map(diff => ({
      ...diff,
      position: {
        x: diff.position.x * scale,
        y: diff.position.y * scale,
        width: diff.position.width * scale,
        height: diff.position.height * scale
      }
    }));
  }, [differences, scale]);

  // Group text differences by type for rendering
  const groupedTextDiffs = useMemo(() => {
    const groups: { [key: string]: TextDiff[] } = {
      added: [],
      removed: [],
      unchanged: []
    };

    textDifferences.forEach(diff => {
      groups[diff.type].push(diff);
    });

    return groups;
  }, [textDifferences]);

  return (
    <div className={`absolute inset-0 pointer-events-none ${className}`}>
      {/* Visual difference overlays */}
      {scaledDifferences.map((diff) => (
        <div
          key={diff.id}
          className="absolute border-2 pointer-events-auto cursor-pointer transition-all hover:opacity-80"
          style={{
            left: diff.position.x,
            top: diff.position.y,
            width: diff.position.width,
            height: diff.position.height,
            borderColor: options.highlightColors[diff.type],
            backgroundColor: `${options.highlightColors[diff.type]}20`,
            zIndex: 10
          }}
          onClick={() => onDiffClick?.(diff)}
          title={`${diff.type}: ${diff.content}`}
        >
          {/* Diff type indicator */}
          <div
            className="absolute -top-6 left-0 text-xs px-1 py-0.5 rounded text-white font-medium"
            style={{
              backgroundColor: options.highlightColors[diff.type]
            }}
          >
            {diff.type.charAt(0).toUpperCase()}
          </div>
        </div>
      ))}

      {/* Text difference summary */}
      {textDifferences.length > 0 && (
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg pointer-events-auto">
          <h4 className="text-sm font-medium mb-2">Text Changes</h4>
          <div className="space-y-1">
            {groupedTextDiffs.added.length > 0 && (
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: options.highlightColors.added }}
                />
                <span className="text-xs">
                  {groupedTextDiffs.added.length} additions
                </span>
              </div>
            )}
            {groupedTextDiffs.removed.length > 0 && (
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded"
                  style={{ backgroundColor: options.highlightColors.removed }}
                />
                <span className="text-xs">
                  {groupedTextDiffs.removed.length} deletions
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Text diff highlighting component for text-only view
interface TextDiffHighlightProps {
  textDifferences: TextDiff[];
  options: ComparisonOptions;
  showLineNumbers?: boolean;
  className?: string;
}

export function TextDiffHighlight({
  textDifferences,
  options,
  showLineNumbers = true,
  className
}: TextDiffHighlightProps) {
  return (
    <div className={`font-mono text-sm ${className}`}>
      {textDifferences.map((diff, index) => {
        const bgColor = diff.type === 'added'
          ? `${options.highlightColors.added}20`
          : diff.type === 'removed'
          ? `${options.highlightColors.removed}20`
          : 'transparent';

        const textColor = diff.type === 'added'
          ? options.highlightColors.added
          : diff.type === 'removed'
          ? options.highlightColors.removed
          : 'inherit';

        return (
          <div
            key={index}
            className="flex"
            style={{
              backgroundColor: bgColor,
              color: textColor
            }}
          >
            {showLineNumbers && options.showLineNumbers && (
              <div className="w-12 text-right pr-2 text-gray-400 select-none">
                {diff.lineNumber || index + 1}
              </div>
            )}
            <div className="flex-1 px-2 py-1 whitespace-pre-wrap">
              {diff.type === 'added' && '+ '}
              {diff.type === 'removed' && '- '}
              {diff.value}
            </div>
          </div>
        );
      })}
    </div>
  );
}

// Diff statistics component
interface DiffStatsProps {
  differences: DiffItem[];
  textDifferences: TextDiff[];
  similarity: number;
  className?: string;
}

export function DiffStats({
  differences,
  textDifferences,
  similarity,
  className
}: DiffStatsProps) {
  const stats = useMemo(() => {
    const added = textDifferences.filter(d => d.type === 'added').length;
    const removed = textDifferences.filter(d => d.type === 'removed').length;
    const unchanged = textDifferences.filter(d => d.type === 'unchanged').length;
    const total = added + removed + unchanged;

    return {
      added,
      removed,
      unchanged,
      total,
      visualDiffs: differences.length,
      similarity: Math.round(similarity * 100)
    };
  }, [differences, textDifferences, similarity]);

  return (
    <div className={`grid grid-cols-2 gap-4 ${className}`}>
      <div className="space-y-2">
        <h4 className="text-sm font-medium">Text Changes</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span className="text-green-600">Added:</span>
            <span>{stats.added}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-red-600">Removed:</span>
            <span>{stats.removed}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Unchanged:</span>
            <span>{stats.unchanged}</span>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <h4 className="text-sm font-medium">Summary</h4>
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>Similarity:</span>
            <Badge variant={stats.similarity > 80 ? 'default' : stats.similarity > 50 ? 'secondary' : 'destructive'}>
              {stats.similarity}%
            </Badge>
          </div>
          <div className="flex justify-between">
            <span>Visual diffs:</span>
            <span>{stats.visualDiffs}</span>
          </div>
          <div className="flex justify-between">
            <span>Total changes:</span>
            <span>{stats.added + stats.removed}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
