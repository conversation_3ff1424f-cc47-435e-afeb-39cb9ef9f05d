"use client"

import React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import {
  LayoutTemplateIcon as Template,
  Plus,
  Download,
  Copy,
  Star,
  Search,
  CheckCircle,
  Users,
  FileText,
  Clock,
  Mail,
  Webhook,
} from "lucide-react"
import { toast } from "sonner"
import type { Workflow } from "./pdf-workflow-builder"

interface WorkflowTemplate {
  id: string
  name: string
  description: string
  category: "approval" | "notification" | "integration" | "compliance" | "custom"
  tags: string[]
  difficulty: "beginner" | "intermediate" | "advanced"
  estimatedTime: string
  workflow: Omit<Workflow, "id" | "metadata">
  preview: {
    nodes: number
    approvers: number
    notifications: number
    integrations: number
  }
  author: string
  rating: number
  downloads: number
  featured: boolean
}

interface PDFWorkflowTemplatesProps {
  onTemplateSelect: (template: WorkflowTemplate) => void
  onCreateFromTemplate: (workflow: Workflow) => void
}

export default function PDFWorkflowTemplates({ onTemplateSelect, onCreateFromTemplate }: PDFWorkflowTemplatesProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState<string>("all")
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null)

  const templates: WorkflowTemplate[] = [
    {
      id: "simple-approval",
      name: "Simple Approval Workflow",
      description: "Basic single-step approval process for form submissions",
      category: "approval",
      tags: ["approval", "basic", "single-step"],
      difficulty: "beginner",
      estimatedTime: "5 minutes",
      workflow: {
        name: "Simple Approval",
        description: "Single approver workflow",
        version: "1.0",
        status: "draft",
        nodes: [
          {
            id: "start-1",
            type: "start",
            label: "Start",
            position: { x: 100, y: 100 },
            config: {},
          },
          {
            id: "form-submit-1",
            type: "form-submit",
            label: "Form Submitted",
            position: { x: 100, y: 200 },
            config: {},
          },
          {
            id: "approval-1",
            type: "approval",
            label: "Manager Approval",
            position: { x: 100, y: 300 },
            config: {
              approvers: ["<EMAIL>"],
              approvalType: "any",
              timeoutDays: 3,
            },
          },
          {
            id: "notification-1",
            type: "notification",
            label: "Approval Notification",
            position: { x: 100, y: 400 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Your form has been approved",
              message: "Your form submission has been approved and processed.",
            },
          },
          {
            id: "end-1",
            type: "end",
            label: "End",
            position: { x: 100, y: 500 },
            config: {},
          },
        ],
        connections: [
          { id: "conn-1", sourceId: "start-1", targetId: "form-submit-1" },
          { id: "conn-2", sourceId: "form-submit-1", targetId: "approval-1" },
          { id: "conn-3", sourceId: "approval-1", targetId: "notification-1", condition: "approved" },
          { id: "conn-4", sourceId: "notification-1", targetId: "end-1" },
        ],
        triggers: { formSubmit: true, manual: false },
      },
      preview: { nodes: 5, approvers: 1, notifications: 1, integrations: 0 },
      author: "Workflow Team",
      rating: 4.8,
      downloads: 1250,
      featured: true,
    },
    {
      id: "multi-level-approval",
      name: "Multi-Level Approval Chain",
      description: "Hierarchical approval process with escalation and conditional routing",
      category: "approval",
      tags: ["approval", "multi-level", "escalation", "hierarchy"],
      difficulty: "intermediate",
      estimatedTime: "15 minutes",
      workflow: {
        name: "Multi-Level Approval",
        description: "Hierarchical approval workflow",
        version: "1.0",
        status: "draft",
        nodes: [
          {
            id: "start-1",
            type: "start",
            label: "Start",
            position: { x: 100, y: 100 },
            config: {},
          },
          {
            id: "form-submit-1",
            type: "form-submit",
            label: "Form Submitted",
            position: { x: 100, y: 200 },
            config: {},
          },
          {
            id: "condition-1",
            type: "condition",
            label: "Check Amount",
            position: { x: 100, y: 300 },
            config: {
              conditions: [{ field: "amount", operator: "greater_than", value: "1000" }],
            },
          },
          {
            id: "approval-1",
            type: "approval",
            label: "Supervisor Approval",
            position: { x: 50, y: 400 },
            config: {
              approvers: ["<EMAIL>"],
              approvalType: "any",
              timeoutDays: 2,
            },
          },
          {
            id: "approval-2",
            type: "approval",
            label: "Manager Approval",
            position: { x: 150, y: 400 },
            config: {
              approvers: ["<EMAIL>"],
              approvalType: "any",
              timeoutDays: 5,
            },
          },
          {
            id: "notification-1",
            type: "notification",
            label: "Final Notification",
            position: { x: 100, y: 500 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Your request has been processed",
              message: "Your request has been approved and is being processed.",
            },
          },
          {
            id: "end-1",
            type: "end",
            label: "End",
            position: { x: 100, y: 600 },
            config: {},
          },
        ],
        connections: [
          { id: "conn-1", sourceId: "start-1", targetId: "form-submit-1" },
          { id: "conn-2", sourceId: "form-submit-1", targetId: "condition-1" },
          { id: "conn-3", sourceId: "condition-1", targetId: "approval-1", condition: "false" },
          { id: "conn-4", sourceId: "condition-1", targetId: "approval-2", condition: "true" },
          { id: "conn-5", sourceId: "approval-1", targetId: "notification-1", condition: "approved" },
          { id: "conn-6", sourceId: "approval-2", targetId: "notification-1", condition: "approved" },
          { id: "conn-7", sourceId: "notification-1", targetId: "end-1" },
        ],
        triggers: { formSubmit: true, manual: false },
      },
      preview: { nodes: 7, approvers: 2, notifications: 1, integrations: 0 },
      author: "Enterprise Team",
      rating: 4.6,
      downloads: 890,
      featured: true,
    },
    {
      id: "notification-cascade",
      name: "Notification Cascade",
      description: "Automated notification system with multiple recipients and channels",
      category: "notification",
      tags: ["notification", "cascade", "multi-channel", "automated"],
      difficulty: "beginner",
      estimatedTime: "8 minutes",
      workflow: {
        name: "Notification Cascade",
        description: "Multi-channel notification workflow",
        version: "1.0",
        status: "draft",
        nodes: [
          {
            id: "start-1",
            type: "start",
            label: "Start",
            position: { x: 100, y: 100 },
            config: {},
          },
          {
            id: "form-submit-1",
            type: "form-submit",
            label: "Form Submitted",
            position: { x: 100, y: 200 },
            config: {},
          },
          {
            id: "notification-1",
            type: "notification",
            label: "Immediate Notification",
            position: { x: 50, y: 300 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "New form submission",
              message: "A new form has been submitted and requires attention.",
            },
          },
          {
            id: "notification-2",
            type: "notification",
            label: "Team Notification",
            position: { x: 150, y: 300 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Form submission update",
              message: "A form submission is being processed by the team.",
            },
          },
          {
            id: "delay-1",
            type: "delay",
            label: "Wait 1 Hour",
            position: { x: 100, y: 400 },
            config: {
              delayHours: 1,
            },
          },
          {
            id: "notification-3",
            type: "notification",
            label: "Follow-up Notification",
            position: { x: 100, y: 500 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Form processing update",
              message: "Your form is being processed. You will receive updates as they become available.",
            },
          },
          {
            id: "end-1",
            type: "end",
            label: "End",
            position: { x: 100, y: 600 },
            config: {},
          },
        ],
        connections: [
          { id: "conn-1", sourceId: "start-1", targetId: "form-submit-1" },
          { id: "conn-2", sourceId: "form-submit-1", targetId: "notification-1" },
          { id: "conn-3", sourceId: "form-submit-1", targetId: "notification-2" },
          { id: "conn-4", sourceId: "notification-1", targetId: "delay-1" },
          { id: "conn-5", sourceId: "notification-2", targetId: "delay-1" },
          { id: "conn-6", sourceId: "delay-1", targetId: "notification-3" },
          { id: "conn-7", sourceId: "notification-3", targetId: "end-1" },
        ],
        triggers: { formSubmit: true, manual: false },
      },
      preview: { nodes: 7, approvers: 0, notifications: 3, integrations: 0 },
      author: "Communication Team",
      rating: 4.4,
      downloads: 650,
      featured: false,
    },
    {
      id: "webhook-integration",
      name: "External System Integration",
      description: "Integrate with external systems using webhooks and API calls",
      category: "integration",
      tags: ["webhook", "api", "integration", "external"],
      difficulty: "advanced",
      estimatedTime: "20 minutes",
      workflow: {
        name: "External Integration",
        description: "Webhook-based integration workflow",
        version: "1.0",
        status: "draft",
        nodes: [
          {
            id: "start-1",
            type: "start",
            label: "Start",
            position: { x: 100, y: 100 },
            config: {},
          },
          {
            id: "form-submit-1",
            type: "form-submit",
            label: "Form Submitted",
            position: { x: 100, y: 200 },
            config: {},
          },
          {
            id: "webhook-1",
            type: "webhook",
            label: "CRM Integration",
            position: { x: 50, y: 300 },
            config: {
              url: "https://api.crm.com/leads",
              method: "POST",
              payload: '{"lead": "{{formData}}", "source": "pdf_form"}',
            },
          },
          {
            id: "webhook-2",
            type: "webhook",
            label: "Analytics Tracking",
            position: { x: 150, y: 300 },
            config: {
              url: "https://analytics.company.com/events",
              method: "POST",
              payload: '{"event": "form_submitted", "data": "{{formData}}"}',
            },
          },
          {
            id: "condition-1",
            type: "condition",
            label: "Check Integration Success",
            position: { x: 100, y: 400 },
            config: {
              conditions: [{ field: "webhook_status", operator: "equals", value: "success" }],
            },
          },
          {
            id: "notification-1",
            type: "notification",
            label: "Success Notification",
            position: { x: 50, y: 500 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Integration successful",
              message: "Form data has been successfully integrated with external systems.",
            },
          },
          {
            id: "notification-2",
            type: "notification",
            label: "Error Notification",
            position: { x: 150, y: 500 },
            config: {
              recipients: ["<EMAIL>"],
              subject: "Integration failed",
              message: "There was an error integrating form data with external systems.",
            },
          },
          {
            id: "end-1",
            type: "end",
            label: "End",
            position: { x: 100, y: 600 },
            config: {},
          },
        ],
        connections: [
          { id: "conn-1", sourceId: "start-1", targetId: "form-submit-1" },
          { id: "conn-2", sourceId: "form-submit-1", targetId: "webhook-1" },
          { id: "conn-3", sourceId: "form-submit-1", targetId: "webhook-2" },
          { id: "conn-4", sourceId: "webhook-1", targetId: "condition-1" },
          { id: "conn-5", sourceId: "webhook-2", targetId: "condition-1" },
          { id: "conn-6", sourceId: "condition-1", targetId: "notification-1", condition: "true" },
          { id: "conn-7", sourceId: "condition-1", targetId: "notification-2", condition: "false" },
          { id: "conn-8", sourceId: "notification-1", targetId: "end-1" },
          { id: "conn-9", sourceId: "notification-2", targetId: "end-1" },
        ],
        triggers: { formSubmit: true, manual: false },
      },
      preview: { nodes: 8, approvers: 0, notifications: 2, integrations: 2 },
      author: "Integration Team",
      rating: 4.7,
      downloads: 420,
      featured: false,
    },
    {
      id: "compliance-audit",
      name: "Compliance & Audit Trail",
      description: "Comprehensive workflow with audit logging and compliance checks",
      category: "compliance",
      tags: ["compliance", "audit", "logging", "governance"],
      difficulty: "advanced",
      estimatedTime: "25 minutes",
      workflow: {
        name: "Compliance Workflow",
        description: "Audit and compliance workflow",
        version: "1.0",
        status: "draft",
        nodes: [
          {
            id: "start-1",
            type: "start",
            label: "Start",
            position: { x: 100, y: 100 },
            config: {},
          },
          {
            id: "form-submit-1",
            type: "form-submit",
            label: "Form Submitted",
            position: { x: 100, y: 200 },
            config: {},
          },
          {
            id: "webhook-1",
            type: "webhook",
            label: "Audit Log Entry",
            position: { x: 100, y: 300 },
            config: {
              url: "https://audit.company.com/log",
              method: "POST",
              payload:
                '{"action": "form_submitted", "user": "{{user}}", "timestamp": "{{timestamp}}", "data": "{{formData}}"}',
            },
          },
          {
            id: "condition-1",
            type: "condition",
            label: "Compliance Check",
            position: { x: 100, y: 400 },
            config: {
              conditions: [{ field: "compliance_required", operator: "equals", value: "true" }],
            },
          },
          {
            id: "approval-1",
            type: "approval",
            label: "Compliance Officer Review",
            position: { x: 50, y: 500 },
            config: {
              approvers: ["<EMAIL>"],
              approvalType: "any",
              timeoutDays: 7,
            },
          },
          {
            id: "webhook-2",
            type: "webhook",
            label: "Compliance Approval Log",
            position: { x: 150, y: 500 },
            config: {
              url: "https://audit.company.com/compliance",
              method: "POST",
              payload: '{"action": "compliance_approved", "approver": "{{approver}}", "timestamp": "{{timestamp}}"}',
            },
          },
          {
            id: "notification-1",
            type: "notification",
            label: "Final Notification",
            position: { x: 100, y: 600 },
            config: {
              recipients: ["<EMAIL>", "<EMAIL>"],
              subject: "Compliance review completed",
              message: "Your submission has completed the compliance review process.",
            },
          },
          {
            id: "end-1",
            type: "end",
            label: "End",
            position: { x: 100, y: 700 },
            config: {},
          },
        ],
        connections: [
          { id: "conn-1", sourceId: "start-1", targetId: "form-submit-1" },
          { id: "conn-2", sourceId: "form-submit-1", targetId: "webhook-1" },
          { id: "conn-3", sourceId: "webhook-1", targetId: "condition-1" },
          { id: "conn-4", sourceId: "condition-1", targetId: "approval-1", condition: "true" },
          { id: "conn-5", sourceId: "condition-1", targetId: "notification-1", condition: "false" },
          { id: "conn-6", sourceId: "approval-1", targetId: "webhook-2", condition: "approved" },
          { id: "conn-7", sourceId: "webhook-2", targetId: "notification-1" },
          { id: "conn-8", sourceId: "notification-1", targetId: "end-1" },
        ],
        triggers: { formSubmit: true, manual: false },
      },
      preview: { nodes: 8, approvers: 1, notifications: 1, integrations: 2 },
      author: "Compliance Team",
      rating: 4.9,
      downloads: 320,
      featured: true,
    },
  ]

  const categories = [
    { value: "all", label: "All Templates" },
    { value: "approval", label: "Approval" },
    { value: "notification", label: "Notification" },
    { value: "integration", label: "Integration" },
    { value: "compliance", label: "Compliance" },
    { value: "custom", label: "Custom" },
  ]

  const filteredTemplates = templates.filter((template) => {
    const matchesSearch =
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = selectedCategory === "all" || template.category === selectedCategory

    return matchesSearch && matchesCategory
  })

  const createFromTemplate = (template: WorkflowTemplate) => {
    const newWorkflow: Workflow = {
      ...template.workflow,
      id: `workflow-${Date.now()}`,
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        createdBy: "current-user",
        tags: template.tags,
      },
    }

    onCreateFromTemplate(newWorkflow)

    toast.success("Workflow created", {
      description: `Created workflow from template: ${template.name}`,
    })
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-500"
      case "intermediate":
        return "bg-yellow-500"
      case "advanced":
        return "bg-red-500"
      default:
        return "bg-gray-500"
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "approval":
        return CheckCircle
      case "notification":
        return Mail
      case "integration":
        return Webhook
      case "compliance":
        return FileText
      default:
        return Template
    }
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Template className="h-5 w-5" />
          Workflow Templates
        </CardTitle>
        <CardDescription>Pre-built workflows to get you started quickly</CardDescription>

        {/* Search and Filter */}
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border rounded-md bg-background"
          >
            {categories.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <ScrollArea className="h-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {filteredTemplates.map((template) => {
              const CategoryIcon = getCategoryIcon(template.category)

              return (
                <div
                  key={template.id}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => setSelectedTemplate(template)}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <CategoryIcon className="h-5 w-5 text-primary" />
                      <h3 className="font-medium">{template.name}</h3>
                      {template.featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                    </div>
                    <Badge
                      variant="outline"
                      className={`text-xs ${getDifficultyColor(template.difficulty)} text-white`}
                    >
                      {template.difficulty}
                    </Badge>
                  </div>

                  <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{template.description}</p>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {template.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                    {template.tags.length > 3 && (
                      <Badge variant="secondary" className="text-xs">
                        +{template.tags.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-4 gap-2 mb-3 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      {template.preview.nodes} nodes
                    </div>
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {template.preview.approvers}
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="h-3 w-3" />
                      {template.preview.notifications}
                    </div>
                    <div className="flex items-center gap-1">
                      <Webhook className="h-3 w-3" />
                      {template.preview.integrations}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        {template.rating}
                      </div>
                      <div className="flex items-center gap-1">
                        <Download className="h-3 w-3" />
                        {template.downloads}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {template.estimatedTime}
                      </div>
                    </div>
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation()
                        createFromTemplate(template)
                      }}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Use
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>

          {filteredTemplates.length === 0 && (
            <div className="text-center py-8">
              <Template className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">No Templates Found</h3>
              <p className="text-sm text-muted-foreground">Try adjusting your search terms or category filter</p>
            </div>
          )}
        </ScrollArea>

        {/* Template Details Dialog */}
        {selectedTemplate && (
          <Dialog open={!!selectedTemplate} onOpenChange={() => setSelectedTemplate(null)}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {React.createElement(getCategoryIcon(selectedTemplate.category), { className: "h-5 w-5" })}
                  {selectedTemplate.name}
                  {selectedTemplate.featured && <Star className="h-4 w-4 text-yellow-500 fill-current" />}
                </DialogTitle>
                <DialogDescription>{selectedTemplate.description}</DialogDescription>
              </DialogHeader>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Template Info</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Category:</span>
                        <Badge variant="outline">{selectedTemplate.category}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Difficulty:</span>
                        <Badge className={`${getDifficultyColor(selectedTemplate.difficulty)} text-white`}>
                          {selectedTemplate.difficulty}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Est. Time:</span>
                        <span>{selectedTemplate.estimatedTime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Author:</span>
                        <span>{selectedTemplate.author}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2">Workflow Preview</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Nodes:</span>
                        <span>{selectedTemplate.preview.nodes}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Approvers:</span>
                        <span>{selectedTemplate.preview.approvers}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Notifications:</span>
                        <span>{selectedTemplate.preview.notifications}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Integrations:</span>
                        <span>{selectedTemplate.preview.integrations}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedTemplate.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      createFromTemplate(selectedTemplate)
                      setSelectedTemplate(null)
                    }}
                    className="flex-1"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create from Template
                  </Button>
                  <Button variant="outline" onClick={() => onTemplateSelect(selectedTemplate)}>
                    <Copy className="h-4 w-4 mr-2" />
                    Preview
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </CardContent>
    </Card>
  )
}
