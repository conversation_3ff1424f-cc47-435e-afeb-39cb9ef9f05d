"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import {
  Play,
  Plus,
  Download,
  Settings,
  Trash2,
  Copy,
  GitBranch,
  CheckCircle,
  XCircle,
  Clock,
  Mail,
  Webhook,
  FileText,
  Diamond,
} from "lucide-react"
import { toast } from "sonner"

export type WorkflowNodeType =
  | "start"
  | "form-submit"
  | "approval"
  | "condition"
  | "notification"
  | "webhook"
  | "delay"
  | "end"

export interface WorkflowNode {
  id: string
  type: WorkflowNodeType
  label: string
  position: { x: number; y: number }
  config: {
    // Approval node config
    approvers?: string[]
    approvalType?: "any" | "all" | "majority"
    timeoutDays?: number

    // Condition node config
    conditions?: Array<{
      field: string
      operator: "equals" | "not_equals" | "contains" | "greater_than" | "less_than"
      value: string
    }>

    // Notification config
    recipients?: string[]
    subject?: string
    message?: string
    template?: string

    // Webhook config
    url?: string
    method?: "GET" | "POST" | "PUT"
    headers?: Record<string, string>
    payload?: string

    // Delay config
    delayDays?: number
    delayHours?: number

    // General config
    description?: string
    metadata?: Record<string, unknown>
  }
}

export interface WorkflowConnection {
  id: string
  sourceId: string
  targetId: string
  label?: string
  condition?: "approved" | "rejected" | "true" | "false"
}

export interface Workflow {
  id: string
  name: string
  description: string
  version: string
  status: "draft" | "active" | "inactive"
  nodes: WorkflowNode[]
  connections: WorkflowConnection[]
  triggers: {
    formSubmit?: boolean
    manual?: boolean
    scheduled?: string
  }
  metadata: {
    created: number
    modified: number
    createdBy: string
    tags: string[]
  }
}

interface PDFWorkflowBuilderProps {
  workflows: Workflow[]
  onWorkflowsChange: (workflows: Workflow[]) => void
  formFields: Array<{ name: string; type: string }>
}

export default function PDFWorkflowBuilder({ workflows, onWorkflowsChange, formFields }: PDFWorkflowBuilderProps) {
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(null)
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null)

  const [draggedNodeType, setDraggedNodeType] = useState<WorkflowNodeType | null>(null)
  const canvasRef = useRef<HTMLDivElement>(null)

  const nodeTypes = [
    { type: "start" as const, label: "Start", icon: Play, color: "bg-green-500" },
    { type: "form-submit" as const, label: "Form Submit", icon: FileText, color: "bg-blue-500" },
    { type: "approval" as const, label: "Approval", icon: CheckCircle, color: "bg-yellow-500" },
    { type: "condition" as const, label: "Condition", icon: Diamond, color: "bg-purple-500" },
    { type: "notification" as const, label: "Notification", icon: Mail, color: "bg-orange-500" },
    { type: "webhook" as const, label: "Webhook", icon: Webhook, color: "bg-indigo-500" },
    { type: "delay" as const, label: "Delay", icon: Clock, color: "bg-gray-500" },
    { type: "end" as const, label: "End", icon: XCircle, color: "bg-red-500" },
  ]

  const createNewWorkflow = () => {
    const newWorkflow: Workflow = {
      id: `workflow-${Date.now()}`,
      name: "New Workflow",
      description: "Workflow description",
      version: "1.0",
      status: "draft",
      nodes: [
        {
          id: "start-1",
          type: "start",
          label: "Start",
          position: { x: 100, y: 100 },
          config: {},
        },
      ],
      connections: [],
      triggers: {
        formSubmit: true,
        manual: false,
      },
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        createdBy: "current-user",
        tags: [],
      },
    }

    const updatedWorkflows = [...workflows, newWorkflow]
    onWorkflowsChange(updatedWorkflows)
    setSelectedWorkflow(newWorkflow)

    toast.success("Workflow created", {
      description: "New workflow has been created successfully",
    })
  }

  const updateWorkflow = (updates: Partial<Workflow>) => {
    if (!selectedWorkflow) return

    const updatedWorkflow = {
      ...selectedWorkflow,
      ...updates,
      metadata: {
        ...selectedWorkflow.metadata,
        modified: Date.now(),
      },
    }

    const updatedWorkflows = workflows.map((w) => (w.id === selectedWorkflow.id ? updatedWorkflow : w))

    onWorkflowsChange(updatedWorkflows)
    setSelectedWorkflow(updatedWorkflow)
  }

  const addNode = (type: WorkflowNodeType, position: { x: number; y: number }) => {
    if (!selectedWorkflow) return

    const nodeConfig = nodeTypes.find((nt) => nt.type === type)
    const newNode: WorkflowNode = {
      id: `${type}-${Date.now()}`,
      type,
      label: nodeConfig?.label || type,
      position,
      config: {},
    }

    updateWorkflow({
      nodes: [...selectedWorkflow.nodes, newNode],
    })
  }

  const updateNode = (nodeId: string, updates: Partial<WorkflowNode>) => {
    if (!selectedWorkflow) return

    const updatedNodes = selectedWorkflow.nodes.map((node) => (node.id === nodeId ? { ...node, ...updates } : node))

    updateWorkflow({ nodes: updatedNodes })

    if (selectedNode?.id === nodeId) {
      setSelectedNode({ ...selectedNode, ...updates })
    }
  }

  const deleteNode = (nodeId: string) => {
    if (!selectedWorkflow) return

    const updatedNodes = selectedWorkflow.nodes.filter((node) => node.id !== nodeId)
    const updatedConnections = selectedWorkflow.connections.filter(
      (conn) => conn.sourceId !== nodeId && conn.targetId !== nodeId,
    )

    updateWorkflow({
      nodes: updatedNodes,
      connections: updatedConnections,
    })

    if (selectedNode?.id === nodeId) {
      setSelectedNode(null)
    }
  }



  const duplicateWorkflow = (workflow: Workflow) => {
    const duplicated: Workflow = {
      ...workflow,
      id: `workflow-${Date.now()}`,
      name: `${workflow.name} (Copy)`,
      status: "draft",
      metadata: {
        ...workflow.metadata,
        created: Date.now(),
        modified: Date.now(),
      },
    }

    const updatedWorkflows = [...workflows, duplicated]
    onWorkflowsChange(updatedWorkflows)

    toast.success("Workflow duplicated", {
      description: "Workflow has been copied successfully",
    })
  }

  const exportWorkflow = (workflow: Workflow) => {
    const exportData = {
      ...workflow,
      exportedAt: new Date().toISOString(),
      version: workflow.version,
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: "application/json" })
    const url = URL.createObjectURL(blob)
    const link = document.createElement("a")
    link.href = url
    link.download = `workflow-${workflow.name.toLowerCase().replace(/\s+/g, "-")}.json`
    link.click()
    URL.revokeObjectURL(url)

    toast.success("Workflow exported", {
      description: "Workflow has been exported successfully",
    })
  }

  const handleCanvasDrop = (e: React.DragEvent) => {
    e.preventDefault()
    if (!draggedNodeType || !canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const position = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    }

    addNode(draggedNodeType, position)
    setDraggedNodeType(null)
  }

  const renderNode = (node: WorkflowNode) => {
    const nodeType = nodeTypes.find((nt) => nt.type === node.type)
    const Icon = nodeType?.icon || Play

    return (
      <div
        key={node.id}
        className={`absolute p-3 bg-white border-2 rounded-lg shadow-lg cursor-pointer transition-all ${
          selectedNode?.id === node.id
            ? "border-primary ring-2 ring-primary/20"
            : "border-gray-200 hover:border-gray-300"
        }`}
        style={{
          left: node.position.x,
          top: node.position.y,
          minWidth: "120px",
        }}
        onClick={() => setSelectedNode(node)}
      >
        <div className="flex items-center gap-2">
          <div className={`p-1 rounded ${nodeType?.color} text-white`}>
            <Icon className="h-4 w-4" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-sm">{node.label}</div>
            <div className="text-xs text-muted-foreground">{node.type}</div>
          </div>
        </div>
      </div>
    )
  }

  const renderNodeProperties = () => {
    if (!selectedNode) return null

    return (
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="node-label">Label</Label>
          <Input
            id="node-label"
            value={selectedNode.label}
            onChange={(e) => updateNode(selectedNode.id, { label: e.target.value })}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="node-description">Description</Label>
          <Textarea
            id="node-description"
            value={selectedNode.config.description || ""}
            onChange={(e) =>
              updateNode(selectedNode.id, {
                config: { ...selectedNode.config, description: e.target.value },
              })
            }
          />
        </div>

        {/* Approval Node Properties */}
        {selectedNode.type === "approval" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="approvers">Approvers (comma-separated emails)</Label>
              <Textarea
                id="approvers"
                value={selectedNode.config.approvers?.join(", ") || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: {
                      ...selectedNode.config,
                      approvers: e.target.value
                        .split(",")
                        .map((email) => email.trim())
                        .filter(Boolean),
                    },
                  })
                }
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="approval-type">Approval Type</Label>
              <Select
                value={selectedNode.config.approvalType || "any"}
                onValueChange={(value) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, approvalType: value as "any" | "all" | "majority" },
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Approver</SelectItem>
                  <SelectItem value="all">All Approvers</SelectItem>
                  <SelectItem value="majority">Majority</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="timeout-days">Timeout (Days)</Label>
              <Input
                id="timeout-days"
                type="number"
                value={selectedNode.config.timeoutDays || 7}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, timeoutDays: Number(e.target.value) },
                  })
                }
              />
            </div>
          </>
        )}

        {/* Condition Node Properties */}
        {selectedNode.type === "condition" && (
          <div className="space-y-3">
            <Label>Conditions</Label>
            {selectedNode.config.conditions?.map((condition, index) => (
              <div key={index} className="grid grid-cols-3 gap-2 p-2 border rounded">
                <Select
                  value={condition.field}
                  onValueChange={(value) => {
                    const updatedConditions = [...(selectedNode.config.conditions || [])]
                    updatedConditions[index] = { ...condition, field: value }
                    updateNode(selectedNode.id, {
                      config: { ...selectedNode.config, conditions: updatedConditions },
                    })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Field" />
                  </SelectTrigger>
                  <SelectContent>
                    {formFields.map((field) => (
                      <SelectItem key={field.name} value={field.name}>
                        {field.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={condition.operator}
                  onValueChange={(value) => {
                    const updatedConditions = [...(selectedNode.config.conditions || [])]
                    updatedConditions[index] = { ...condition, operator: value as "equals" | "not_equals" | "contains" | "greater_than" | "less_than" }
                    updateNode(selectedNode.id, {
                      config: { ...selectedNode.config, conditions: updatedConditions },
                    })
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Operator" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals</SelectItem>
                    <SelectItem value="not_equals">Not Equals</SelectItem>
                    <SelectItem value="contains">Contains</SelectItem>
                    <SelectItem value="greater_than">Greater Than</SelectItem>
                    <SelectItem value="less_than">Less Than</SelectItem>
                  </SelectContent>
                </Select>

                <Input
                  value={condition.value}
                  onChange={(e) => {
                    const updatedConditions = [...(selectedNode.config.conditions || [])]
                    updatedConditions[index] = { ...condition, value: e.target.value }
                    updateNode(selectedNode.id, {
                      config: { ...selectedNode.config, conditions: updatedConditions },
                    })
                  }}
                  placeholder="Value"
                />
              </div>
            )) || []}

            <Button
              size="sm"
              onClick={() => {
                const newCondition = { field: "", operator: "equals" as const, value: "" }
                const updatedConditions = [...(selectedNode.config.conditions || []), newCondition]
                updateNode(selectedNode.id, {
                  config: { ...selectedNode.config, conditions: updatedConditions },
                })
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Condition
            </Button>
          </div>
        )}

        {/* Notification Node Properties */}
        {selectedNode.type === "notification" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="recipients">Recipients (comma-separated emails)</Label>
              <Textarea
                id="recipients"
                value={selectedNode.config.recipients?.join(", ") || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: {
                      ...selectedNode.config,
                      recipients: e.target.value
                        .split(",")
                        .map((email) => email.trim())
                        .filter(Boolean),
                    },
                  })
                }
                placeholder="<EMAIL>, <EMAIL>"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="subject">Subject</Label>
              <Input
                id="subject"
                value={selectedNode.config.subject || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, subject: e.target.value },
                  })
                }
                placeholder="Email subject"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={selectedNode.config.message || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, message: e.target.value },
                  })
                }
                placeholder="Email message content"
                rows={4}
              />
            </div>
          </>
        )}

        {/* Webhook Node Properties */}
        {selectedNode.type === "webhook" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="webhook-url">URL</Label>
              <Input
                id="webhook-url"
                value={selectedNode.config.url || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, url: e.target.value },
                  })
                }
                placeholder="https://api.example.com/webhook"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="webhook-method">Method</Label>
              <Select
                value={selectedNode.config.method || "POST"}
                onValueChange={(value) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, method: value as "GET" | "POST" | "PUT" },
                  })
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="webhook-payload">Payload (JSON)</Label>
              <Textarea
                id="webhook-payload"
                value={selectedNode.config.payload || ""}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, payload: e.target.value },
                  })
                }
                placeholder='{"formData": "{{formData}}", "status": "submitted"}'
                rows={4}
              />
            </div>
          </>
        )}

        {/* Delay Node Properties */}
        {selectedNode.type === "delay" && (
          <>
            <div className="space-y-2">
              <Label htmlFor="delay-days">Delay Days</Label>
              <Input
                id="delay-days"
                type="number"
                value={selectedNode.config.delayDays || 0}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, delayDays: Number(e.target.value) },
                  })
                }
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="delay-hours">Delay Hours</Label>
              <Input
                id="delay-hours"
                type="number"
                value={selectedNode.config.delayHours || 0}
                onChange={(e) =>
                  updateNode(selectedNode.id, {
                    config: { ...selectedNode.config, delayHours: Number(e.target.value) },
                  })
                }
              />
            </div>
          </>
        )}

        <div className="flex gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              const duplicatedNode: WorkflowNode = {
                ...selectedNode,
                id: `${selectedNode.type}-${Date.now()}`,
                position: {
                  x: selectedNode.position.x + 20,
                  y: selectedNode.position.y + 20,
                },
              }
              if (selectedWorkflow) {
                updateWorkflow({
                  nodes: [...selectedWorkflow.nodes, duplicatedNode],
                })
              }
            }}
          >
            <Copy className="h-4 w-4 mr-2" />
            Duplicate
          </Button>

          <Button size="sm" variant="destructive" onClick={() => deleteNode(selectedNode.id)}>
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Workflow Builder
            </CardTitle>
            <CardDescription>Create automated form processing workflows</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button size="sm" onClick={createNewWorkflow}>
              <Plus className="h-4 w-4 mr-2" />
              New Workflow
            </Button>
          </div>
        </div>

        {/* Workflow Selector */}
        {workflows.length > 0 && (
          <div className="space-y-2">
            <Label>Select Workflow</Label>
            <Select
              value={selectedWorkflow?.id || ""}
              onValueChange={(value) => {
                const workflow = workflows.find((w) => w.id === value)
                setSelectedWorkflow(workflow || null)
                setSelectedNode(null)
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a workflow to edit" />
              </SelectTrigger>
              <SelectContent>
                {workflows.map((workflow) => (
                  <SelectItem key={workflow.id} value={workflow.id}>
                    <div className="flex items-center gap-2">
                      <Badge variant={workflow.status === "active" ? "default" : "secondary"}>{workflow.status}</Badge>
                      {workflow.name}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        {selectedWorkflow ? (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 h-full">
            {/* Node Palette */}
            <div className="space-y-4">
              <div>
                <h4 className="font-medium mb-2">Workflow Info</h4>
                <div className="space-y-2">
                  <Input
                    value={selectedWorkflow.name}
                    onChange={(e) => updateWorkflow({ name: e.target.value })}
                    placeholder="Workflow name"
                  />
                  <Textarea
                    value={selectedWorkflow.description}
                    onChange={(e) => updateWorkflow({ description: e.target.value })}
                    placeholder="Workflow description"
                    rows={2}
                  />
                  <Select
                    value={selectedWorkflow.status}
                    onValueChange={(value) => updateWorkflow({ status: value as "draft" | "active" | "inactive" })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Separator />

              <div>
                <h4 className="font-medium mb-2">Node Types</h4>
                <div className="space-y-2">
                  {nodeTypes.map((nodeType) => {
                    const Icon = nodeType.icon
                    return (
                      <div
                        key={nodeType.type}
                        className="flex items-center gap-2 p-2 border rounded cursor-move hover:bg-muted/50"
                        draggable
                        onDragStart={() => setDraggedNodeType(nodeType.type)}
                      >
                        <div className={`p-1 rounded ${nodeType.color} text-white`}>
                          <Icon className="h-4 w-4" />
                        </div>
                        <span className="text-sm">{nodeType.label}</span>
                      </div>
                    )
                  })}
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Button size="sm" onClick={() => exportWorkflow(selectedWorkflow)} className="w-full">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => duplicateWorkflow(selectedWorkflow)}
                  className="w-full"
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Duplicate
                </Button>
              </div>
            </div>

            {/* Canvas */}
            <div className="lg:col-span-2 border rounded-lg relative overflow-hidden bg-gray-50">
              <div
                ref={canvasRef}
                className="w-full h-full relative"
                onDrop={handleCanvasDrop}
                onDragOver={(e) => e.preventDefault()}
              >
                {selectedWorkflow.nodes.map(renderNode)}

                {/* Connections */}
                <svg className="absolute inset-0 pointer-events-none" style={{ zIndex: 1 }}>
                  {selectedWorkflow.connections.map((connection) => {
                    const sourceNode = selectedWorkflow.nodes.find((n) => n.id === connection.sourceId)
                    const targetNode = selectedWorkflow.nodes.find((n) => n.id === connection.targetId)

                    if (!sourceNode || !targetNode) return null

                    const x1 = sourceNode.position.x + 60
                    const y1 = sourceNode.position.y + 30
                    const x2 = targetNode.position.x + 60
                    const y2 = targetNode.position.y + 30

                    return (
                      <g key={connection.id}>
                        <line
                          x1={x1}
                          y1={y1}
                          x2={x2}
                          y2={y2}
                          stroke="#666"
                          strokeWidth="2"
                          markerEnd="url(#arrowhead)"
                        />
                        {connection.condition && (
                          <text
                            x={(x1 + x2) / 2}
                            y={(y1 + y2) / 2}
                            textAnchor="middle"
                            className="text-xs fill-gray-600"
                          >
                            {connection.condition}
                          </text>
                        )}
                      </g>
                    )
                  })}

                  <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                    </marker>
                  </defs>
                </svg>
              </div>

              <div className="absolute bottom-4 left-4 text-sm text-muted-foreground">
                Drag nodes from the palette to add them to the workflow
              </div>
            </div>

            {/* Properties Panel */}
            <div className="space-y-4">
              <h4 className="font-medium">{selectedNode ? "Node Properties" : "Select a node to edit"}</h4>

              {selectedNode ? (
                <ScrollArea className="h-[400px]">{renderNodeProperties()}</ScrollArea>
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  <Settings className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">Click on a node to edit its properties</p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full text-center">
            <div>
              <GitBranch className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-medium text-lg mb-2">No Workflow Selected</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Create a new workflow or select an existing one to start building
              </p>
              <Button onClick={createNewWorkflow}>
                <Plus className="h-4 w-4 mr-2" />
                Create New Workflow
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
