"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Settings, BarChart3, Clock, CheckCircle, XCircle, Calendar } from "lucide-react"
import { toast } from "sonner"
import PDFWorkflowBuilder, { type Workflow } from "./pdf-workflow-builder"
import PDFWorkflowEngine, { type WorkflowExecution } from "./pdf-workflow-engine"
import PDFWorkflowTemplates from "./pdf-workflow-templates"
import type { FormField, FormData } from "../forms/pdf-form-manager"

interface PDFWorkflowManagerProps {
  formFields: FormField[]
  formData: FormData
  currentUser: string
}

export default function PDFWorkflowManager({ formFields, formData, currentUser }: PDFWorkflowManagerProps) {
  const [workflows, setWorkflows] = useState<Workflow[]>([])
  const [executions, setExecutions] = useState<WorkflowExecution[]>([])
  const [activeTab, setActiveTab] = useState("overview")

  // Load workflows and executions from localStorage
  useEffect(() => {
    const savedWorkflows = localStorage.getItem("pdf-workflows")
    if (savedWorkflows) {
      setWorkflows(JSON.parse(savedWorkflows))
    }

    const savedExecutions = localStorage.getItem("pdf-workflow-executions")
    if (savedExecutions) {
      setExecutions(JSON.parse(savedExecutions))
    }
  }, [])

  // Save workflows to localStorage
  const handleWorkflowsChange = (newWorkflows: Workflow[]) => {
    setWorkflows(newWorkflows)
    localStorage.setItem("pdf-workflows", JSON.stringify(newWorkflows))
  }

  // Save executions to localStorage
  const handleExecutionsChange = (newExecutions: WorkflowExecution[]) => {
    setExecutions(newExecutions)
    localStorage.setItem("pdf-workflow-executions", JSON.stringify(newExecutions))
  }

  // Create workflow from template
  const handleCreateFromTemplate = (workflow: Workflow) => {
    const newWorkflows = [...workflows, workflow]
    handleWorkflowsChange(newWorkflows)
    setActiveTab("builder")
  }

  // Calculate statistics
  const stats = {
    totalWorkflows: workflows.length,
    activeWorkflows: workflows.filter((w) => w.status === "active").length,
    totalExecutions: executions.length,
    runningExecutions: executions.filter((e) => e.status === "running" || e.status === "waiting_approval").length,
    completedExecutions: executions.filter((e) => e.status === "completed").length,
    failedExecutions: executions.filter((e) => e.status === "failed").length,
    successRate:
      executions.length > 0
        ? Math.round((executions.filter((e) => e.status === "completed").length / executions.length) * 100)
        : 0,
    avgExecutionTime:
      executions.filter((e) => e.completedAt).reduce((acc, e) => acc + (e.completedAt! - e.startedAt), 0) /
      Math.max(executions.filter((e) => e.completedAt).length, 1),
  }

  const recentExecutions = executions.sort((a, b) => b.startedAt - a.startedAt).slice(0, 5)

  const pendingApprovals = executions.flatMap((e) =>
    e.approvals.filter((a) => a.status === "pending" && a.approver === currentUser),
  )

  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Workflow Manager
        </CardTitle>
        <CardDescription>Manage automated form processing workflows</CardDescription>
      </CardHeader>

      <CardContent className="flex-1 overflow-hidden">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="builder">Builder</TabsTrigger>
            <TabsTrigger value="engine">Engine</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <div className="flex-1 overflow-hidden mt-4">
            <TabsContent value="overview" className="h-full">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
                {/* Statistics */}
                <div className="space-y-4">
                  <h3 className="font-medium">Workflow Statistics</h3>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{stats.totalWorkflows}</div>
                      <div className="text-xs text-blue-700">Total Workflows</div>
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{stats.activeWorkflows}</div>
                      <div className="text-xs text-green-700">Active</div>
                    </div>
                    <div className="p-3 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{stats.runningExecutions}</div>
                      <div className="text-xs text-yellow-700">Running</div>
                    </div>
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{stats.successRate}%</div>
                      <div className="text-xs text-purple-700">Success Rate</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Completed</span>
                      <span>{stats.completedExecutions}</span>
                    </div>
                    <Progress value={(stats.completedExecutions / Math.max(stats.totalExecutions, 1)) * 100} />
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Failed</span>
                      <span>{stats.failedExecutions}</span>
                    </div>
                    <Progress
                      value={(stats.failedExecutions / Math.max(stats.totalExecutions, 1)) * 100}
                      className="[&>div]:bg-red-500"
                    />
                  </div>
                </div>

                {/* Recent Activity */}
                <div className="space-y-4">
                  <h3 className="font-medium">Recent Executions</h3>

                  <ScrollArea className="h-[300px]">
                    <div className="space-y-2">
                      {recentExecutions.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                          <Clock className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No recent executions</p>
                        </div>
                      ) : (
                        recentExecutions.map((execution) => (
                          <div key={execution.id} className="p-3 border rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-sm">{execution.workflowName}</span>
                              <Badge
                                variant={
                                  execution.status === "completed"
                                    ? "default"
                                    : execution.status === "failed"
                                      ? "destructive"
                                      : execution.status === "running"
                                        ? "secondary"
                                        : "outline"
                                }
                              >
                                {execution.status}
                              </Badge>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {new Date(execution.startedAt).toLocaleString()}
                            </div>
                            {execution.status === "running" && (
                              <div className="mt-2">
                                <Progress
                                  value={
                                    (execution.steps.filter((s) => s.status === "completed").length /
                                      Math.max(execution.steps.length, 1)) *
                                    100
                                  }
                                  className="h-1"
                                />
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </ScrollArea>
                </div>

                {/* Pending Approvals */}
                <div className="space-y-4">
                  <h3 className="font-medium">Pending Approvals</h3>

                  <ScrollArea className="h-[300px]">
                    <div className="space-y-2">
                      {pendingApprovals.length === 0 ? (
                        <div className="text-center text-muted-foreground py-8">
                          <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                          <p className="text-sm">No pending approvals</p>
                        </div>
                      ) : (
                        pendingApprovals.map((approval) => {
                          const execution = executions.find((e) => e.approvals.some((a) => a.id === approval.id))

                          return (
                            <div key={approval.id} className="p-3 border rounded-lg bg-yellow-50">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium text-sm">{execution?.workflowName}</span>
                                <Badge variant="outline" className="text-yellow-700">
                                  Pending
                                </Badge>
                              </div>
                              <div className="text-xs text-muted-foreground mb-2">
                                Submitted: {execution ? new Date(execution.startedAt).toLocaleString() : "Unknown"}
                              </div>
                              {approval.expiresAt && (
                                <div className="text-xs text-red-600">
                                  Expires: {new Date(approval.expiresAt).toLocaleString()}
                                </div>
                              )}
                              <div className="flex gap-2 mt-2">
                                <Button size="sm" className="flex-1">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Approve
                                </Button>
                                <Button size="sm" variant="destructive" className="flex-1">
                                  <XCircle className="h-3 w-3 mr-1" />
                                  Reject
                                </Button>
                              </div>
                            </div>
                          )
                        })
                      )}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="builder" className="h-full">
              <PDFWorkflowBuilder
                workflows={workflows}
                onWorkflowsChange={handleWorkflowsChange}
                formFields={formFields}
              />
            </TabsContent>

            <TabsContent value="engine" className="h-full">
              <PDFWorkflowEngine
                workflows={workflows}
                executions={executions}
                onExecutionsChange={handleExecutionsChange}
                formData={formData}
                currentUser={currentUser}
              />
            </TabsContent>

            <TabsContent value="templates" className="h-full">
              <PDFWorkflowTemplates
                onTemplateSelect={(template) => {
                  toast.success("Template selected", {
                    description: `Viewing template: ${template.name}`,
                  })
                }}
                onCreateFromTemplate={handleCreateFromTemplate}
              />
            </TabsContent>

            <TabsContent value="analytics" className="h-full">
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Execution Trends */}
                  <div className="space-y-4">
                    <h3 className="font-medium flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Execution Trends
                    </h3>

                    <div className="p-4 border rounded-lg">
                      <div className="space-y-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Total Executions</span>
                          <span className="font-medium">{stats.totalExecutions}</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Success Rate</span>
                          <span className="font-medium text-green-600">{stats.successRate}%</span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm">Avg. Execution Time</span>
                          <span className="font-medium">{Math.round(stats.avgExecutionTime / 1000 / 60)}m</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-2">
                      <div className="p-3 bg-green-50 rounded text-center">
                        <div className="text-lg font-bold text-green-600">{stats.completedExecutions}</div>
                        <div className="text-xs text-green-700">Completed</div>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded text-center">
                        <div className="text-lg font-bold text-yellow-600">{stats.runningExecutions}</div>
                        <div className="text-xs text-yellow-700">Running</div>
                      </div>
                      <div className="p-3 bg-red-50 rounded text-center">
                        <div className="text-lg font-bold text-red-600">{stats.failedExecutions}</div>
                        <div className="text-xs text-red-700">Failed</div>
                      </div>
                    </div>
                  </div>

                  {/* Workflow Performance */}
                  <div className="space-y-4">
                    <h3 className="font-medium flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Workflow Performance
                    </h3>

                    <ScrollArea className="h-[300px]">
                      <div className="space-y-2">
                        {workflows.map((workflow) => {
                          const workflowExecutions = executions.filter((e) => e.workflowId === workflow.id)
                          const successCount = workflowExecutions.filter((e) => e.status === "completed").length
                          const successRate =
                            workflowExecutions.length > 0
                              ? Math.round((successCount / workflowExecutions.length) * 100)
                              : 0

                          return (
                            <div key={workflow.id} className="p-3 border rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="font-medium text-sm">{workflow.name}</span>
                                <Badge variant={workflow.status === "active" ? "default" : "secondary"}>
                                  {workflow.status}
                                </Badge>
                              </div>
                              <div className="grid grid-cols-3 gap-2 text-xs">
                                <div>
                                  <span className="text-muted-foreground">Executions:</span>
                                  <div className="font-medium">{workflowExecutions.length}</div>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Success:</span>
                                  <div className="font-medium text-green-600">{successRate}%</div>
                                </div>
                                <div>
                                  <span className="text-muted-foreground">Nodes:</span>
                                  <div className="font-medium">{workflow.nodes.length}</div>
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                {/* Activity Timeline */}
                <div className="space-y-4">
                  <h3 className="font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Recent Activity
                  </h3>

                  <ScrollArea className="h-[200px]">
                    <div className="space-y-2">
                      {executions
                        .sort((a, b) => b.startedAt - a.startedAt)
                        .slice(0, 10)
                        .map((execution) => (
                          <div key={execution.id} className="flex items-center gap-3 p-2 border rounded">
                            <div
                              className={`w-2 h-2 rounded-full ${
                                execution.status === "completed"
                                  ? "bg-green-500"
                                  : execution.status === "failed"
                                    ? "bg-red-500"
                                    : execution.status === "running"
                                      ? "bg-blue-500"
                                      : "bg-yellow-500"
                              }`}
                            />
                            <div className="flex-1">
                              <div className="text-sm font-medium">{execution.workflowName}</div>
                              <div className="text-xs text-muted-foreground">
                                {new Date(execution.startedAt).toLocaleString()}
                              </div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {execution.status}
                            </Badge>
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </TabsContent>
          </div>
        </Tabs>
      </CardContent>
    </Card>
  )
}
