"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Download, FileText, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import type { Annotation } from "./pdf-annotations";

interface PDFAnnotationExportProps {
  annotations: Annotation[];
  numPages: number;
  documentTitle?: string;
}

type ExportFormat = "json" | "csv" | "markdown" | "html" | "pdf-comments";

export default function PDFAnnotationExport({
  annotations,
  numPages,
  documentTitle = "Document",
}: PDFAnnotationExportProps) {
  const [exportFormat, setExportFormat] = useState<ExportFormat>("json");
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [includeCoordinates, setIncludeCoordinates] = useState(false);
  const [filterByPage, setFilterByPage] = useState<number | null>(null);
  const [copied, setCopied] = useState(false);

  const filteredAnnotations = filterByPage
    ? annotations.filter((ann) => ann.pageNumber === filterByPage)
    : annotations;

  const exportToJSON = () => {
    const exportData = {
      document: documentTitle,
      exportDate: new Date().toISOString(),
      totalPages: numPages,
      totalAnnotations: filteredAnnotations.length,
      annotations: filteredAnnotations.map((ann) => ({
        id: ann.id,
        type: ann.type,
        pageNumber: ann.pageNumber,
        content: ann.content,
        color: ann.color,
        author: ann.author,
        timestamp: ann.timestamp,
        ...(includeCoordinates && {
          coordinates: {
            x: ann.x,
            y: ann.y,
            width: ann.width,
            height: ann.height,
            points: ann.points,
          },
        }),
        ...(includeMetadata && {
          metadata: {
            created: ann.timestamp ? new Date(ann.timestamp).toISOString() : new Date().toISOString(),
            formattedDate: ann.timestamp ? new Date(ann.timestamp).toLocaleString() : new Date().toLocaleString(),
          },
        }),
      })),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: "application/json",
    });
    downloadFile(blob, `${documentTitle}-annotations.json`);
  };

  const exportToCSV = () => {
    const headers = [
      "ID",
      "Type",
      "Page",
      "Content",
      "Color",
      "Author",
      "Date",
      ...(includeCoordinates ? ["X", "Y", "Width", "Height"] : []),
    ];

    const rows = filteredAnnotations.map((ann) => [
      ann.id,
      ann.type,
      ann.pageNumber.toString(),
      `"${(ann.content || "").replace(/"/g, '""')}"`,
      ann.color,
      ann.author,
      ann.timestamp ? new Date(ann.timestamp).toLocaleString() : new Date().toLocaleString(),
      ...(includeCoordinates
        ? [
            ann.x?.toString() || "",
            ann.y?.toString() || "",
            ann.width?.toString() || "",
            ann.height?.toString() || "",
          ]
        : []),
    ]);

    const csvContent = [
      headers.join(","),
      ...rows.map((row) => row.join(",")),
    ].join("\n");
    const blob = new Blob([csvContent], { type: "text/csv" });
    downloadFile(blob, `${documentTitle}-annotations.csv`);
  };

  const exportToMarkdown = () => {
    const content = `# ${documentTitle} - Annotations

**Export Date:** ${new Date().toLocaleString()}  
**Total Pages:** ${numPages}  
**Total Annotations:** ${filteredAnnotations.length}

---

${filteredAnnotations
  .map(
    (ann) => `
## ${ann.type.charAt(0).toUpperCase() + ann.type.slice(1)} - Page ${
      ann.pageNumber
    }

${ann.content ? `**Content:** ${ann.content}` : ""}
${ann.content ? "" : "*No text content*"}

- **Type:** ${ann.type}
- **Color:** ${ann.color}
- **Author:** ${ann.author}
- **Date:** ${ann.timestamp ? new Date(ann.timestamp).toLocaleString() : new Date().toLocaleString()}
${includeCoordinates ? `- **Position:** (${ann.x}, ${ann.y})` : ""}

---
`
  )
  .join("")}

*Exported from PDF Viewer*`;

    const blob = new Blob([content], { type: "text/markdown" });
    downloadFile(blob, `${documentTitle}-annotations.md`);
  };

  const exportToHTML = () => {
    const content = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${documentTitle} - Annotations</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { border-bottom: 2px solid #ccc; padding-bottom: 20px; margin-bottom: 30px; }
        .annotation { border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px; }
        .annotation-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .annotation-type { background: #f0f0f0; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
        .annotation-content { background: #f9f9f9; padding: 10px; border-radius: 4px; margin: 10px 0; }
        .metadata { font-size: 12px; color: #666; }
        .color-indicator { width: 20px; height: 20px; border-radius: 50%; display: inline-block; margin-right: 10px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>${documentTitle} - Annotations</h1>
        <p><strong>Export Date:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Total Pages:</strong> ${numPages} | <strong>Total Annotations:</strong> ${
      filteredAnnotations.length
    }</p>
    </div>

    ${filteredAnnotations
      .map(
        (ann) => `
    <div class="annotation">
        <div class="annotation-header">
            <div>
                <span class="color-indicator" style="background-color: ${
                  ann.color
                };"></span>
                <span class="annotation-type">${ann.type.toUpperCase()}</span>
                <strong>Page ${ann.pageNumber}</strong>
            </div>
            <div class="metadata">${ann.timestamp ? new Date(ann.timestamp).toLocaleString() : new Date().toLocaleString()}</div>
        </div>
        ${
          ann.content
            ? `<div class="annotation-content">${ann.content}</div>`
            : '<div class="annotation-content"><em>No text content</em></div>'
        }
        <div class="metadata">
            Author: ${ann.author} | Color: ${ann.color}
            ${includeCoordinates ? ` | Position: (${ann.x}, ${ann.y})` : ""}
        </div>
    </div>
    `
      )
      .join("")}

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; text-align: center; color: #666;">
        <p>Exported from PDF Viewer</p>
    </footer>
</body>
</html>`;

    const blob = new Blob([content], { type: "text/html" });
    downloadFile(blob, `${documentTitle}-annotations.html`);
  };

  const copyToClipboard = async () => {
    const summary = `${documentTitle} - Annotation Summary

Total Annotations: ${filteredAnnotations.length}
Export Date: ${new Date().toLocaleString()}

${filteredAnnotations
  .map(
    (ann) =>
      `• Page ${ann.pageNumber} (${ann.type}): ${ann.content || "No content"}`
  )
  .join("\n")}`;

    try {
      await navigator.clipboard.writeText(summary);
      setCopied(true);
      toast("Copied to clipboard", {
        description: "Annotation summary copied successfully",
      });
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error("Copy failed", {
        description: "Unable to copy to clipboard",
      });
    }
  };

  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);

    toast("Export successful", {
      description: `Annotations exported as ${filename}`,
    });
  };

  const handleExport = () => {
    switch (exportFormat) {
      case "json":
        exportToJSON();
        break;
      case "csv":
        exportToCSV();
        break;
      case "markdown":
        exportToMarkdown();
        break;
      case "html":
        exportToHTML();
        break;
      default:
        toast.error("Export format not supported", {
          description: "Please select a valid export format",
        });
    }
  };

  const getAnnotationsByType = () => {
    const types = filteredAnnotations.reduce((acc, ann) => {
      acc[ann.type] = (acc[ann.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return types;
  };

  const annotationTypes = getAnnotationsByType();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Download className="h-5 w-5" />
          Export Annotations
        </CardTitle>
        <CardDescription>
          Export your annotations in various formats for sharing or archival
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary */}
        <div className="grid grid-cols-2 gap-4 p-4 bg-muted/50 rounded-lg">
          <div>
            <div className="text-sm font-medium">Total Annotations</div>
            <div className="text-2xl font-bold">
              {filteredAnnotations.length}
            </div>
          </div>
          <div>
            <div className="text-sm font-medium">Pages with Annotations</div>
            <div className="text-2xl font-bold">
              {new Set(filteredAnnotations.map((ann) => ann.pageNumber)).size}
            </div>
          </div>
        </div>

        {/* Annotation Types */}
        {Object.keys(annotationTypes).length > 0 && (
          <div>
            <div className="text-sm font-medium mb-2">Annotation Types</div>
            <div className="flex flex-wrap gap-2">
              {Object.entries(annotationTypes).map(([type, count]) => (
                <Badge key={type} variant="secondary">
                  {type}: {count}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Export Options */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Export Format</label>
            <Select
              value={exportFormat}
              onValueChange={(value: ExportFormat) => setExportFormat(value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="json">JSON (Structured Data)</SelectItem>
                <SelectItem value="csv">CSV (Spreadsheet)</SelectItem>
                <SelectItem value="markdown">
                  Markdown (Documentation)
                </SelectItem>
                <SelectItem value="html">HTML (Web Page)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="text-sm font-medium">
              Filter by Page (Optional)
            </label>
            <Select
              value={filterByPage?.toString() || "all"}
              onValueChange={(value) =>
                setFilterByPage(value === "all" ? null : Number.parseInt(value))
              }
            >
              <SelectTrigger className="mt-1">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Pages</SelectItem>
                {Array.from({ length: numPages }, (_, i) => i + 1).map(
                  (page) => (
                    <SelectItem key={page} value={page.toString()}>
                      Page {page}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-metadata"
                checked={includeMetadata}
                onCheckedChange={(checked) =>
                  setIncludeMetadata(checked as boolean)
                }
              />
              <label htmlFor="include-metadata" className="text-sm">
                Include metadata (timestamps, author info)
              </label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="include-coordinates"
                checked={includeCoordinates}
                onCheckedChange={(checked) =>
                  setIncludeCoordinates(checked as boolean)
                }
              />
              <label htmlFor="include-coordinates" className="text-sm">
                Include position coordinates
              </label>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button onClick={handleExport} className="flex-1">
            <Download className="h-4 w-4 mr-2" />
            Export {exportFormat.toUpperCase()}
          </Button>

          <Button variant="outline" onClick={copyToClipboard}>
            {copied ? (
              <Check className="h-4 w-4" />
            ) : (
              <Copy className="h-4 w-4" />
            )}
          </Button>
        </div>

        {filteredAnnotations.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>No annotations to export</p>
            <p className="text-sm">Add some annotations first</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
