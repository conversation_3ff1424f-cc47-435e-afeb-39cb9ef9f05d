"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import {
  Pen,
  Brush,
  Eraser,
  Undo,
  Redo,
  Save,
  Upload,
  Trash2,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Layers,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { AnnotationStyle } from '@/lib/annotations/annotation-engine';

export interface DrawingTool {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  type: 'pen' | 'pencil' | 'brush' | 'marker' | 'eraser' | 'shape' | 'text';
  category: 'drawing' | 'shapes' | 'text' | 'editing';
  settings: {
    minSize: number;
    maxSize: number;
    defaultSize: number;
    hasOpacity: boolean;
    hasSmoothing: boolean;
    hasStabilization: boolean;
    supportsPressure: boolean;
  };
}

interface AdvancedDrawingToolsProps {
  selectedTool: string;
  onToolSelect: (toolId: string) => void;
  currentStyle: AnnotationStyle;
  onStyleChange: (style: Partial<AnnotationStyle>) => void;
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  onSave?: () => void;
  onLoad?: () => void;
  onClear?: () => void;
  showGrid?: boolean;
  onGridToggle?: (show: boolean) => void;
  showRulers?: boolean;
  onRulersToggle?: (show: boolean) => void;
  layers?: Array<{ id: string; name: string; visible: boolean; locked: boolean }>;
  activeLayer?: string;
  onLayerChange?: (layerId: string) => void;
  onLayerToggle?: (layerId: string, property: 'visible' | 'locked') => void;
  className?: string;
}

const DRAWING_TOOLS: DrawingTool[] = [
  {
    id: 'pen',
    name: 'Pen',
    icon: Pen,
    type: 'pen',
    category: 'drawing',
    settings: {
      minSize: 1,
      maxSize: 20,
      defaultSize: 2,
      hasOpacity: true,
      hasSmoothing: true,
      hasStabilization: true,
      supportsPressure: true,
    },
  },
  {
    id: 'pencil',
    name: 'Pencil',
    icon: Pen,
    type: 'pencil',
    category: 'drawing',
    settings: {
      minSize: 1,
      maxSize: 15,
      defaultSize: 3,
      hasOpacity: true,
      hasSmoothing: false,
      hasStabilization: false,
      supportsPressure: true,
    },
  },
  {
    id: 'brush',
    name: 'Brush',
    icon: Brush,
    type: 'brush',
    category: 'drawing',
    settings: {
      minSize: 5,
      maxSize: 50,
      defaultSize: 15,
      hasOpacity: true,
      hasSmoothing: true,
      hasStabilization: false,
      supportsPressure: true,
    },
  },
  {
    id: 'marker',
    name: 'Marker',
    icon: Pen,
    type: 'marker',
    category: 'drawing',
    settings: {
      minSize: 3,
      maxSize: 25,
      defaultSize: 8,
      hasOpacity: false,
      hasSmoothing: true,
      hasStabilization: false,
      supportsPressure: false,
    },
  },
  {
    id: 'eraser',
    name: 'Eraser',
    icon: Eraser,
    type: 'eraser',
    category: 'editing',
    settings: {
      minSize: 5,
      maxSize: 100,
      defaultSize: 20,
      hasOpacity: false,
      hasSmoothing: false,
      hasStabilization: false,
      supportsPressure: false,
    },
  },
];

const COLOR_PALETTE = [
  '#000000', '#FFFFFF', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
  '#800000', '#008000', '#000080', '#808000', '#800080', '#008080', '#C0C0C0', '#808080',
  '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F',
  '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA', '#F1948A', '#AED6F1', '#A9DFBF', '#F9E79F',
];

export default function AdvancedDrawingTools({
  selectedTool,
  onToolSelect,
  currentStyle,
  onStyleChange,
  onUndo,
  onRedo,
  canUndo = false,
  canRedo = false,
  onSave,
  onLoad,
  onClear,
  showGrid = false,
  onGridToggle,
  showRulers = false,
  onRulersToggle,
  layers = [],
  activeLayer,
  onLayerChange,
  onLayerToggle,
  className,
}: AdvancedDrawingToolsProps) {
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [toolSettings, setToolSettings] = useState({
    size: 5,
    opacity: 1,
    smoothing: 0.5,
    stabilization: 0.3,
  });

  const selectedToolData = DRAWING_TOOLS.find(tool => tool.id === selectedTool);

  // Update tool settings when tool changes
  useEffect(() => {
    if (selectedToolData) {
      setToolSettings(prev => ({
        ...prev,
        size: selectedToolData.settings.defaultSize,
      }));
    }
  }, [selectedToolData]);

  // Handle style changes
  const handleStyleChange = useCallback((changes: Partial<AnnotationStyle>) => {
    onStyleChange(changes);
  }, [onStyleChange]);

  // Handle tool size change
  const handleSizeChange = useCallback((value: number[]) => {
    const size = value[0];
    setToolSettings(prev => ({ ...prev, size }));
    handleStyleChange({
      stroke: {
        ...currentStyle.stroke,
        width: size,
      },
    });
  }, [currentStyle.stroke, handleStyleChange]);

  // Handle opacity change
  const handleOpacityChange = useCallback((value: number[]) => {
    const opacity = value[0] / 100;
    setToolSettings(prev => ({ ...prev, opacity }));
    handleStyleChange({
      effects: {
        ...currentStyle.effects,
        opacity,
      },
    });
  }, [currentStyle.effects, handleStyleChange]);

  // Handle color selection
  const handleColorSelect = useCallback((color: string) => {
    handleStyleChange({
      stroke: {
        ...currentStyle.stroke,
        color,
      },
      fill: {
        ...currentStyle.fill,
        color,
      },
    });
    setShowColorPicker(false);
  }, [currentStyle, handleStyleChange]);

  const renderToolButton = (tool: DrawingTool) => {
    const Icon = tool.icon;
    const isSelected = selectedTool === tool.id;

    return (
      <Button
        key={tool.id}
        variant={isSelected ? "default" : "ghost"}
        size="sm"
        onClick={() => onToolSelect(tool.id)}
        className={cn(
          "flex flex-col items-center gap-1 h-auto py-2",
          isSelected && "bg-primary text-primary-foreground"
        )}
        title={tool.name}
      >
        <Icon className="h-4 w-4" />
        <span className="text-xs">{tool.name}</span>
      </Button>
    );
  };

  const renderColorPalette = () => (
    <div className="grid grid-cols-8 gap-1 p-2">
      {COLOR_PALETTE.map(color => (
        <button
          key={color}
          className={cn(
            "w-6 h-6 rounded border-2 border-transparent hover:border-gray-400",
            currentStyle.stroke?.color === color && "border-gray-600"
          )}
          style={{ backgroundColor: color }}
          onClick={() => handleColorSelect(color)}
          title={color}
        />
      ))}
    </div>
  );

  const renderToolSettings = () => {
    if (!selectedToolData) return null;

    return (
      <div className="space-y-4">
        {/* Size Control */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-sm">Size</Label>
            <Badge variant="outline" className="text-xs">
              {toolSettings.size}px
            </Badge>
          </div>
          <Slider
            value={[toolSettings.size]}
            onValueChange={handleSizeChange}
            min={selectedToolData.settings.minSize}
            max={selectedToolData.settings.maxSize}
            step={1}
            className="w-full"
          />
        </div>

        {/* Opacity Control */}
        {selectedToolData.settings.hasOpacity && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm">Opacity</Label>
              <Badge variant="outline" className="text-xs">
                {Math.round(toolSettings.opacity * 100)}%
              </Badge>
            </div>
            <Slider
              value={[toolSettings.opacity * 100]}
              onValueChange={handleOpacityChange}
              min={10}
              max={100}
              step={5}
              className="w-full"
            />
          </div>
        )}

        {/* Smoothing Control */}
        {selectedToolData.settings.hasSmoothing && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm">Smoothing</Label>
              <Badge variant="outline" className="text-xs">
                {Math.round(toolSettings.smoothing * 100)}%
              </Badge>
            </div>
            <Slider
              value={[toolSettings.smoothing * 100]}
              onValueChange={(value) => setToolSettings(prev => ({ ...prev, smoothing: value[0] / 100 }))}
              min={0}
              max={100}
              step={10}
              className="w-full"
            />
          </div>
        )}

        {/* Stabilization Control */}
        {selectedToolData.settings.hasStabilization && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label className="text-sm">Stabilization</Label>
              <Badge variant="outline" className="text-xs">
                {Math.round(toolSettings.stabilization * 100)}%
              </Badge>
            </div>
            <Slider
              value={[toolSettings.stabilization * 100]}
              onValueChange={(value) => setToolSettings(prev => ({ ...prev, stabilization: value[0] / 100 }))}
              min={0}
              max={100}
              step={10}
              className="w-full"
            />
          </div>
        )}

        {/* Stroke Style */}
        <div className="space-y-2">
          <Label className="text-sm">Stroke Style</Label>
          <Select
            value={currentStyle.stroke?.style || 'solid'}
            onValueChange={(style) => handleStyleChange({
              stroke: { ...currentStyle.stroke, style: style as 'solid' | 'dashed' | 'dotted' }
            })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="solid">Solid</SelectItem>
              <SelectItem value="dashed">Dashed</SelectItem>
              <SelectItem value="dotted">Dotted</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    );
  };

  const renderLayerPanel = () => (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">Layers</Label>
        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
          <Layers className="h-3 w-3" />
        </Button>
      </div>
      <div className="space-y-1 max-h-32 overflow-y-auto">
        {layers.map(layer => (
          <div
            key={layer.id}
            className={cn(
              "flex items-center gap-2 p-2 rounded text-sm cursor-pointer hover:bg-muted",
              activeLayer === layer.id && "bg-muted"
            )}
            onClick={() => onLayerChange?.(layer.id)}
          >
            <Button
              size="sm"
              variant="ghost"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onLayerToggle?.(layer.id, 'visible');
              }}
            >
              {layer.visible ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="h-4 w-4 p-0"
              onClick={(e) => {
                e.stopPropagation();
                onLayerToggle?.(layer.id, 'locked');
              }}
            >
              {layer.locked ? <Lock className="h-3 w-3" /> : <Unlock className="h-3 w-3" />}
            </Button>
            <span className="flex-1 truncate">{layer.name}</span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Drawing Tools</CardTitle>
        <CardDescription>Advanced drawing and annotation tools</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs defaultValue="tools" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="tools">Tools</TabsTrigger>
            <TabsTrigger value="style">Style</TabsTrigger>
            <TabsTrigger value="layers">Layers</TabsTrigger>
          </TabsList>

          <TabsContent value="tools" className="space-y-4">
            {/* Tool Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Drawing Tools</Label>
              <div className="grid grid-cols-2 gap-2">
                {DRAWING_TOOLS.filter(tool => tool.category === 'drawing').map(renderToolButton)}
              </div>
            </div>

            <Separator />

            {/* Editing Tools */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Editing Tools</Label>
              <div className="grid grid-cols-2 gap-2">
                {DRAWING_TOOLS.filter(tool => tool.category === 'editing').map(renderToolButton)}
              </div>
            </div>

            <Separator />

            {/* Action Buttons */}
            <div className="grid grid-cols-2 gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onUndo}
                disabled={!canUndo}
                className="flex items-center gap-1"
              >
                <Undo className="h-3 w-3" />
                Undo
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onRedo}
                disabled={!canRedo}
                className="flex items-center gap-1"
              >
                <Redo className="h-3 w-3" />
                Redo
              </Button>
            </div>

            {/* Utility Buttons */}
            <div className="grid grid-cols-3 gap-2">
              {onSave && (
                <Button variant="outline" size="sm" onClick={onSave}>
                  <Save className="h-3 w-3" />
                </Button>
              )}
              {onLoad && (
                <Button variant="outline" size="sm" onClick={onLoad}>
                  <Upload className="h-3 w-3" />
                </Button>
              )}
              {onClear && (
                <Button variant="outline" size="sm" onClick={onClear}>
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="style" className="space-y-4">
            {/* Color Selection */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Color</Label>
                <div
                  className="w-6 h-6 rounded border-2 border-gray-300 cursor-pointer"
                  style={{ backgroundColor: currentStyle.stroke?.color || '#000000' }}
                  onClick={() => setShowColorPicker(!showColorPicker)}
                />
              </div>
              {showColorPicker && renderColorPalette()}
            </div>

            <Separator />

            {/* Tool Settings */}
            {renderToolSettings()}

            <Separator />

            {/* Canvas Options */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Canvas Options</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Show Grid</Label>
                  <Switch
                    checked={showGrid}
                    onCheckedChange={onGridToggle}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <Label className="text-sm">Show Rulers</Label>
                  <Switch
                    checked={showRulers}
                    onCheckedChange={onRulersToggle}
                  />
                </div>
              </div>
            </div>

            {/* Advanced Effects */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Effects</Label>
              <div className="space-y-2">
                <div className="space-y-1">
                  <Label className="text-xs">Shadow</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <Label className="text-xs text-muted-foreground">Blur</Label>
                      <Slider
                        value={[currentStyle.effects?.shadow?.blur || 0]}
                        onValueChange={(value) => handleStyleChange({
                          effects: {
                            ...currentStyle.effects,
                            shadow: {
                              ...currentStyle.effects?.shadow,
                              blur: value[0],
                              offsetX: currentStyle.effects?.shadow?.offsetX || 2,
                              offsetY: currentStyle.effects?.shadow?.offsetY || 2,
                              color: currentStyle.effects?.shadow?.color || '#00000040',
                            },
                          },
                        })}
                        min={0}
                        max={20}
                        step={1}
                        className="w-full"
                      />
                    </div>
                    <div>
                      <Label className="text-xs text-muted-foreground">Offset</Label>
                      <Slider
                        value={[currentStyle.effects?.shadow?.offsetX || 0]}
                        onValueChange={(value) => handleStyleChange({
                          effects: {
                            ...currentStyle.effects,
                            shadow: {
                              ...currentStyle.effects?.shadow,
                              offsetX: value[0],
                              offsetY: value[0],
                              blur: currentStyle.effects?.shadow?.blur || 0,
                              color: currentStyle.effects?.shadow?.color || '#00000040',
                            },
                          },
                        })}
                        min={0}
                        max={10}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="layers" className="space-y-4">
            {renderLayerPanel()}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
