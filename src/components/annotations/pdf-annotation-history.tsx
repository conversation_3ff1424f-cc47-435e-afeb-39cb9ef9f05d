"use client";

import { useState, useCallback } from "react";
import type { Annotation } from "./pdf-annotations";

interface AnnotationAction {
  type: "add" | "update" | "delete";
  annotation: Annotation;
  previousState?: Annotation;
  timestamp: number;
}

interface UseAnnotationHistoryProps {
  maxHistorySize?: number;
}

export function useAnnotationHistory({
  maxHistorySize = 50,
}: UseAnnotationHistoryProps = {}) {
  const [history, setHistory] = useState<AnnotationAction[]>([]);
  const [currentIndex, setCurrentIndex] = useState(-1);

  const addToHistory = useCallback(
    (action: Omit<AnnotationAction, "timestamp">) => {
      const newAction: AnnotationAction = {
        ...action,
        timestamp: Date.now(),
      };

      setHistory((prev) => {
        // Remove any actions after current index (when undoing then making new changes)
        const newHistory = prev.slice(0, currentIndex + 1);
        newHistory.push(newAction);

        // Limit history size
        if (newHistory.length > maxHistorySize) {
          newHistory.shift();
          setCurrentIndex((prev) => Math.max(-1, prev - 1));
        } else {
          setCurrentIndex(newHistory.length - 1);
        }

        return newHistory;
      });
    },
    [currentIndex, maxHistorySize]
  );

  const undo = useCallback((): AnnotationAction | null => {
    if (currentIndex >= 0) {
      const action = history[currentIndex];
      setCurrentIndex((prev) => prev - 1);
      return action;
    }
    return null;
  }, [history, currentIndex]);

  const redo = useCallback((): AnnotationAction | null => {
    if (currentIndex < history.length - 1) {
      const nextIndex = currentIndex + 1;
      const action = history[nextIndex];
      setCurrentIndex(nextIndex);
      return action;
    }
    return null;
  }, [history, currentIndex]);

  const canUndo = currentIndex >= 0;
  const canRedo = currentIndex < history.length - 1;

  const clearHistory = useCallback(() => {
    setHistory([]);
    setCurrentIndex(-1);
  }, []);

  return {
    addToHistory,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory,
    historySize: history.length,
    currentIndex,
  };
}
