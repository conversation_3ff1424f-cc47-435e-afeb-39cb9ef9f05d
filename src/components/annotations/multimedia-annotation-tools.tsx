"use client";

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Progress } from '@/components/ui/progress';
import {
  Image,
  Video,
  Music,
  FileText,
  Upload,
  Trash2,
  ExternalLink,
  Camera,
  Mic,
  Square,
  Circle,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { AdvancedAnnotation } from '@/lib/annotations/annotation-engine';

interface MultimediaFile {
  id: string;
  name: string;
  type: 'image' | 'video' | 'audio' | 'file';
  url: string;
  thumbnailUrl?: string;
  size: number;
  mimeType: string;
  duration?: number; // for video/audio
  dimensions?: { width: number; height: number };
  uploadedAt: Date;
  description?: string;
}

interface MultimediaAnnotationToolsProps {
  onMultimediaAdd: (annotation: Partial<AdvancedAnnotation>) => void;
  savedFiles?: MultimediaFile[];
  onFileSave?: (file: MultimediaFile) => void;
  onFileDelete?: (fileId: string) => void;
  maxFileSize?: number; // in MB
  allowedTypes?: string[];
  enableRecording?: boolean;
  className?: string;
}

const DEFAULT_ALLOWED_TYPES = [
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'video/mp4',
  'video/webm',
  'audio/mp3',
  'audio/wav',
  'audio/ogg',
  'application/pdf',
  'text/plain',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
];

export default function MultimediaAnnotationTools({
  onMultimediaAdd,
  savedFiles = [],
  onFileSave,
  onFileDelete,
  maxFileSize = 50, // 50MB default
  allowedTypes = DEFAULT_ALLOWED_TYPES,
  enableRecording = true,
  className,
}: MultimediaAnnotationToolsProps) {
  const [selectedFile, setSelectedFile] = useState<MultimediaFile | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [recordingType, setRecordingType] = useState<'audio' | 'video'>('audio');
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [showRecordDialog, setShowRecordDialog] = useState(false);
  const [fileDescription, setFileDescription] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);

  // Handle file selection
  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!allowedTypes.includes(file.type)) {
      toast.error('File type not supported');
      return;
    }

    // Validate file size
    if (file.size > maxFileSize * 1024 * 1024) {
      toast.error(`File size must be less than ${maxFileSize}MB`);
      return;
    }

    handleFileUpload(file);
  }, [allowedTypes, maxFileSize, handleFileUpload]);

  // Handle file upload
  const handleFileUpload = useCallback(async (file: File) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      // Create file URL (in real app, this would be uploaded to server)
      const url = URL.createObjectURL(file);
      
      // Get file type
      const type = file.type.startsWith('image/') ? 'image' :
                   file.type.startsWith('video/') ? 'video' :
                   file.type.startsWith('audio/') ? 'audio' : 'file';

      // Get dimensions for images/videos
      let dimensions: { width: number; height: number } | undefined;
      if (type === 'image') {
        dimensions = await getImageDimensions(url);
      } else if (type === 'video') {
        dimensions = await getVideoDimensions(url);
      }

      // Create multimedia file object
      const multimediaFile: MultimediaFile = {
        id: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: file.name,
        type,
        url,
        size: file.size,
        mimeType: file.type,
        dimensions,
        uploadedAt: new Date(),
        description: fileDescription,
      };

      // Generate thumbnail for videos
      if (type === 'video') {
        multimediaFile.thumbnailUrl = await generateVideoThumbnail(url);
      }

      clearInterval(progressInterval);
      setUploadProgress(100);

      // Save file
      onFileSave?.(multimediaFile);
      
      // Add as annotation
      onMultimediaAdd({
        type: 'image', // Will be handled as multimedia annotation
        multimedia: {
          type: multimediaFile.type,
          url: multimediaFile.url,
          thumbnailUrl: multimediaFile.thumbnailUrl,
          filename: multimediaFile.name,
          fileSize: multimediaFile.size,
          mimeType: multimediaFile.mimeType,
          duration: multimediaFile.duration,
          dimensions: multimediaFile.dimensions,
        },
        width: Math.min(dimensions?.width || 200, 300),
        height: Math.min(dimensions?.height || 150, 200),
        content: multimediaFile.description,
      });

      setShowUploadDialog(false);
      setFileDescription('');
      toast.success('File uploaded successfully');
    } catch {
      toast.error('Failed to upload file');
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [fileDescription, onFileSave, onMultimediaAdd, generateVideoThumbnail, getImageDimensions, getVideoDimensions]);

  // Start recording
  const startRecording = useCallback(async () => {
    try {
      const constraints = recordingType === 'video' 
        ? { video: true, audio: true }
        : { audio: true };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      recordedChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, {
          type: recordingType === 'video' ? 'video/webm' : 'audio/webm',
        });
        
        const file = new File([blob], `recording_${Date.now()}.webm`, {
          type: blob.type,
        });
        
        handleFileUpload(file);
        
        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      toast.success(`${recordingType} recording started`);
    } catch {
      toast.error('Failed to start recording');
    }
  }, [recordingType, handleFileUpload]);

  // Stop recording
  const stopRecording = useCallback(() => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
      toast.success('Recording stopped');
    }
  }, [isRecording]);

  // Get image dimensions
  const getImageDimensions = useCallback((url: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = () => {
        resolve({ width: 200, height: 150 });
      };
      img.src = url;
    });
  }, []);

  // Get video dimensions
  const getVideoDimensions = useCallback((url: string): Promise<{ width: number; height: number }> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      video.onloadedmetadata = () => {
        resolve({ width: video.videoWidth, height: video.videoHeight });
      };
      video.onerror = () => {
        resolve({ width: 320, height: 240 });
      };
      video.src = url;
    });
  }, []);

  // Generate video thumbnail
  const generateVideoThumbnail = useCallback((url: string): Promise<string> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.onloadeddata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        video.currentTime = 1; // Seek to 1 second
      };

      video.onseeked = () => {
        if (ctx) {
          ctx.drawImage(video, 0, 0);
          const thumbnail = canvas.toDataURL('image/jpeg', 0.8);
          resolve(thumbnail);
        } else {
          resolve('');
        }
      };

      video.onerror = () => {
        resolve('');
      };

      video.src = url;
    });
  }, []);

  // Handle file selection from saved files
  const handleSavedFileSelect = useCallback((file: MultimediaFile) => {
    setSelectedFile(file);
    
    onMultimediaAdd({
      type: 'image', // Will be handled as multimedia annotation
      multimedia: {
        type: file.type,
        url: file.url,
        thumbnailUrl: file.thumbnailUrl,
        filename: file.name,
        fileSize: file.size,
        mimeType: file.mimeType,
        duration: file.duration,
        dimensions: file.dimensions,
      },
      width: Math.min(file.dimensions?.width || 200, 300),
      height: Math.min(file.dimensions?.height || 150, 200),
      content: file.description,
    });
  }, [onMultimediaAdd]);

  // Render file preview
  const renderFilePreview = (file: MultimediaFile) => {
    const Icon = file.type === 'image' ? Image :
                file.type === 'video' ? Video :
                file.type === 'audio' ? Music : FileText;

    return (
      <div
        key={file.id}
        className={cn(
          "p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md",
          selectedFile?.id === file.id && "border-primary bg-primary/5"
        )}
        onClick={() => handleSavedFileSelect(file)}
      >
        <div className="flex items-center gap-2 mb-2">
          <Icon className="h-4 w-4" />
          <span className="text-sm font-medium truncate">{file.name}</span>
        </div>
        
        {/* Preview */}
        <div className="h-20 bg-muted rounded mb-2 overflow-hidden">
          {file.type === 'image' ? (
            <img
              src={file.url}
              alt={file.name}
              className="w-full h-full object-cover"
            />
          ) : file.type === 'video' ? (
            <img
              src={file.thumbnailUrl || file.url}
              alt={file.name}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Icon className="h-8 w-8 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* File info */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <Badge variant="outline">{file.type}</Badge>
            <span className="text-muted-foreground">
              {(file.size / 1024 / 1024).toFixed(1)}MB
            </span>
          </div>
          {file.dimensions && (
            <div className="text-xs text-muted-foreground">
              {file.dimensions.width} × {file.dimensions.height}
            </div>
          )}
          {file.description && (
            <div className="text-xs text-muted-foreground line-clamp-2">
              {file.description}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-1 mt-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              // Open file in new tab
              window.open(file.url, '_blank');
            }}
          >
            <ExternalLink className="h-3 w-3" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={(e) => {
              e.stopPropagation();
              onFileDelete?.(file.id);
            }}
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    );
  };

  return (
    <Card className={cn("w-80", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Multimedia Annotations</CardTitle>
        <CardDescription>Add images, videos, audio, and files to documents</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="upload">Upload</TabsTrigger>
            <TabsTrigger value="record">Record</TabsTrigger>
            <TabsTrigger value="library">Library</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            {/* Upload Section */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">Upload Files</Label>
                <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm" variant="outline">
                      <Upload className="h-3 w-3 mr-1" />
                      Upload
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Upload Multimedia File</DialogTitle>
                      <DialogDescription>
                        Upload images, videos, audio files, or documents
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label>File</Label>
                        <input
                          ref={fileInputRef}
                          type="file"
                          accept={allowedTypes.join(',')}
                          onChange={handleFileSelect}
                          className="w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                        />
                        <div className="text-xs text-muted-foreground">
                          Max size: {maxFileSize}MB
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <Label>Description (Optional)</Label>
                        <Textarea
                          value={fileDescription}
                          onChange={(e) => setFileDescription(e.target.value)}
                          placeholder="Add a description for this file..."
                          rows={3}
                        />
                      </div>

                      {isUploading && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Uploading...</span>
                            <span>{uploadProgress}%</span>
                          </div>
                          <Progress value={uploadProgress} className="h-2" />
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* Quick upload area */}
              <div
                className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center cursor-pointer hover:border-muted-foreground/50 transition-colors"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Click to upload or drag and drop
                </p>
                <p className="text-xs text-muted-foreground mt-1">
                  Images, videos, audio, documents
                </p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="record" className="space-y-4">
            {/* Recording Section */}
            {enableRecording && (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium">Record Media</Label>
                  <Dialog open={showRecordDialog} onOpenChange={setShowRecordDialog}>
                    <DialogTrigger asChild>
                      <Button size="sm" variant="outline">
                        <Mic className="h-3 w-3 mr-1" />
                        Record
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Record Audio/Video</DialogTitle>
                        <DialogDescription>
                          Record audio or video directly from your device
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label>Recording Type</Label>
                          <div className="flex gap-2">
                            <Button
                              variant={recordingType === 'audio' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => setRecordingType('audio')}
                            >
                              <Mic className="h-3 w-3 mr-1" />
                              Audio
                            </Button>
                            <Button
                              variant={recordingType === 'video' ? 'default' : 'outline'}
                              size="sm"
                              onClick={() => setRecordingType('video')}
                            >
                              <Camera className="h-3 w-3 mr-1" />
                              Video
                            </Button>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label>Description (Optional)</Label>
                          <Textarea
                            value={fileDescription}
                            onChange={(e) => setFileDescription(e.target.value)}
                            placeholder="Add a description for this recording..."
                            rows={3}
                          />
                        </div>

                        <div className="flex gap-2">
                          {!isRecording ? (
                            <Button onClick={startRecording} className="flex-1">
                              <Circle className="h-3 w-3 mr-1 fill-current" />
                              Start Recording
                            </Button>
                          ) : (
                            <Button onClick={stopRecording} variant="destructive" className="flex-1">
                              <Square className="h-3 w-3 mr-1 fill-current" />
                              Stop Recording
                            </Button>
                          )}
                        </div>

                        {isRecording && (
                          <div className="text-center">
                            <div className="animate-pulse text-red-500 font-medium">
                              Recording {recordingType}...
                            </div>
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>

                {/* Quick record buttons */}
                <div className="grid grid-cols-2 gap-2">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setRecordingType('audio');
                      setShowRecordDialog(true);
                    }}
                  >
                    <Mic className="h-4 w-4 mr-2" />
                    Audio
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => {
                      setRecordingType('video');
                      setShowRecordDialog(true);
                    }}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Video
                  </Button>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="library" className="space-y-4">
            {/* File Library */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label className="text-sm font-medium">File Library</Label>
                <Badge variant="outline">{savedFiles.length} files</Badge>
              </div>

              <ScrollArea className="h-64">
                <div className="grid gap-2">
                  {savedFiles.map(renderFilePreview)}
                  {savedFiles.length === 0 && (
                    <div className="text-center py-8 text-muted-foreground">
                      <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No files uploaded</p>
                      <p className="text-xs">Upload files to see them here</p>
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
