/* Mobile & Cross-Platform Optimizations */

/* Touch Target Sizes */
.touch-target {
  min-height: var(--touch-target-min, 44px);
  min-width: var(--touch-target-min, 44px);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.touch-target-comfortable {
  min-height: var(--touch-target-comfortable, 48px);
  min-width: var(--touch-target-comfortable, 48px);
}

.touch-target-large {
  min-height: 56px;
  min-width: 56px;
}

/* Touch Device Optimizations */
.touch-device .touch-target {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

.touch-device .touch-target:active {
  transform: scale(0.95);
  opacity: 0.8;
}

/* Safe Area Support */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

.safe-area-inset {
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* Mobile Spacing */
.mobile-spacing {
  --spacing: var(--mobile-spacing, 16px);
}

.tablet-spacing {
  --spacing: var(--tablet-spacing, 24px);
}

.mobile-device {
  --base-spacing: var(--mobile-spacing, 16px);
  --touch-spacing: 8px;
}

.tablet-device {
  --base-spacing: var(--tablet-spacing, 24px);
  --touch-spacing: 12px;
}

/* Scroll Optimizations */
.momentum-scroll {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
}

.overscroll-behavior-contain {
  overscroll-behavior: contain;
}

.scroll-smooth-disabled {
  scroll-behavior: auto !important;
}

/* Gesture Prevention */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.no-drag {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.no-callout {
  -webkit-touch-callout: none;
}

.no-highlight {
  -webkit-tap-highlight-color: transparent;
}

/* Touch Gestures */
.touch-none {
  touch-action: none;
}

.touch-pan-x {
  touch-action: pan-x;
}

.touch-pan-y {
  touch-action: pan-y;
}

.touch-manipulation {
  touch-action: manipulation;
}

/* Hardware Acceleration */
.hardware-accelerated {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
}

.gpu-accelerated {
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
}

/* Orientation Specific Styles */
.portrait {
  /* Portrait-specific styles */
}

.landscape {
  /* Landscape-specific styles */
}

@media (orientation: portrait) {
  .orientation-portrait-only {
    display: block;
  }
  
  .orientation-landscape-only {
    display: none;
  }
}

@media (orientation: landscape) {
  .orientation-portrait-only {
    display: none;
  }
  
  .orientation-landscape-only {
    display: block;
  }
}

/* Device-Specific Optimizations */
@media (max-width: 767px) {
  .mobile-device {
    /* Mobile-specific optimizations */
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full-width {
    width: 100% !important;
  }
  
  .mobile-text-center {
    text-align: center !important;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .tablet-device {
    /* Tablet-specific optimizations */
  }
  
  .tablet-hidden {
    display: none !important;
  }
}

@media (min-width: 1024px) {
  .desktop-only {
    display: block;
  }
  
  .mobile-only,
  .tablet-only {
    display: none !important;
  }
}

/* High DPI / Retina Display Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .retina-optimized {
    /* High DPI optimizations */
  }
}

/* Hover Support Detection */
@media (hover: hover) {
  .hover-supported:hover {
    /* Hover effects for devices that support it */
  }
}

@media (hover: none) {
  .no-hover {
    /* Styles for touch-only devices */
  }
  
  .hover-supported:hover {
    /* Disable hover effects on touch devices */
    transform: none !important;
    opacity: 1 !important;
  }
}

/* Pointer Support Detection */
@media (pointer: coarse) {
  .coarse-pointer {
    /* Optimizations for touch/coarse pointers */
  }
  
  .touch-target {
    min-height: 48px;
    min-width: 48px;
  }
}

@media (pointer: fine) {
  .fine-pointer {
    /* Optimizations for mouse/fine pointers */
  }
}

/* Accessibility Enhancements */
.high-contrast {
  filter: contrast(150%);
}

.large-text {
  font-size: 1.2em;
  line-height: 1.6;
}

.large-text .touch-target {
  min-height: 56px;
  min-width: 56px;
}

@media (prefers-reduced-motion: reduce) {
  .reduced-motion,
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

@media (prefers-color-scheme: dark) {
  .auto-dark-mode {
    /* Automatic dark mode styles */
  }
}

/* iOS Specific Optimizations */
@supports (-webkit-touch-callout: none) {
  .ios-device {
    /* iOS-specific styles */
  }
  
  .ios-device input,
  .ios-device textarea,
  .ios-device select {
    font-size: 16px; /* Prevent zoom */
    border-radius: 0; /* Remove default styling */
  }
  
  .ios-device .touch-target {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  }
}

/* Android Specific Optimizations */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .android-device {
    /* Android-specific styles */
  }
}

/* Performance Optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-scroll {
  will-change: scroll-position;
}

.contain-layout {
  contain: layout;
}

.contain-paint {
  contain: paint;
}

.contain-strict {
  contain: strict;
}

/* Smooth Scrolling with Reduced Motion Support */
.smooth-scroll {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  .smooth-scroll {
    scroll-behavior: auto;
  }
}

/* Focus Management for Touch Devices */
.touch-device .focus-visible:focus {
  outline: 2px solid var(--focus-color, #007AFF);
  outline-offset: 2px;
}

.touch-device .focus-visible:focus:not(:focus-visible) {
  outline: none;
}

/* Virtual Keyboard Support */
@media (max-height: 500px) and (orientation: landscape) {
  .virtual-keyboard-open {
    /* Styles when virtual keyboard is open */
  }
}

/* Notch Support (iPhone X and newer) */
@supports (padding: max(0px)) {
  .notch-support {
    padding-left: max(16px, env(safe-area-inset-left));
    padding-right: max(16px, env(safe-area-inset-right));
  }
}

/* Foldable Device Support */
@media (spanning: single-fold-vertical) {
  .foldable-vertical {
    /* Styles for vertical fold */
  }
}

@media (spanning: single-fold-horizontal) {
  .foldable-horizontal {
    /* Styles for horizontal fold */
  }
}

/* Print Optimizations */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  .touch-target {
    min-height: auto;
    min-width: auto;
  }
}

/* Utility Classes */
.full-viewport-height {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
}

.full-viewport-width {
  width: 100vw;
  width: 100dvw; /* Dynamic viewport width for mobile */
}

.mobile-container {
  max-width: 100%;
  padding-left: var(--mobile-spacing, 16px);
  padding-right: var(--mobile-spacing, 16px);
}

.tablet-container {
  max-width: 100%;
  padding-left: var(--tablet-spacing, 24px);
  padding-right: var(--tablet-spacing, 24px);
}

/* Animation Utilities */
.fade-in-mobile {
  animation: fadeInMobile 0.3s ease-out;
}

.slide-up-mobile {
  animation: slideUpMobile 0.3s ease-out;
}

.scale-in-mobile {
  animation: scaleInMobile 0.2s ease-out;
}

@keyframes fadeInMobile {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUpMobile {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleInMobile {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* Reduced motion variants */
@media (prefers-reduced-motion: reduce) {
  .fade-in-mobile,
  .slide-up-mobile,
  .scale-in-mobile {
    animation: none;
  }
}
