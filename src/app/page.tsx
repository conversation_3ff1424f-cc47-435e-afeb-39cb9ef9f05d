"use client"

import dynamic from 'next/dynamic'

// Dynamically import the PDF viewer to avoid SSR issues
const MultiDocumentPDFViewer = dynamic(
  () => import("@/components/core/multi-document-pdf-viewer"),
  {
    ssr: false,
    loading: () => <div className="flex items-center justify-center h-screen">Loading PDF Viewer...</div>
  }
)

export default function Home() {
  return (
    <MultiDocumentPDFViewer
      maxDocuments={8} // Increased for better desktop support
      showUploadOnEmpty={true}
      enableLayoutOptimization={true}
      defaultLayoutPreferences={{
        defaultLayoutMode: 'single',
        enableMultiDocument: true,
        sidebarDefaultMode: 'compact',
        headerMode: 'compact',
        tabDisplayMode: 'compact',
        compactSpacing: false, // Better spacing for desktop
        enableResponsiveLayout: true,
        enableAutoLayout: true,
        maxDocuments: 8,
        maxVisibleTabs: 10,
        showBorders: true,
        enableDensityControl: true
      }}
    />
  )
}
